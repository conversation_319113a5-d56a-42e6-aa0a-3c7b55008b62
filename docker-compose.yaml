services:
  app-prod:
    image: biela-ai:production
    build:
      context: .
      dockerfile: Dockerfile
      target: biela-ai-production
    ports:
      - "5173:5173"
    env_file: ".env.local"
    environment:
      - NODE_ENV=production
      - COMPOSE_PROFILES=production
      # No strictly needed but serving as hints for Coolify
      - PORT=5173
      - VITE_LOG_LEVEL=${VITE_LOG_LEVEL:-debug}
      - RUNNING_IN_DOCKER=true
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: pnpm run dockerstart
    profiles:
      - production

  app-dev:
    image: biela-ai:development
    build:
      target: biela-ai-development
    environment:
      - NODE_ENV=development
      - VITE_HMR_PROTOCOL=ws
      - VITE_HMR_HOST=localhost
      - VITE_HMR_PORT=5173
      - CHOKIDAR_USEPOLLING=false
      - WATCHPACK_POLLING=false
      - PORT=5173
      - VITE_LOG_LEVEL=${VITE_LOG_LEVEL:-debug}
      - RUNNING_IN_DOCKER=true
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - .:/app:delegated
      - /app/node_modules
    ports:
      - "5173:5173"
    command: pnpm run dev --host 0.0.0.0
    profiles: [ "development", "default" ]
