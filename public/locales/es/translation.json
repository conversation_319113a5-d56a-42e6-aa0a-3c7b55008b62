{"whatWouldYouLikeToBuild": "Únete ahora para asegurar beneficios exclusivos antes del lanzamiento oficial.", "whatWouldYouLikeToBuildSubtitle": "<PERSON> puedes <1>imaginarlo</1>, puedes <5><PERSON><PERSON><PERSON></5>.", "fromIdeaToDeployment": "Empieza gratis. Programa cualquier cosa. Convierte tus habilidades en oportunidades con cada solicitud.", "codePlaceholder": "Si lo puedes imaginar, BIELA puede codificarlo. ¿Qué haremos hoy?", "defaultPlaceholder": "¿Cómo puedo ayudarte hoy? Hagamos algo asombroso juntos", "checkingFeatures": "Verificando las funciones", "checklists": "Listas de verificación", "runUnitTestsSuggestionTitle": "Sugerencia", "runUnitTestsSuggestionMessage": "¿Te gustaría ejecutar pruebas unitarias para tu proyecto?", "runUnitTestsPrimaryButton": "Ejecutar pruebas unitarias", "runUnitTestsSecondaryButton": "Descar<PERSON>", "createDatabaseTitle": "Creación de base de datos", "createDatabaseMessage": "¿Te gustaría crear una base de datos para tu proyecto?", "createDatabasePrimaryButton": "Crear base de datos", "createDatabaseSecondaryButton": "Descar<PERSON>", "extendedThinking": "Pensamiento extendido", "extendedThinkingTooltip": "Permitir que la IA piense más profundamente antes de responder", "firstResponseOnly": "Solo primera respuesta", "always": "Siempre", "attachFile": "Adjuntar archivo", "voiceInput": "Entrada de voz", "selectLanguage": "Seleccionar idioma para la entrada de voz", "languageSelectionDisabled": "Selección de idioma deshabilitada durante la grabación", "notAvailableInFirefox": "Esta función no está disponible en Firefox, pero sí en Chrome y Safari", "enhancePrompt": "Mejorar indicación", "cleanUpProject": "Limpiar proyecto", "showPrompt": "Mostrar aviso", "hidePrompt": "Ocultar aviso", "sendButton": "Enviar", "abortButton": "<PERSON><PERSON><PERSON>", "inspirationTitle": "¿Necesitas inspiración? Prueba uno de estos:", "cleanUpPrompt": "Limpia el proyecto asegurándote de que ningún archivo individual exceda las 300 líneas de código. Refactoriza los archivos grandes en componentes modulares más pequeños, manteniendo la funcionalidad completa. Identifica y elimina todos los archivos, có<PERSON>s, componentes y datos redundantes que ya no sean necesarios. Asegúrate de que todos los componentes permanezcan correctamente conectados y operativos, evitando cualquier interrupción en el sistema existente. Mantén la integridad del código verificando que ningún cambio introduzca errores o rompa las funciones actuales. El objetivo es optimizar el proyecto en términos de eficiencia, mantenibilidad y claridad.", "checklistPrompt": "Revisa mi aviso inicial, comprende el objetivo paso a paso y crea para mí una lista de verificación con una marca verde para lo que se ha completado y una marca roja para lo que aún falta por hacer.", "personalPortfolioIdea": "Crea un sitio web de portafolio personal con un tema oscuro", "recipeFinderIdea": "Construye una aplicación de búsqueda de recetas que sugiera comidas basadas en los ingredientes", "weatherDashboardIdea": "Diseña un panel del clima con fondos animados", "habitTrackerIdea": "Desarrolla un rastreador de hábitos con visualización del progreso", "loading": "Cargando", "error": "Error", "succes": "¡Éxito!", "tryAgain": "Intentar de nuevo", "dashboard": "<PERSON><PERSON>", "getStartedTitle": "Comenzar", "getStartedSub": "Descubre cómo funciona Biela.dev", "createProject": "Crear nuevo proyecto", "createProjectSub": "Empieza desde cero", "uploadProject": "Subir proyecto", "uploadProjectSub": "Importa un proyecto existente", "importChat": "Importar chat", "importChatSub": "Importar un chat existente", "createFolder": "<PERSON><PERSON><PERSON> una nueva carpeta", "createFolderSub": "Organiza tus proyectos", "editProjectName": "Editar nombre del proyecto", "editName": "Editar nombre", "cancel": "<PERSON><PERSON><PERSON>", "changeFolder": "Cambiar <PERSON>a", "save": "Guardar", "importing": "Importando...", "importFolder": "Importar carpeta", "giveTitle": "Asigna un título", "projects": "Proyectos", "searchProjects": "Buscar proyectos...", "becomeAffiliate": "Conviértete en afiliado", "exclusiveGrowth": "Beneficios exclusivos de crecimiento", "lifetimeEarnings": "Ganancias de por vida", "highCommissions": "Altas comisiones", "earnCommission": "Gana un 50% de comisión en tu primera venta", "joinAffiliateProgram": "Únete a nuestro programa de afiliados", "folders": "Carpetas", "organizeProjects": "Organiza tus proyectos por categoría", "createNewFolder": "<PERSON><PERSON><PERSON> una nueva carpeta", "enterFolderName": "Ingresa el nombre de la carpeta", "editFolder": "<PERSON><PERSON>", "deleteFolder": "Eliminar carpeta", "all": "Todo", "webProjects": "Proyectos web", "mobilepps": "Aplicaciones móviles", "developmentComparison": "Comparación de desarrollo", "traditionalVsAI": "Tradicional vs. IA", "traditional": "Tradicional", "standardApproach": "En<PERSON>que <PERSON>", "developmentCost": "Costo de desarrollo", "developmentTime": "Tiempo de desarrollo", "costSavings": "Ahorro en costos", "reducedCosts": "Costos reducidos", "timeSaved": "Tiempo <PERSON>", "fasterDelivery": "Entrega más rápida", "bielaDevAI": "Biela.dev IA", "nextGenDevelopment": "Desarrollo de próxima generación", "developmentCosts": "Costos de desarrollo", "openInGitHub": "Abrir en GitHub", "downloadProject": "Des<PERSON><PERSON> proyecto", "duplicateProject": "Duplicar proyecto", "openProject": "<PERSON><PERSON>r proyecto", "deleteProject": "Eliminar proyecto", "confirmDelete": "¿Eliminar esta carpeta?", "invoicePreview": "<PERSON>", "settings": {"title": "Configuraciones", "deployment": {"AdvancedSettings": {"advanced-settings": "Configuraciones avanzadas", "configure-advanced-deployment-options": "Configura opciones avanzadas de despliegue", "server-configuration": "Configuración del servidor", "memory-limit": "Límite de memoria", "region": "Región", "security-settings": "Configuraciones de seguridad", "enable-ddos-protection": "Habilitar protección DDoS", "protect-against-distributed": "Proteger contra ataques distribuidos de denegación de servicio", "ip-whitelisting": "Lista blanca de IP", "restrict-acces-to": "Restringir el acceso a direcciones IP específicas", "deployment-options": "Opciones de despliegue", "auto-deploy": "Despliegue automático", "automatically-deploy-when": "Desplegar automáticamente al hacer push en la rama principal", "preview-deployments": "Ver despliegues en vista previa", "create-preview-deployments": "<PERSON><PERSON><PERSON> despliegues de vista previa para pull requests"}, "BuildSettings": {"build-and-deployment-settings": "Configuraciones de compilación y despliegue", "build-command": "Comando de compilación", "override": "Sobrescribir", "output-directory": "Directorio de salida", "override2": "Sobrescribir"}, "DatabaseConfiguration": {"database-configuration": "Configuración de base de datos", "configure-your-db-connections": "Configura tus conexiones y ajustes de base de datos", "database-type": "Tipo de base de datos", "connection-string": "Cadena de conexión", "your-db-credentials": "Tus credenciales de base de datos están cifradas y se almacenan de forma segura", "database-settings": "Ajustes de base de datos", "pool-size": "Tamaño del pool", "require": "<PERSON><PERSON><PERSON>", "prefer": "Preferir", "disable": "Desactivar", "add-database": "Agregar base de datos"}, "DomainSettings": {"domain-settings": "Configuración de dominios", "configure-your-custom-domain": "Configura tus dominios personalizados y certificados SSL", "custom-domain": "<PERSON>inio personaliza<PERSON>", "add": "Agregar", "ssl-certificate": "Certificado SSL", "auto-renew-ssl-certificates": "Renovar certificados SSL automáticamente", "auto-renew-before-expiry": "Renovar certificados SSL automáticamente antes de su expiración", "force-https": "Forzar HTTPS", "redirect-all-http-traffic": "Redirigir todo el tráfico HTTP a HTTPS", "active-domain": "Dominios activos", "remove": "Eliminar"}, "EnvironmentVariables": {"environment-variables": "Variables de entorno", "configure-environment-variables": "Configura las variables de entorno para tus despliegues", "all-enviroments": "Todos los entornos", "environment-variables2": "Variables de entorno", "preview": "Vista previa", "development": "Desarrollo", "create-new": "<PERSON><PERSON><PERSON> nuevo", "key": "Clave", "value": "Valor", "save-variable": "Guardar variable", "you-can-also-import": "También puedes importar variables desde un archivo .env:", "import-env-file": "Importar archivo .env"}, "ProjectConfiguration": {"project-configuration": "Configuración del proyecto", "config-your-project-settings": "Configura los ajustes de tu proyecto y opciones de despliegue", "project-url": "URL del proyecto", "framework": "Framework", "repo": "Repositorio", "branch": "<PERSON>", "main": "principal", "development": "desarrollo", "staging": "staging"}}, "identity": {"unverified": {"title": "Verificación de Identidad", "description": "Verifica tu identidad para usar Biela.dev", "subtitle": "Proceso de Verificación Seguro", "processServers": "Toda la información de la tarjeta se almacena de forma segura en los servidores de Stripe, no en los servidores de Biela", "processCharge": "Tu tarjeta no será cargada sin tu consentimiento explícito para una suscripción", "processBenefits": "Biela.dev es completamente gratuito para cuentas verificadas hasta el 15 de mayo de 2025", "verifyStripe": "Verificar con tarjeta de crédito o débito", "verifyStripeDescription": "Conecta tu método de pago para la verificación", "verifyNow": "Verificar Ahora"}, "verified": {"title": "Identidad Verificada", "description": "Tu identidad ha sido verificada con éxito", "paymentMethod": "Mé<PERSON><PERSON>", "cardEnding": "Tarjeta terminada en", "updatePayment": "<PERSON><PERSON><PERSON><PERSON>", "untilDate": "Hasta el 15 de mayo de 2025", "freeAccess": "Acceso <PERSON>", "freeAccessDescription": "Disfruta de acceso completo a Biela.dev sin costo", "secureStorage": "Almacenamiento Seguro", "secureStorageDescription": "La información de tu tarjeta se almacena de forma segura en los servidores de Stripe, no en los servidores de Biela. Tu tarjeta no se cargará sin tu consentimiento explícito para una suscripción.", "subscriptionAvailable": "Las suscripciones estarán disponibles a partir del 15 de mayo de 2025."}, "connectingToStripe": "Conectando con <PERSON>e..."}, "tabs": {"billing": "Facturación", "profile": "Perfil", "deployment": "<PERSON><PERSON><PERSON><PERSON>", "identity": "Identidad"}}, "help": {"title": "¿Cómo podemos ayudarte?", "searchPlaceholder": "Buscar en la documentación...", "categories": {"getting-started": {"title": "Primeros pasos", "description": "Aprende lo básico para usar Biela.dev", "articles": ["Guía de inicio rápido", "Visión general de la plataforma", "Creación de tu primer proyecto", "Comprendiendo el desarrollo con IA"]}, "ai-development": {"title": "Desarrollo con IA", "description": "Domina el desarrollo potenciado por IA", "articles": ["Escribir indicaciones efectivas", "Mejores prácticas para la generación de código", "Consejos para depurar la IA", "Características avanzadas de IA"]}, "project-management": {"title": "Gestión de proyectos", "description": "Organiza y administra tus proyectos", "articles": ["Estructura del proyecto", "Colaboración en equipo", "Control de versiones", "Opciones de despliegue"]}}, "channels": {"docs": {"name": "Documentación", "description": "Guías completas y referencias de API"}, "community": {"name": "Comunidad", "description": "Conéctate con otros desarrolladores"}, "github": {"name": "GitHub", "description": "Reporta problemas y contribuye"}}, "support": {"title": "¿Aún necesitas ayuda?", "description": "Nuestro equipo de soporte está disponible 24/7 para ayudarte con cualquier pregunta o problema que tengas.", "button": "<PERSON>ar so<PERSON>e"}}, "getStarted": {"title": "Descubre cómo funciona Biela.dev", "description": "Crea tu aplicación en minutos con Biela.dev. Nuestra IA automatiza todo el proceso de desarrollo, desde la configuración hasta el despliegue. ¡Así es como nuestra IA construye tu app sin esfuerzo!", "features": {"docs": {"title": "Documentación para desarrolladores", "description": "Aprende a usar Biela.dev con guías fáciles de seguir, consejos y las mejores prácticas. ¡Perfecto para principiantes y desarrolladores experimentados!", "cta": "Explorar documentación para desarrolladores"}, "support": {"title": "Feedback y soporte", "description": "¿Necesitas ayuda o tienes algún comentario? Contacta al soporte y ayuda a mejorar Biela.dev.", "cta": "Enviar feedback"}, "platform": {"title": "Características de la plataforma", "description": "Descubre las poderosas herramientas que ofrece Biela.dev para ayudarte a crear sitios web y apps sin esfuerzo. ¡Deja que la IA se encargue del código por ti!", "cta": "Explorar caracterís<PERSON>s"}}, "video": {"title": "Video de inicio rápido", "description": "Mira cómo Biela.dev construye para ti: ¡creación de apps y web sin esfuerzo!", "cta": "Ver tutorial"}, "guide": {"title": "Guía de inicio rápido", "steps": {"setup": {"title": "Configura tu proyecto al instante", "description": "Prepara tu entorno de desarrollo en segundos"}, "generate": {"title": "Genera código full-stack con IA", "description": "Deja que la IA escriba código listo para producción por ti"}, "features": {"title": "Generación instantánea de funciones", "description": "Agrega funciones complejas con indicaciones simples"}, "editor": {"title": "Editor sin código y de bajo código", "description": "Modifica tu app visualmente o a través del código"}, "optimize": {"title": "Optimiza y prueba en tiempo real", "description": "Asegúrate de que tu app funcione a la perfección"}, "deploy": {"title": "Despliega con un clic", "description": "Pon tu app en línea al instante con el despliegue automático"}}}, "faq": {"title": "Preguntas frecuentes", "questions": {"what": {"question": "¿Qué es Biela.dev?", "answer": "Biela.dev es una plataforma potenciada por IA que te ayuda a crear sitios web y apps, incluso si no sabes programar. Automatiza todo el proceso de desarrollo, desde la creación de código hasta el despliegue."}, "experience": {"question": "¿Necesito experiencia en programación para usar Biela.dev?", "answer": "¡No! Biela.dev está diseñado tanto para principiantes como para desarrolladores experimentados. Puedes construir con la ayuda de la IA o personalizar el código generado según sea necesario."}, "projects": {"question": "¿Qué tipos de proyectos puedo crear?", "answer": "Puedes crear sitios web, aplicaciones web, apps móviles, plataformas SaaS, tiendas de comercio electrónico, paneles de administración y mucho más."}, "edit": {"question": "¿Puedo editar el código generado por Biela.dev?", "answer": "¡Sí! Biela.dev te permite personalizar el código generado por la IA o utilizar el editor sin código/low-code para hacer modificaciones fácilmente."}, "deployment": {"question": "¿Cómo funciona el despliegue?", "answer": "Con un solo clic, Biela.dev despliega tu proyecto, poniéndolo en línea y listo para usar. ¡No se requiere configuración manual del servidor!"}, "pricing": {"question": "¿Biela.dev es gratis?", "answer": "Biela.dev ofrece un plan gratuito con funciones básicas. Para obtener herramientas y recursos más avanzados, puedes actualizar a un plan premium."}, "integrations": {"question": "¿Puedo integrar herramientas o bases de datos de terceros?", "answer": "¡Sí! Biela.dev es compatible con integraciones de bases de datos populares (MongoDB, Firebase, Supabase) y APIs de terceros."}, "help": {"question": "Si necesito a<PERSON>, ¿dónde puedo obtenerla?", "answer": "Puedes consultar nuestra documentación para desarrolladores, la guía de inicio rápido o contactar al soporte a través de nuestro centro de ayuda."}}}, "cta": {"title": "¿Listo para comenzar?", "description": "Crea tu primer proyecto con Biela.dev y experimenta el futuro del desarrollo.", "button": "Crear nuevo proyecto"}}, "confirmDeleteProject": "Confirmar eliminación del proyecto", "confirmRemoveFromFolder": "Confirmar elimina<PERSON> de <PERSON>", "deleteProjectWarning": "¿Está seguro de que desea eliminar permanentemente {{projectName}}?", "removeFromFolderWarning": "¿Está seguro de que desea eliminar {{projectName}} de {{folderName}}?", "confirm": "Confirmar", "confirmDeleteFolder": "Confirmar elimina<PERSON> de <PERSON>", "deleteFolderWarning": "¿Está seguro de que desea eliminar {{folderName}}? Esta acción no se puede deshacer.", "folderDeletedSuccessfully": "Carpeta eliminada con éxito", "downloadChat": "<PERSON><PERSON><PERSON> chat", "inactiveTitle": "Esta pestaña está inactiva", "inactiveDescription": "Haz clic en el botón de abajo para activar esta pestaña y continuar usando la aplicación.", "inactiveButton": "Usar esta pestaña", "suggestions": {"weatherDashboard": "Crea un panel meteorológico", "ecommercePlatform": "Construye una plataforma de comercio electrónico", "socialMediaApp": "Diseña una aplicación de redes sociales", "portfolioWebsite": "Genera un sitio web como portafolio", "taskManagementApp": "Crea una aplicación de gestión de tareas", "fitnessTracker": "Construye un rastreador de fitness", "recipeSharingPlatform": "Diseña una plataforma para compartir recetas", "travelBookingSite": "Crea un sitio de reservas de viajes", "learningPlatform": "Desarrolla una plataforma de aprendizaje", "musicStreamingApp": "Diseña una aplicación de streaming musical", "realEstateListing": "Crea un listado inmobiliario", "jobBoard": "Constituye una bolsa de trabajo"}, "pleaseWait": "Por favor espera...", "projectsInAll": "Proyectos en Todo", "projectsInCurrentFolder": "Proyectos en {{folderName}}", "createYourFirstProject": "Crea tu primer proyecto", "startCreateNewProjectDescription": "Comienza a construir algo increíble. Tus proyectos se mostrarán aquí una vez que los crees.", "createProjectBtn": "Nuevo Proyecto", "publishedToContest": "Publicado en el concurso", "publishToContest": "Publicar en el concurso", "refreshSubmission": "Actualizar envío", "contestInformation": "Información del concurso", "selectFolder": "Se<PERSON>ccionar una carpeta", "folder": "Carpeta", "selectAFolder": "Selecciona una carpeta", "thisProject": "este proyecto", "projectDeletedSuccessfully": "Proyecto eliminado con éxito", "projectRemovedFromFolder": "Proyecto eliminado de la <PERSON>a", "unnamedProject": "Proyecto sin nombre", "permanentDeletion": "Eliminación permanente", "removeFromFolder": "Eliminar de {{folderName}}", "verifiedAccount": "Cuenta verificada", "hasSubmittedAMinimumOfOneProject": "Ha enviado al menos un proyecto", "haveAtLeastActiveReferrals": "Tener al menos {{number}} referencias activas", "GoToAffiliateDashBoard": "<PERSON>r al panel de afiliados", "LikeOneProjectThatBelongsToAnotherUser": "Le gustó un proyecto que pertenece a otro usuario", "GoToContestPage": "Ir a la página del concurso", "ContestStatus": "Estado del concurso", "ShowcaseYourBestWork": "Muestra tu mejor trabajo", "submissions": "Env<PERSON><PERSON>", "qualifyConditions": "Condiciones de calificación", "completed": "Completado", "collapseQualifyConditions": "Colapsar condiciones de calificación", "expandQualifyConditions": "Expandir condiciones de calificación", "basicParticipation": "Participación básica", "complete": "Completo", "incomplete": "Incompleto", "forTop3Places": "Para los 3 primeros lugares", "deleteSubmission": "Eliminar envío", "view": "<PERSON>er", "submitMoreProjectsToIncreaseYourChangesOfWining": "¡Envía más proyectos para aumentar tus posibilidades de ganar!", "removeFromContest": "¿Eliminar del concurso?", "removeFromContestDescription": "¿Estás seguro de que deseas eliminar <project>{{project}}</project> del concurso? Esta acción no se puede deshacer.", "cardVerificationRequired": "Verificación de tarjeta requerida", "pleaseVerifyCard": "Por favor verifica tu tarjeta para continuar", "unlockFeaturesMessage": "Para desbloquear el acceso completo a todas las funciones de la plataforma, requerimos una verificación rápida de tarjeta. Este proceso es completamente seguro y garantiza una experiencia fluida en Biela.dev. No se realizarán cargos a su tarjeta durante la verificación.", "freeVerificationNotice": "Beneficios de Verificación", "accessToAllFeatures": "Acceso completo a las funciones de Biela.dev.", "enhancedFunctionality": "Rendimiento mejorado de la plataforma.", "quickSecureVerification": "Proceso de verificación seguro y cifrado.", "noCharges": "*Sin cargos ni tarifas ocultas", "verificationOnly": " — solo verificación.", "verifyNow": "Verificar ahora", "DropFilesToUpload": "Suelta los archivos para subirlos", "projectInfo": {"information": "Información del Proyecto", "type": "Tipo de Proyecto", "complexity": "Complejidad", "components": "Componentes", "features": "Características", "confidenceScore": "Puntaje de Confianza", "estimationUncertainty": "Incertidumbre en la Estimación", "keyTechnologies": "Tecnologías Clave"}, "projectMetrics": {"teamComposition": "Composición del Equipo", "hoursBreakdown": "<PERSON><PERSON><PERSON>", "timeToMarket": "Tiempo para el Mercado", "maintenance": "Mantenimiento", "aiPowered": "Impulsado por IA", "developerLevel": "Nivel del Desarrollador", "nextGenAI": "IA de Próxima Generación", "keyBenefits": "<PERSON><PERSON><PERSON><PERSON>", "instantDevelopment": "Desarrollo Instantáneo", "noMaintenanceCosts": "Sin Costos de Mantenimiento", "highConfidence": "Alta Confianza", "productionReadyCode": "Código Listo para Producción", "immediate": "Inmediato", "uncertainty": "Incertidumbre", "minimal": "<PERSON><PERSON><PERSON>"}, "loadingMessages": {"publish": ["Obteniendo información del proyecto...", "Haciendo una captura de pantalla...", "Generando resumen y descripción..."], "refresh": ["Recuperando los datos de la presentación actual...", "Actualizando la captura de pantalla del proyecto...", "Refrescando los detalles del proyecto..."]}, "errorMessages": {"publish": "No se pudo publicar \"{{projectName}}\" al concurso. Hubo un problema procesando su solicitud.", "refresh": "No se pudo actualizar la presentación de \"{{projectName}}\". El servidor no pudo actualizar la información de su proyecto."}, "actions": {"refresh": {"title": "Actualizando la Presentación", "successMessage": "Su presentación del proyecto ha sido actualizada exitosamente.", "buttonText": "Ver presentación actualizada"}, "publish": {"title": "Publicando al Concurso", "successMessage": "Su proyecto ha sido enviado al concurso.", "buttonText": "Ver en la Página del Concurso"}}, "SupabaseConnect": "Supabase", "SupabaseDashboard": "Supabase", "RestoreApp": "Restaurar", "SaveApp": "Guardar", "ForkChat": "Bifurcar", "BielaTerminal": "Terminal Biela", "UnitTesting": "Pruebas unitarias", "InstallDependencies": "Instalar dependencias", "InstallDependenciesDescription": "Instala todos los paquetes necesarios para el proyecto usando", "BuildProject": "Compilar proyecto", "BuildProjectDescription": "Compila y optimiza el proyecto para producción usando", "StartDevelopment": "Iniciar desar<PERSON>o", "StartDevelopmentDescription": "Inicia el servidor de desarrollo para vista previa en vivo usando", "NoProjectFound": "No se encontró ningún proyecto con ese nombre.", "NoProjectFoundDescription": " Por favor, revisa si hay errores tipográficos e inténtalo de nuevo.", "ContextLimitReached": "Se alcanzó el límite de contexto", "ClaudeContextDescription1": "Has alcanzado la capacidad de contexto para este proyecto con Claude. No te preocupes: podemos continuar cambiando a un modelo Gemini con una ventana de contexto mucho más amplia.", "ClaudeContextDescription2": "Los modelos Gemini ofrecen hasta 5 veces más capacidad de contexto, lo que te permite mantener todo tu código, historial de chat y continuar desarrollando tu proyecto sin interrupciones.", "SelectModelToContinue": "Selecciona un modelo para continuar:", "Performance": "Rendimiento", "AlternativeLimitExplanation": "Parece que la IA ha alcanzado el límite de procesamiento para este proyecto. La mayor parte del espacio está siendo utilizada por los archivos importados, no por el chat en sí.", "SuggestedSolutions": "Soluciones sugeridas:", "ReimportProject": "Reimportar como un nuevo proyecto", "ReimportProjectDescription": "Esto borrará el historial del chat y liberará espacio de contexto, manteniendo tus archivos.", "BreakIntoProjects": "Dividir en múltiples proyectos", "BreakIntoProjectsDescription": "Divide tu trabajo en componentes más pequeños que puedan desarrollarse por separado.", "ExportWork": "Exportar trabajo completado", "ExportWorkDescription": "Descarga y archiva los archivos terminados para liberar espacio de contexto.", "AlternativeContextNote": "Para aprovechar al máximo tu contexto disponible, considera eliminar archivos o bibliotecas no utilizadas y céntrate en los archivos clave necesarios para esta fase del desarrollo.", "ContinueWithSelectedModel": "Continuar con el modelo seleccionado", "Close": "<PERSON><PERSON><PERSON>", "AIModel": "Modelo de IA", "Active": "Activo", "Stats": "Estadísticas", "Cost": "Costo", "ExtendedThinkingDisabledForModel": "No disponible con este modelo", "ExtendedThinkingAlwaysOn": "Siempre activo con este modelo", "limitReached": "¡Has alcanzado tu límite!", "deleteProjectsSupabase": "Elimina algunos proyectos o aumenta tu límite en Supabase.", "goTo": "Ir a", "clickProjectSettings": " haz clic en el proyecto, Configuración del proyecto, desplázate hacia abajo y haz clic", "delete": "Eliminar", "retrying": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "retryConnection": "Reintentar conexión", "RegisterPageTitle": "Registrarse – biela.dev", "RegisterPageDescription": "Crea tu cuenta en biela.dev y desbloquea todas las funciones.", "SignUpHeading": "Regístrate", "AlreadyLoggedInRedirectHome": "¡Ya has iniciado sesión! Redirigiendo a la página principal...", "PasswordsMismatch": "Las contraseñas no coinciden.", "FirstNameRequired": "El nombre es obligatorio", "LastNameRequired": "El apellido es obligatorio", "UsernameRequired": "El nombre de usuario es obligatorio", "EmailRequired": "El correo electrónico es obligatorio", "EmailInvalid": "Por favor, introduce una dirección de correo válida", "TooManyRequests": "<PERSON><PERSON><PERSON><PERSON> solicitudes, por favor intenta más tarde", "SomethingWentWrongMessage": "<PERSON>go salió mal, por favor intenta más tarde", "PasswordRequired": "La contraseña es obligatoria", "ConfirmPasswordRequired": "Por favor, confirma tu contraseña", "AcceptTermsRequired": "Debes aceptar los Términos de servicio y la Política de privacidad", "CaptchaRequired": "Por favor, completa el CAPTCHA", "RegistrationFailed": "El registro ha fallado", "EmailConfirmationSent": "¡Se ha enviado un correo de confirmación! Por favor, confirma tu correo y luego inicia sesión.", "RegistrationServerError": "El registro ha fallado (el servidor devolvió falso).", "SomethingWentWrong": "Algo salió mal", "CheckEmailHeading": "Revisa tu correo electrónico para confirmar tu registro", "CheckEmailDescription": "Te hemos enviado un correo con un enlace de confirmación.", "GoToHomepage": "Ir a la página principal", "ReferralCodeOptional": "Código de referido (opcional)", "EnterReferralCode": "Ingresa un código de referido si fuiste invitado por un usuario existente.", "PasswordPlaceholder": "Contraseña", "ConfirmPasswordPlaceholder": "Confirmar con<PERSON>", "CreateAccount": "<PERSON><PERSON><PERSON> cuenta", "AlreadyHaveAccountPrompt": "¿Ya tienes una cuenta?", "Login": "In<PERSON><PERSON>", "AcceptTermsPrefix": "Estoy de acuerdo con los", "TermsOfService": "Términos de servicio", "AndSeparator": "y", "PrivacyPolicy": "Política de privacidad", "LoginPageTitle": "Iniciar se<PERSON> – biela.dev", "LoginPageDescription": "Accede a tu cuenta o inicia sesión en biela.dev para comenzar a usar todas las funciones.", "LogInHeading": "In<PERSON><PERSON>", "EmailOrUsernamePlaceholder": "Correo electrónico / Nombre de usuario", "ForgotPassword?": "¿Olvidaste tu contraseña?", "LoginToProfile": "Inicia sesión en tu perfil", "UserNotConfirmed": "Cuenta de usuario no confirmada", "ConfirmEmailNotice": "Debes confirmar tu dirección de correo electrónico para activar tu cuenta.", "ResendConfirmationEmail": "Reenviar correo de confirmación", "ResendConfirmationSuccess": "¡El correo de verificación ha sido reenviado! Por favor, revisa tu bandeja de entrada.", "ResendConfirmationError": "Error al reenviar el correo de confirmación.", "LoginSuccess": "¡Inicio de sesión exitoso! Redirigiendo...", "LoginFailed": "Error al iniciar sesión", "LoginWithGoogle": "Iniciar se<PERSON><PERSON> con Google", "LoginWithGitHub": "Iniciar se<PERSON><PERSON> con GitHub", "SignUpWithGoogle": "Registrarse con Google", "SignUpWithGitHub": "Registrarse con GitHub", "Or": "O", "NoAccountPrompt": "¿No tienes una cuenta?", "SignMeUp": "Regístrame", "ForgotPasswordPageTitle": "¿Olvidaste tu contraseña? – biela.dev", "ForgotPasswordPageDescription": "Restablece la contraseña de tu cuenta de biela.dev y recupera el acceso.", "BackToLogin": "Volver al inicio de sesión", "ForgotPasswordHeading": "¿Olvidaste tu contraseña?", "ForgotPasswordDescription": "Introduce tu dirección de correo electrónico y te enviaremos un enlace de verificación para restablecer tu contraseña.", "VerificationLinkSent": "¡Enlace de verificación enviado! Por favor, revisa tu correo.", "EnterYourEmailPlaceholder": "Introduce tu correo electrónico", "Sending": "Enviando...", "SendVerificationCode": "Enviar código de verificación", "InvalidConfirmationLink": "Enlace de confirmación inválido", "Back": "Volver", "ResetPassword": "Restablecer contraseña", "ResetPasswordDescription": "Crea una nueva contraseña para tu cuenta", "NewPasswordPlaceholder": "Nueva contraseña", "ConfirmNewPasswordPlaceholder": "Confirmar nueva contraseña", "ResetPasswordButton": "Restablecer contraseña", "PasswordRequirements": "La contraseña debe tener al menos 8 caracteres e incluir una letra mayúscula, una letra minúscula, un número y un carácter especial.", "PasswordUpdatedSuccess": "¡Contraseña actualizada con éxito!", "affiliateDashboard": "Panel de afiliados", "userDashboard": "Panel de usuario", "returnToAffiliateDashboard": "Volver al panel de afiliados", "returnToUserDashboard": "Volver al panel de usuario", "myProfile": "Mi perfil", "viewAndEditYourProfile": "Ver y editar tu perfil", "billing": "Facturación", "manageYourBillingInformation": "Gestionar tu información de facturación", "logout": "<PERSON><PERSON><PERSON>", "logoutDescription": "<PERSON><PERSON><PERSON> sesi<PERSON> de tu cuenta", "SupabaseNotAvailable": "Supabase no está disponible en este momento, por favor inténtelo de nuevo más tarde.", "projectActions": {"invalidSlug": "Slug de proyecto no válido.", "downloadSuccess": "¡Proyecto descargado con éxito!", "downloadError": "No se pudo descargar el proyecto.", "exportSuccess": "¡Chat exportado! Revisa tu carpeta de descargas.", "exportError": "No se pudo exportar el chat.", "duplicateSuccess": "¡Chat duplicado con éxito!", "duplicateError": "No se pudo duplicar el chat."}, "enter_new_phone_number": "Ingresa nuevo número de teléfono", "enter_new_phone_number_below": "Por favor, ingresa tu nuevo número de teléfono abajo:", "new_phone_placeholder": "Nuevo número de teléfono", "enter_otp_code": "Ingresar código OTP", "confirm_phone_message": "Para usar tu cuenta, necesitas confirmar tu número de teléfono. Ingresa el código enviado a ({{phone}}).", "wrong_phone": "¿Número incorrecto?", "resend_sms": "Reenviar SMS", "submit": "Enviar", "tokensAvailable": "tokens disponibles", "sectionTitle": "Gestión de Dominios", "addDomainButton": "Agregar Nombre de Dominio", "connectCustomDomainTitle": "Conectar un Nombre de Dominio Personalizado", "disclaimer": "Aviso legal:", "disclaimerText": "Para verificar correctamente, debes configurar correctamente todas las reglas DNS anteriores", "domainInputDescription": "Ingresa el nombre del dominio que deseas conectar a este proyecto.", "domainLabel": "Nombre de Dominio", "domainPlaceholder": "example.com", "cancelButton": "<PERSON><PERSON><PERSON>", "continueButton": "Agregar Nombre de Dominio", "deployingText": "Desplegando...", "addingText": "Agregando...", "verifyButtonText": "Verificar", "configureDnsTitle": "Configurar Registros DNS", "configureDnsDescription": "Agrega los siguientes registros DNS a tu dominio para verificar la propiedad y conectarlo a este proyecto.", "tableHeaderType": "Tipo", "tableHeaderName": "Nombre", "tableHeaderValue": "Valor", "note": "Nota:", "noteText": "Los cambios en DNS pueden tardar hasta 48 horas en propagarse. Sin embargo, a menudo surten efecto en pocos minutos o unas pocas horas.", "backButton": "Atrás", "showDnsButton": "Mostrar Configuración DNS", "hideDnsButton": "Ocultar Configuración DNS", "removeButton": "Eliminar", "dnsSettingsTitle": "Configuración DNS del Dominio", "removeDomainConfirmTitle": "Eliminar Nombre de Dominio", "removeConfirmationText": "¿Estás seguro de que deseas eliminar el nombre de dominio ", "importantCleanupTitle": "Limpieza Importante de DNS", "cleanupDescription": "Después de eliminar este nombre de dominio de tu proyecto, recuerda eliminar también los registros DNS creados durante la configuración. Esto ayuda a mantener una configuración DNS limpia y evita conflictos potenciales en el futuro.", "confirmRemoveButton": "Eliminar Nombre de Dominio", "customConfigTitle": "Configuración de Dominio Personalizado", "customConfigDescription": "Conecta tus propios nombres de dominio a este proyecto. Tu proyecto siempre será accesible a través del dominio predeterminado de Biela, pero los dominios personalizados brindan una experiencia de marca profesional para tus usuarios.", "defaultLabel": "Predeterminado", "statusActive": "Activo", "statusPending": "Pendiente", "lastVerifiedText": "Verificado hace un momento", "errorInvalidDomain": "Por favor ingresa un nombre de dominio válido (por ejemplo, example.com)", "errorDuplicateDomain": "Este nombre de dominio ya está conectado a tu proyecto", "errorAddFail": "No se pudo agregar el nombre de dominio.", "successAdd": "¡Nombre de dominio agregado exitosamente! Tu dominio ha sido agregado a este proyecto.", "benefitsTitle": "Beneficios del Dominio", "benefitSecurityTitle": "<PERSON><PERSON><PERSON><PERSON>", "benefitSecurityDesc": "Todos los dominios personalizados están asegurados automáticamente con certificados SSL.", "benefitPerformanceTitle": "Alto Rendimiento", "benefitPerformanceDesc": "La CDN global asegura que tu proyecto cargue rápidamente para los usuarios de todo el mundo.", "benefitBrandingTitle": "Marca Profesional", "benefitBrandingDesc": "Usa tu propio dominio para una experiencia de marca consistente.", "benefitAnalyticsTitle": "Integración con Analíticas", "benefitAnalyticsDesc": "Los dominios personalizados funcionan perfectamente con las plataformas de análisis.", "meta": {"index": {"title": "biela.dev | Constructor de Web y Apps con IA – Construye con Indicaciones", "description": "Transforma tus ideas en sitios web o aplicaciones en vivo con biela.dev. Utiliza indicaciones impulsadas por IA para crear productos digitales personalizados sin esfuerzo."}, "login": {"title": "Inicia Sesión en Tu Cuenta de biela.dev", "description": "Accede a tu panel de biela.dev para gestionar y construir tus proyectos generados por IA."}, "register": {"title": "Regístrate en biela.dev – Comienza a Construir con IA", "description": "Crea tu cuenta de biela.dev para empezar a construir sitios web y aplicaciones utilizando indicaciones potenciadas por IA."}, "dashboard": {"title": "Tu Panel de Proyectos – biela.dev", "description": "Gestiona tus sitios web y aplicaciones construidos con IA, edita proyectos en vivo y haz seguimiento de tu historial de construcción, todo en un solo lugar."}, "profile": {"title": "Tu perfil – biela.dev", "description": "Consulta y actualiza los detalles de tu cuenta en biela.dev, gestiona tus preferencias y personaliza tu experiencia con la IA."}, "billing": {"title": "Facturación – biela.dev", "description": "Agrega tu tarjeta de crédito para verificar tu identidad y desbloquear el acceso completo a las funciones de biela.dev — no se realizará ningún cargo."}}, "transferProject": "Compartir una copia", "transferSecurityNoteDescription": "El destinatario recibirá acceso completo a una copia de este proyecto y todos sus recursos asociados. Tú seguirás teniendo acceso al proyecto original.", "transferProjectDescription": "Introduce el nombre de usuario o el correo electrónico de la persona a la que deseas transferir una copia de este proyecto.", "transferProjectLabel": "Nombre de usuario o correo electrónico", "transferProjectPlaceholder": "<NAME_EMAIL>", "transferButton": "Transferir", "transferSecurityNote": "Nota de seguridad:", "dontHavePermisionToTransfer": "No tienes permiso para transferir este proyecto", "transferProjectUserNotFound": "¡Usuario {{ user }} no encontrado!", "transferErrorOwnAccount": "No puedes transferir un proyecto a tu propia cuenta.", "transferError": "Error al transferir el proyecto", "transferSuccess": "Transferido con éxito a {{ user }}", "enterValidEmailUsername": "Por favor, ingrese un nombre de usuario o correo electrónico", "enterMinValidEmailUsername": "Por favor, ingrese un nombre de usuario válido (mínimo 3 caracteres) o una dirección de correo electrónico", "youWillStillHaveAccess": "Todavía tendrás acceso al proyecto original", "newChangesWillNotAffect": "Los nuevos cambios no afectarán al proyecto del otro usuario"}