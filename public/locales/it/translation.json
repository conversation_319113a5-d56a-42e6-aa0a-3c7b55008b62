{"whatWouldYouLikeToBuild": "Trasforma la tua idea in un sito web o app live in pochi minuti", "whatWouldYouLikeToBuildSubtitle": "Se puoi <1>immaginare</1> qualcosa, puoi <5>program<PERSON><PERSON></5>.", "fromIdeaToDeployment": "Inizia gratis. Programma qualsiasi cosa. Trasforma le tue competenze in opportunità con ogni prompt.", "codePlaceholder": "Se riesci a immaginarlo, BIELA può codificarlo, cosa facciamo oggi?", "defaultPlaceholder": "Come posso aiutarti oggi? Facciamo qualcosa di straordinario insieme", "checkingFeatures": "Verifica delle funzionalità", "checklists": "Liste di controllo", "runUnitTestsSuggestionTitle": "Suggerimento", "runUnitTestsSuggestionMessage": "Vuoi eseguire i test unitari per il tuo progetto?", "runUnitTestsPrimaryButton": "Esegui test unitari", "runUnitTestsSecondaryButton": "<PERSON><PERSON><PERSON>", "createDatabaseTitle": "Creazione del database", "createDatabaseMessage": "Vuoi creare un database per il tuo progetto?", "createDatabasePrimaryButton": "Crea database", "createDatabaseSecondaryButton": "<PERSON><PERSON><PERSON>", "extendedThinking": "Pensiero esteso", "extendedThinkingTooltip": "Permetti all'IA di pensare più profondamente prima di rispondere", "firstResponseOnly": "Solo prima risposta", "always": "Sempre", "attachFile": "Allega file", "voiceInput": "Input vocale", "selectLanguage": "Seleziona la lingua per l'input vocale", "languageSelectionDisabled": "Selezione della lingua disabilitata durante la registrazione", "notAvailableInFirefox": "Questa funzione non è disponibile in Firefox, ma lo è in Chrome e Safari", "enhancePrompt": "<PERSON><PERSON><PERSON> su<PERSON>o", "cleanUpProject": "<PERSON><PERSON><PERSON><PERSON> pro<PERSON>", "showPrompt": "<PERSON>ra prompt", "hidePrompt": "Nascondi prompt", "sendButton": "Invia", "abortButton": "<PERSON><PERSON><PERSON>", "inspirationTitle": "Hai bisogno di ispirazione? Prova uno di questi:", "cleanUpPrompt": "Pulisci il progetto assicurandoti che nessun file superi le 300 righe di codice. Rifattorizza i file grandi in componenti modulari più piccoli mantenendo la funzionalità completa. Identifica ed elimina tutti i file, il codice, i componenti e i dati ridondanti che non sono più necessari. Assicurati che tutti i componenti rimangano correttamente collegati e funzionanti, evitando interruzioni nel sistema esistente. Mantieni l'integrità del codice verificando che nessuna modifica introduca errori o rompa le funzionalità attuali. L'obiettivo è ottimizzare il progetto in termini di efficienza, manutenibilità e chiarezza.", "checklistPrompt": "Esamina il mio prompt iniziale, comprendi l'obiettivo punto per punto e crea per me una lista di controllo con una spunta verde per tutto ciò che è stato fatto e una spunta rossa per ciò che manca da fare.", "personalPortfolioIdea": "Crea un sito web portfolio personale con un tema scuro", "recipeFinderIdea": "Crea un'app per trovare ricette che suggerisca piatti in base agli ingredienti", "weatherDashboardIdea": "Progetta un cruscotto meteo con sfondi animati", "habitTrackerIdea": "Sviluppa un tracker per le abitudini con visualizzazione dei progressi", "loading": "Caricamento", "error": "Errore", "succes": "Successo!", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "dashboard": "Dashboard", "getStartedTitle": "Inizia", "getStartedSub": "Scopri come funziona Biela.dev", "createProject": "Crea nuovo progetto", "createProjectSub": "Inizia da zero", "editProjectName": "Modifica nome progetto", "editName": "Modifica nome", "uploadProject": "<PERSON>ica progetto", "uploadProjectSub": "Importa un progetto esistente", "importChat": "Importa chat", "importChatSub": "Importa una chat esistente", "createFolder": "Crea una nuova cartella", "createFolderSub": "Organizza i tuoi progetti", "cancel": "<PERSON><PERSON><PERSON>", "changeFolder": "<PERSON><PERSON> cartella", "save": "<PERSON><PERSON>", "importing": "Importazione in corso...", "importFolder": "<PERSON><PERSON><PERSON> cartella", "giveTitle": "Assegna un titolo", "projects": "<PERSON><PERSON><PERSON>", "searchProjects": "Cerca progetti...", "becomeAffiliate": "<PERSON><PERSON><PERSON> affiliato", "exclusiveGrowth": "Vantaggi esclusivi per la crescita", "lifetimeEarnings": "Guadagni a vita", "highCommissions": "Commissioni elevate", "earnCommission": "Guadagna il 50% di commissione sulla tua prima vendita", "joinAffiliateProgram": "Unisciti al nostro programma di affiliazione", "folders": "<PERSON><PERSON><PERSON>", "organizeProjects": "Organizza i tuoi progetti per categoria", "createNewFolder": "Crea una nuova cartella", "enterFolderName": "Inserisci il nome della cartella", "editFolder": "Modifica cartella", "deleteFolder": "<PERSON><PERSON> cartella", "all": "<PERSON><PERSON>", "webProjects": "Progetti web", "mobilepps": "App mobili", "developmentComparison": "Confronto di sviluppo", "traditionalVsAI": "Tradizionale vs IA", "traditional": "Tradizionale", "standardApproach": "Approccio standard", "developmentCost": "Costo di sviluppo", "developmentTime": "Tempo di sviluppo", "costSavings": "Risparmio sui costi", "reducedCosts": "<PERSON><PERSON><PERSON>", "timeSaved": "Tempo rispar<PERSON>to", "fasterDelivery": "Consegna più rapida", "bielaDevAI": "Biela.dev IA", "nextGenDevelopment": "Sviluppo di nuova generazione", "developmentCosts": "Costi di sviluppo", "openInGitHub": "<PERSON><PERSON> su GitHub", "downloadProject": "Scarica progetto", "duplicateProject": "Duplica progetto", "openProject": "<PERSON><PERSON> progetto", "deleteProject": "Elimina progetto", "confirmDelete": "Eliminare questa cartella?", "invoicePreview": "La tua fattura", "settings": {"title": "Impostazioni", "deployment": {"AdvancedSettings": {"advanced-settings": "Impostazioni avanzate", "configure-advanced-deployment-options": "Configura opzioni di deployment avanzate", "server-configuration": "Configurazione del server", "memory-limit": "Limite di memoria", "region": "Regione", "security-settings": "Impostazioni di sicurezza", "enable-ddos-protection": "Abilita la protezione DDoS", "protect-against-distributed": "Proteggi contro attacchi distribuiti di tipo denial-of-service", "ip-whitelisting": "Whitelist IP", "restrict-acces-to": "Limita l'accesso a indirizzi IP specifici", "deployment-options": "Opzioni di deployment", "auto-deploy": "Deploy automatico", "automatically-deploy-when": "Esegui il deploy automaticamente quando viene fatto un push sul branch principale", "preview-deployments": "Visualizza i deployment in anteprima", "create-preview-deployments": "Crea deployment in anteprima per le pull request"}, "BuildSettings": {"build-and-deployment-settings": "Impostazioni di build e deployment", "build-command": "Comando di build", "override": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "output-directory": "Directory di output", "override2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "DatabaseConfiguration": {"database-configuration": "Configurazione del database", "configure-your-db-connections": "Configura le connessioni e le impostazioni del tuo database", "database-type": "Tipo di database", "connection-string": "Stringa di connessione", "your-db-credentials": "Le tue credenziali del database sono criptate e conservate in sicurezza", "database-settings": "Impostazioni del database", "pool-size": "Dimensione del pool", "require": "<PERSON><PERSON>", "prefer": "<PERSON><PERSON><PERSON><PERSON>", "disable": "Disabilita", "add-database": "Aggiungi database"}, "DomainSettings": {"domain-settings": "Impostazioni del dominio", "configure-your-custom-domain": "Configura i tuoi domini personalizzati e i certificati SSL", "custom-domain": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ssl-certificate": "Certificato SSL", "auto-renew-ssl-certificates": "Rinnova automaticamente i certificati SSL", "auto-renew-before-expiry": "Rinnova automaticamente i certificati SSL prima della scadenza", "force-https": "Forza HTTPS", "redirect-all-http-traffic": "Reindirizza tutto il traffico HTTP a HTTPS", "active-domain": "<PERSON><PERSON> attivi", "remove": "<PERSON><PERSON><PERSON><PERSON>"}, "EnvironmentVariables": {"environment-variables": "Variabili d'ambiente", "configure-environment-variables": "Configura le variabili d'ambiente per i tuoi deployment", "all-enviroments": "<PERSON><PERSON> gli <PERSON>", "environment-variables2": "Variabili d'ambiente", "preview": "Anteprima", "development": "<PERSON><PERSON><PERSON><PERSON>", "create-new": "Crea nuovo", "key": "Chiave", "value": "Valore", "save-variable": "Salva variabile", "you-can-also-import": "Puoi anche importare variabili da un file .env:", "import-env-file": "Importa file .env"}, "ProjectConfiguration": {"project-configuration": "Configurazione del progetto", "config-your-project-settings": "Configura le impostazioni del tuo progetto e le opzioni di deployment", "project-url": "URL del progetto", "framework": "Framework", "repo": "Repository", "branch": "Branch", "main": "main", "development": "<PERSON><PERSON><PERSON><PERSON>", "staging": "staging"}}, "identity": {"unverified": {"title": "Verifica dell'identità", "description": "Verifica la tua identità per utilizzare Biela.dev", "subtitle": "Processo di verifica sicuro", "processServers": "<PERSON>tte le informazioni sulla carta sono archiviate in modo sicuro sui server di Stripe, non sui server di Biela", "processCharge": "La tua carta non verrà addebitata senza il tuo esplicito consenso per un abbonamento", "processBenefits": "Biela.dev è completamente gratuito per gli account verificati fino al 15 maggio 2025", "verifyStripe": "Verifica con carta di credito o di debito", "verifyStripeDescription": "Collega il tuo metodo di pagamento per la verifica", "verifyNow": "Verifica ora"}, "verified": {"title": "Identità verificata", "description": "La tua identità è stata verificata con successo", "paymentMethod": "Metodo di pagamento", "cardEnding": "Carta che termina con", "updatePayment": "Aggiorna metodo di pagamento", "untilDate": "Fino al 15 maggio 2025", "freeAccess": "<PERSON>o gratuito", "freeAccessDescription": "Goditi l'accesso completo a Biela.dev senza costi", "secureStorage": "Archiviazione sicura", "secureStorageDescription": "Le informazioni della tua carta sono conservate in modo sicuro sui server di Stripe, non su quelli di Biela. La tua carta non verrà addebitata senza il tuo esplicito consenso per un abbonamento.", "subscriptionAvailable": "Gli abbonamenti saranno disponibili a partire dal 15 maggio 2025."}, "connectingToStripe": "Connessione a Stripe in corso..."}, "tabs": {"billing": "Fatturazione", "profile": "<PERSON>ilo", "deployment": "Deployment", "identity": "Identità"}}, "help": {"title": "Come possiamo aiutarti?", "searchPlaceholder": "Cerca nella documentazione...", "categories": {"getting-started": {"title": "Primi passi", "description": "Impara le basi per utilizzare Biela.dev", "articles": ["Guida rapida", "Panoramica della piattaforma", "<PERSON><PERSON><PERSON> il tuo primo progetto", "Comprendere lo sviluppo con IA"]}, "ai-development": {"title": "Sviluppo con IA", "description": "Padroneggia lo sviluppo supportato dall'IA", "articles": ["<PERSON><PERSON><PERSON> prompt efficaci", "Best practices per la generazione di codice", "Consigli per il debug dell'IA", "Funzionalità avanzate dell'IA"]}, "project-management": {"title": "Gestione dei progetti", "description": "Organizza e gestisci i tuoi progetti", "articles": ["Struttura del progetto", "Collaborazione in team", "Controllo delle versioni", "Opzioni di deployment"]}}, "channels": {"docs": {"name": "Documentazione", "description": "Guide complete e riferimenti API"}, "community": {"name": "Comunità", "description": "Connettiti con altri svilup<PERSON>tori"}, "github": {"name": "GitHub", "description": "Segnala problemi e contribuisci"}}, "support": {"title": "Hai ancora bisogno di aiuto?", "description": "Il nostro team di supporto è disponibile 24/7 per aiutarti con qualsiasi domanda o problema.", "button": "Contatta il supporto"}}, "getStarted": {"title": "Scopri come funziona Biela.dev", "description": "Crea la tua app in pochi minuti con Biela.dev. La nostra IA automatizza l'intero processo di sviluppo, dalla configurazione al deployment. Ecco come la nostra IA costruisce la tua app senza sforzo!", "features": {"docs": {"title": "Documentazione per sviluppatori", "description": "Scop<PERSON> come utilizzare Biela.dev con guide facili da seguire, consigli e best practices. Perfetto sia per i principianti che per gli sviluppatori esperti!", "cta": "Esplora la documentazione per sviluppatori"}, "support": {"title": "Feedback e supporto", "description": "Hai bisogno di aiuto o vuoi dare un feedback? Contatta il supporto e contribuisci a migliorare Biela.dev!", "cta": "Invia feedback"}, "platform": {"title": "Funzionalità della piattaforma", "description": "Scopri gli strumenti potenti offerti da Biela.dev per aiutarti a creare siti web e app senza sforzo. Lascia che l'IA si occupi della programmazione per te!", "cta": "Esplora le funzionalità"}}, "video": {"title": "Video di avvio rapido", "description": "Guarda come Biela.dev costruisce per te: creazione di app e siti web senza sforzo!", "cta": "Guarda il tutorial"}, "guide": {"title": "Guida di avvio rapido", "steps": {"setup": {"title": "Configura istantaneamente il tuo progetto", "description": "Prepara il tuo ambiente di sviluppo in pochi secondi"}, "generate": {"title": "Genera codice full-stack con l'IA", "description": "Lascia che l'IA scriva per te un codice pronto per la produzione"}, "features": {"title": "Generazione istantanea di funzionalità", "description": "Aggiungi funzionalità complesse con comandi semplici"}, "editor": {"title": "Editor no-code & low-code", "description": "Modifica la tua app in modo visivo o tramite codice"}, "optimize": {"title": "<PERSON><PERSON><PERSON><PERSON> e testa in tempo reale", "description": "Assicurati che la tua app funzioni perfettamente"}, "deploy": {"title": "Deploy con un clic", "description": "Metti online la tua app istantaneamente con il deployment automatico"}}}, "faq": {"title": "<PERSON><PERSON><PERSON>", "questions": {"what": {"question": "Che cos'è Biela.dev?", "answer": "Biela.dev è una piattaforma alimentata dall'IA che ti aiuta a creare siti web e app, anche se non sai programmare. Automatizza l'intero processo di sviluppo, dalla scrittura del codice al deployment."}, "experience": {"question": "Ho bisogno di esperienza in programmazione per utilizzare Biela.dev?", "answer": "No! Biela.dev è progettato sia per i principianti che per gli sviluppatori esperti. Puoi costruire con l'aiuto dell'IA o personalizzare il codice generato secondo le tue necessità."}, "projects": {"question": "Che tipi di progetti posso creare?", "answer": "Puoi creare siti web, applicazioni web, app mobili, piattaforme SaaS, negozi online, dashboard amministrativi e molto altro."}, "edit": {"question": "Posso modificare il codice generato da Biela.dev?", "answer": "Sì! Biela.dev ti permette di personalizzare il codice generato dall'IA oppure di utilizzare l'editor no-code/low-code per apportare modifiche facilmente."}, "deployment": {"question": "Come funziona il deployment?", "answer": "Con un solo clic, Biela.dev deploya il tuo progetto, renden<PERSON><PERSON> online e pronto all'uso. Non è necessaria alcuna configurazione manuale del server!"}, "pricing": {"question": "Biela.dev è gratuito?", "answer": "Biela.dev offre un piano gratuito con funzionalità di base. Per strumenti e risorse più avanzati, puoi passare a un piano premium."}, "integrations": {"question": "Posso integrare strumenti o database di terze parti?", "answer": "Sì! Biela.dev supporta integrazioni con database popolari (MongoDB, Firebase, Supabase) e API di terze parti."}, "help": {"question": "Dove posso ottenere aiuto se ho dei problemi?", "answer": "Puoi consultare la nostra Documentazione per sviluppatori, la Guida di avvio rapido o contattare il Supporto tramite il nostro centro assistenza."}}}, "cta": {"title": "Pronto per iniziare?", "description": "Crea il tuo primo progetto con Biela.dev e sperimenta il futuro dello sviluppo.", "button": "Crea nuovo progetto"}}, "confirmDeleteProject": "Conferma eliminazione progetto", "confirmRemoveFromFolder": "Conferma rimozione dalla cartella", "deleteProjectWarning": "Sei sicuro di voler eliminare definitivamente {{projectName}}?", "removeFromFolderWarning": "Sei sicuro di voler rimuovere {{projectName}} da {{folderName}}?", "confirm": "Conferma", "confirmDeleteFolder": "Conferma eliminazione cartella", "deleteFolderWarning": "Sei sicuro di voler eliminare {{folderName}}? Questa azione non può essere annullata.", "folderDeletedSuccessfully": "Cartella eliminata con successo", "downloadChat": "Scarica la chat", "inactiveTitle": "Questa scheda è inattiva", "inactiveDescription": "Fai clic sul pulsante qui sotto per attivare questa scheda e continuare a usare l'app.", "inactiveButton": "Usa questa scheda", "suggestions": {"weatherDashboard": "Crea un cruscotto meteo", "ecommercePlatform": "Costruisci una piattaforma di e-commerce", "socialMediaApp": "Progetta un'app di social media", "portfolioWebsite": "Genera un sito portfolio", "taskManagementApp": "Crea un'app per la gestione delle attività", "fitnessTracker": "Costruisci un tracker per il fitness", "recipeSharingPlatform": "Progetta una piattaforma per la condivisione di ricette", "travelBookingSite": "Crea un sito per prenotazioni di viaggi", "learningPlatform": "Costruisci una piattaforma di apprendimento", "musicStreamingApp": "Progetta un'app di streaming musicale", "realEstateListing": "Crea un annuncio immobiliare", "jobBoard": "Costruisci un portale per annunci di lavoro"}, "pleaseWait": "Attendere prego...", "projectsInAll": "<PERSON><PERSON><PERSON> in Tutti", "projectsInCurrentFolder": "<PERSON><PERSON><PERSON> in {{folderName}}", "createYourFirstProject": "Crea il tuo primo progetto", "startCreateNewProjectDescription": "Inizia a costruire qualcosa di straordinario. I tuoi progetti verranno visualizzati qui una volta creati.", "createProjectBtn": "Nuovo Progetto", "publishedToContest": "Pubblicato nel concorso", "publishToContest": "Pubblica nel concorso", "refreshSubmission": "Aggiorna invio", "contestInformation": "Informazioni sul concorso", "selectFolder": "Seleziona una cartella", "folder": "<PERSON><PERSON><PERSON>", "selectAFolder": "Seleziona una cartella", "thisProject": "questo progetto", "projectDeletedSuccessfully": "<PERSON><PERSON>to eliminato con successo", "projectRemovedFromFolder": "<PERSON><PERSON><PERSON> rim<PERSON>o dalla cartella", "unnamedProject": "Progetto senza nome", "permanentDeletion": "Eliminazione permanente", "removeFromFolder": "<PERSON><PERSON><PERSON><PERSON> <PERSON> {{folderName}}", "verifiedAccount": "Account verificato", "hasSubmittedAMinimumOfOneProject": "Ha inviato almeno un progetto", "haveAtLeastActiveReferrals": "<PERSON><PERSON> almeno {{number}} referral attivi", "GoToAffiliateDashBoard": "Vai al pannello affiliati", "LikeOneProjectThatBelongsToAnotherUser": "<PERSON><PERSON> mi piace a un progetto di un altro utente", "GoToContestPage": "Vai alla pagina del concorso", "ContestStatus": "Stato del concorso", "ShowcaseYourBestWork": "Mostra il tuo miglior lavoro", "submissions": "Invii", "qualifyConditions": "Condizioni di qualificazione", "completed": "Completato", "collapseQualifyConditions": "Comprimi condizioni di qualificazione", "expandQualifyConditions": "Espandi condizioni di qualificazione", "basicParticipation": "Partecipazione di base", "complete": "Completato", "incomplete": "Incompleto", "forTop3Places": "Per i primi 3 posti", "deleteSubmission": "Elimina invio", "view": "Visualizza", "submitMoreProjectsToIncreaseYourChangesOfWining": "Invia più progetti per aumentare le tue possibilità di vincere!", "removeFromContest": "Rimuovere dal concorso?", "removeFromContestDescription": "Sei sicuro di voler rimuovere <project>{{project}}</project> dal concorso? Questa azione non può essere annullata.", "cardVerificationRequired": "Verifica della carta richiesta", "pleaseVerifyCard": "Verifica la tua carta per continuare", "unlockFeaturesMessage": "Per sbloccare l'accesso completo a tutte le funzionalità della piattaforma, richiediamo una rapida verifica della carta. Questo processo è completamente sicuro e garantisce un'esperienza fluida su Biela.dev. Non verranno addebitati costi sulla tua carta durante la verifica.", "freeVerificationNotice": "Vantaggi della Verifica", "accessToAllFeatures": "Accesso completo alle funzionalità di Biela.dev.", "enhancedFunctionality": "Prestazioni della piattaforma migliorate.", "quickSecureVerification": "Processo di verifica sicuro e crittografato.", "noCharges": "*<PERSON><PERSON><PERSON> addebito o costo nascosto", "verificationOnly": " — solo verifica.", "verifyNow": "Verifica ora", "DropFilesToUpload": "Trascina i file da caricare", "projectInfo": {"information": "Informazioni sul progetto", "type": "Tipo di progetto", "complexity": "Complessità", "components": "Componenti", "features": "Funzionalità", "confidenceScore": "Livello di fiducia", "estimationUncertainty": "Incertezza della stima", "keyTechnologies": "Tecnologie chiave"}, "projectMetrics": {"teamComposition": "Composizione del team", "hoursBreakdown": "Distribuzione delle ore", "timeToMarket": "Time to Market", "maintenance": "Manutenzione", "aiPowered": "Basato su IA", "developerLevel": "Livello dello sviluppatore", "nextGenAI": "IA di nuova generazione", "keyBenefits": "<PERSON><PERSON><PERSON> principali", "instantDevelopment": "<PERSON><PERSON><PERSON><PERSON>", "noMaintenanceCosts": "Nessun costo di manutenzione", "highConfidence": "Alta fiducia", "productionReadyCode": "Codice pronto per la produzione", "immediate": "Immediato", "uncertainty": "Incertezza", "minimal": "Minimo"}, "loadingMessages": {"publish": ["Recupero informazioni sul progetto...", "Creazione screenshot...", "Generazione del riepilogo e descrizione..."], "refresh": ["Recupero dati di invio correnti...", "Aggiornamento dello screenshot del progetto...", "Aggiornamento dei dettagli del progetto..."]}, "errorMessages": {"publish": "Impossibile pubblicare \"{{projectName}}\" al concorso. Si è verificato un problema durante l'elaborazione della richiesta.", "refresh": "Impossibile aggiornare l'invio di \"{{projectName}}\". Il server non ha potuto aggiornare le informazioni del progetto."}, "actions": {"refresh": {"title": "Aggiornamento invio", "successMessage": "L'invio del tuo progetto è stato aggiornato con successo.", "buttonText": "Visualizza invio aggiornato"}, "publish": {"title": "Pubblicazione al concorso", "successMessage": "Il tuo progetto è stato inviato al concorso.", "buttonText": "Visualizza nella pagina del concorso"}}, "SupabaseConnect": "Collega a Supabase", "SupabaseDashboard": "Dashboard di Supabase", "RestoreApp": "R<PERSON><PERSON><PERSON>", "SaveApp": "<PERSON><PERSON>", "ForkChat": "Fork", "BielaTerminal": "Terminale Biela", "UnitTesting": "Test unitari", "InstallDependencies": "In<PERSON><PERSON> dipendenze", "InstallDependenciesDescription": "Installa tutti i pacchetti richiesti per il progetto usando", "BuildProject": "Compila progetto", "BuildProjectDescription": "Compila e ottimizza il progetto per la produzione usando", "StartDevelopment": "<PERSON><PERSON><PERSON> s<PERSON>", "StartDevelopmentDescription": "Avvia il server di sviluppo per l’anteprima in tempo reale usando", "NoProjectFound": "<PERSON><PERSON><PERSON> progetto trovato con quel nome.", "NoProjectFoundDescription": " Controlla se ci sono errori di battitura e riprova.", "LotOfContext": "Wow, è un sacco di contesto!", "LotOfContextDescription1": "Sembra che l'IA abbia raggiunto il limite di elaborazione per questo progetto. La maggior parte dello spazio è occupata dai file importati, non dalla chat stessa. Reimportare il progetto come nuovo cancellerà la cronologia della chat e libererà solo lo spazio sufficiente per qualche altro prompt — ma tieni presente che i file continueranno a occupare la maggior parte dello spazio disponibile.", "LotOfContextDescription2": "Per mantenere tutto funzionante senza problemi, prova a reimportare ora per un nuovo inizio.", "limitReached": "Hai raggiunto il tuo limite!", "deleteProjectsSupabase": "Elimina alcuni progetti o aumenta il tuo limite in Supabase.", "goTo": "Vai a", "clickProjectSettings": " fai clic sul progetto, Impostazioni del progetto, scorri fino in fondo e fai clic", "delete": "Elimina", "retrying": "<PERSON>nto…", "retryConnection": "Riprova la connessione", "RegisterPageTitle": "Registrati – biela.dev", "RegisterPageDescription": "Crea il tuo account su biela.dev e sblocca tutte le funzionalità.", "SignUpHeading": "Registrati", "AlreadyLoggedInRedirectHome": "Sei già connesso! Reindirizzamento alla home...", "PasswordsMismatch": "Le password non corrispondono.", "FirstNameRequired": "Il nome è obbligatorio", "LastNameRequired": "Il cognome è obbligatorio", "UsernameRequired": "Il nome utente è obbligatorio", "EmailRequired": "L'e-mail è obbligatoria", "EmailInvalid": "Inserisci un indirizzo e-mail valido", "TooManyRequests": "<PERSON><PERSON><PERSON> rich<PERSON>e, riprova più tardi", "SomethingWentWrongMessage": "Qualcosa è andato storto, riprova più tardi", "PasswordRequired": "La password è obbligatoria", "ConfirmPasswordRequired": "Conferma la tua password", "AcceptTermsRequired": "Devi accettare i Termini di servizio e l'Informativa sulla privacy", "CaptchaRequired": "Completa il CAPTCHA", "RegistrationFailed": "Registrazione non riuscita", "EmailConfirmationSent": "È stata inviata un'e-mail di conferma! Conferma la tua e-mail e poi accedi.", "RegistrationServerError": "Registrazione fallita (il server ha restituito false).", "SomethingWentWrong": "Qualcosa è andato storto", "CheckEmailHeading": "Controlla la tua e-mail per confermare la registrazione", "CheckEmailDescription": "Ti abbiamo inviato un'e-mail con un link di conferma.", "GoToHomepage": "Vai alla home page", "ReferralCodeOptional": "Codice di invito (opzionale)", "EnterReferralCode": "Inserisci un codice di invito se sei stato invitato da un altro utente.", "PasswordPlaceholder": "Password", "ConfirmPasswordPlaceholder": "Conferma password", "CreateAccount": "Crea account", "AlreadyHaveAccountPrompt": "Hai già un account?", "Login": "Accedi", "AcceptTermsPrefix": "Accetto i", "TermsOfService": "Termini di servizio", "AndSeparator": "e", "PrivacyPolicy": "Informativa sulla privacy", "LoginPageTitle": "Accedi – biela.dev", "LoginPageDescription": "Accedi al tuo account o entra su biela.dev per utilizzare tutte le funzionalità.", "LogInHeading": "Accedi", "EmailOrUsernamePlaceholder": "E-mail / Nome utente", "ForgotPassword?": "Password dimenticata?", "LoginToProfile": "Accedi al tuo profilo", "UserNotConfirmed": "Account non confermato", "ConfirmEmailNotice": "Devi confermare il tuo indirizzo e-mail per attivare l'account.", "ResendConfirmationEmail": "Invia nuovamente e-mail di conferma", "ResendConfirmationSuccess": "E-mail di verifica inviata nuovamente! Controlla la tua casella di posta.", "ResendConfirmationError": "Errore durante l'invio dell'e-mail di conferma.", "LoginSuccess": "Accesso effettuato con successo! Reindirizzamento...", "LoginFailed": "Accesso non riuscito", "LoginWithGoogle": "Accedi con Google", "LoginWithGitHub": "Accedi con GitHub", "SignUpWithGoogle": "Registrati con Google", "SignUpWithGitHub": "Registrati con GitHub", "Or": "OPPURE", "NoAccountPrompt": "Non hai un account?", "SignMeUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ForgotPasswordPageTitle": "Password dimenticata – biela.dev", "ForgotPasswordPageDescription": "Reimposta la password del tuo account biela.dev e recupera l'accesso.", "BackToLogin": "Torna al login", "ForgotPasswordHeading": "Password dimenticata", "ForgotPasswordDescription": "Inserisci il tuo indirizzo e-mail e ti invieremo un link per reimpostare la password.", "VerificationLinkSent": "Link di verifica inviato! Controlla la tua e-mail.", "EnterYourEmailPlaceholder": "Inserisci la tua e-mail", "Sending": "Invio in corso...", "SendVerificationCode": "Invia codice di verifica", "InvalidConfirmationLink": "Link di conferma non valido", "Back": "Indietro", "ResetPassword": "Reim<PERSON>a password", "ResetPasswordDescription": "Crea una nuova password per il tuo account", "NewPasswordPlaceholder": "Nuova password", "ConfirmNewPasswordPlaceholder": "Conferma nuova password", "ResetPasswordButton": "Reim<PERSON>a password", "PasswordRequirements": "La password deve essere di almeno 8 caratteri e includere una lettera maiuscola, una minuscola, un numero e un carattere speciale.", "PasswordUpdatedSuccess": "Password aggiornata con successo!", "affiliateDashboard": "Dashboard affiliato", "userDashboard": "Dashboard utente", "returnToAffiliateDashboard": "<PERSON>na alla dashboard affiliato", "returnToUserDashboard": "Torna alla dashboard utente", "myProfile": "Il mio profilo", "viewAndEditYourProfile": "Visualizza e modifica il tuo profilo", "billing": "Fatturazione", "manageYourBillingInformation": "Gestisci le tue informazioni di fatturazione", "logout": "<PERSON>nne<PERSON><PERSON>", "logoutDescription": "Disconnettiti dal tuo account", "SupabaseNotAvailable": "Supabase non è disponibile al momento, riprova più tardi.", "projectActions": {"invalidSlug": "Slug del progetto non valido.", "downloadSuccess": "Progetto scaricato con successo!", "downloadError": "Impossibile scaricare il progetto.", "exportSuccess": "Chat esportata! Controlla la cartella dei download.", "exportError": "Impossibile esportare la chat.", "duplicateSuccess": "Chat duplicata con successo!", "duplicateError": "Impossibile duplicare la chat."}, "enter_new_phone_number": "Inserisci un nuovo numero", "enter_new_phone_number_below": "Inser<PERSON>ci il tuo nuovo numero di telefono qui sotto:", "new_phone_placeholder": "Nuovo numero di telefono", "enter_otp_code": "Inserisci il codice OTP", "confirm_phone_message": "Per usare il tuo account, devi confermare il numero. Inserisci il codice inviato a ({{phone}}).", "wrong_phone": "Numero sbagliato?", "resend_sms": "Reinvia <PERSON>", "submit": "Invia", "tokensAvailable": "token disponibili", "sectionTitle": "Gestione Dominio", "addDomainButton": "Aggiungi Nome Dominio", "connectCustomDomainTitle": "Collega un Nome Dominio Personalizzato", "disclaimer": "Nota:", "disclaimerText": "Per verificare correttamente, devi impostare correttamente tutte le regole DNS sopra elencate", "domainInputDescription": "Inserisci il nome del dominio che vuoi collegare a questo progetto.", "domainLabel": "Nome Dominio", "domainPlaceholder": "example.com", "cancelButton": "<PERSON><PERSON><PERSON>", "continueButton": "Aggiungi Nome Dominio", "deployingText": "Distribuzione in corso...", "addingText": "Aggiunta in corso...", "verifyButtonText": "Verifica", "configureDnsTitle": "Configura Record DNS", "configureDnsDescription": "Aggiungi i seguenti record DNS al tuo dominio per verificarne la proprietà e collegarlo a questo progetto.", "tableHeaderType": "Tipo", "tableHeaderName": "Nome", "tableHeaderValue": "Valore", "note": "Nota:", "noteText": "Le modifiche al DNS possono richiedere fino a 48 ore per propagarsi. Tuttavia, spesso hanno effetto entro pochi minuti o poche ore.", "backButton": "Indietro", "showDnsButton": "Mostra Impostazioni DNS", "hideDnsButton": "Nascondi Impostazioni DNS", "removeButton": "<PERSON><PERSON><PERSON><PERSON>", "dnsSettingsTitle": "Impostazioni DNS Dominio", "removeDomainConfirmTitle": "Rimuovi Nome Dominio", "removeConfirmationText": "Sei sicuro di voler rimuovere il nome di dominio ", "importantCleanupTitle": "Pulizia DNS Importante", "cleanupDescription": "Dopo aver rimosso questo dominio dal tuo progetto, ricorda anche di rimuovere i record DNS creati durante la configurazione. Questo aiuta a mantenere una configurazione DNS pulita e a prevenire potenziali conflitti futuri.", "confirmRemoveButton": "Rimuovi Nome Dominio", "customConfigTitle": "Configurazione Dominio Personalizzato", "customConfigDescription": "Collega i tuoi domini personalizzati a questo progetto. Il tuo progetto sarà sempre accessibile tramite il dominio predefinito di Biela, ma i domini personalizzati offrono un'esperienza di marca professionale per i tuoi utenti.", "defaultLabel": "Predefinito", "statusActive": "Attivo", "statusPending": "In Attesa", "lastVerifiedText": "Verificato pochi istanti fa", "errorInvalidDomain": "Inserisci un nome di dominio valido (es. example.com)", "errorDuplicateDomain": "Questo nome di dominio è già collegato al tuo progetto", "errorAddFail": "Impossibile aggiungere il nome di dominio.", "successAdd": "Nome dominio aggiunto con successo! Il dominio è stato collegato a questo progetto.", "benefitsTitle": "Vantaggi del Dominio", "benefitSecurityTitle": "<PERSON><PERSON><PERSON>", "benefitSecurityDesc": "Tutti i domini personalizzati sono automaticamente protetti con certificati SSL.", "benefitPerformanceTitle": "Prestazioni Elevate", "benefitPerformanceDesc": "La CDN globale garantisce un caricamento rapido del progetto per utenti in tutto il mondo.", "benefitBrandingTitle": "Branding Professionale", "benefitBrandingDesc": "Utilizza il tuo dominio per un'esperienza di marca coerente.", "benefitAnalyticsTitle": "Integrazione Analitica", "benefitAnalyticsDesc": "I domini personalizzati funzionano perfettamente con le piattaforme di analisi.", "meta": {"index": {"title": "biela.dev | Costruttore di Web e App con IA – Costruisci con i Prompt", "description": "Trasforma le tue idee in siti web o app funzionanti con biela.dev. Utilizza prompt guidati dall'IA per creare prodotti digitali personalizzati senza sforzo."}, "login": {"title": "Accedi al Tuo Account biela.dev", "description": "Accedi alla tua dashboard biela.dev per gestire e costruire i tuoi progetti generati dall'IA."}, "register": {"title": "Registrati su biela.dev – Inizia a Costruire con l'IA", "description": "Crea il tuo account biela.dev per iniziare a costruire siti web e app utilizzando prompt alimentati dall'IA."}, "dashboard": {"title": "La Tua Dashboard Progetti – biela.dev", "description": "Gestisci i tuoi siti web e app costruiti con l'IA, modifica progetti in tempo reale e tieni traccia della tua cronologia di sviluppo, tutto in un unico posto."}, "profile": {"title": "Il tuo profilo – biela.dev", "description": "Visualizza e aggiorna i dettagli del tuo account biela.dev, gestisci le preferenze e personalizza la tua esperienza di sviluppo AI."}, "billing": {"title": "Fatturazione – biela.dev", "description": "Aggiungi la tua carta di credito per verificare la tua identità e sbloccare tutte le funzionalità di biela.dev – nessun addebito verrà effettuato."}}, "transferProject": "Condividi una copia", "transferSecurityNoteDescription": "Il destinatario riceverà l'accesso completo a una copia di questo progetto e a tutte le risorse associate.", "transferProjectDescription": "Inserisci il nome utente o l'indirizzo email della persona a cui vuoi trasferire una copia di questo progetto.", "transferProjectLabel": "Nome utente o email", "transferProjectPlaceholder": "<NAME_EMAIL>", "transferButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transferSecurityNote": "Nota di sicurezza:", "dontHavePermisionToTransfer": "Non hai il permesso di trasferire questo progetto", "transferProjectUserNotFound": "Utente {{ user }} non trovato!", "transferErrorOwnAccount": "Non puoi trasferire un progetto sul tuo stesso account.", "transferError": "Errore nel trasferimento del progetto", "transferSuccess": "<PERSON><PERSON><PERSON><PERSON> con successo a {{ user }}", "enterValidEmailUsername": "Per favore, inserisci un nome utente o un'email", "enterMinValidEmailUsername": "Per favore, inserisci un nome utente valido (minimo 3 caratteri) o un indirizzo email", "youWillStillHaveAccess": "Avrai ancora accesso al progetto originale", "newChangesWillNotAffect": "Le nuove modifiche non influiranno sul progetto dell'altro utente"}