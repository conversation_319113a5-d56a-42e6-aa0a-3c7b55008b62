{"whatWouldYouLikeToBuild": "在几分钟内将你的创意变成一个实时网站或应用", "whatWouldYouLikeToBuildSubtitle": "如果你能<1>想象</1>它，你就能<5>编码</5>它。", "fromIdeaToDeployment": "免费开始。编写任何内容的代码。用每一次提示将你的技能转化为机会。", "codePlaceholder": "如果你能想象，BIELA 就能编写代码，今天我们做什么？", "defaultPlaceholder": "今天我怎样能帮你？让我们一起创造点非凡的东西吧", "checkingFeatures": "正在检查功能", "checklists": "检查清单", "runUnitTestsSuggestionTitle": "建议", "runUnitTestsSuggestionMessage": "你想为你的项目运行单元测试吗？", "runUnitTestsPrimaryButton": "运行单元测试", "runUnitTestsSecondaryButton": "取消", "createDatabaseTitle": "创建数据库", "createDatabaseMessage": "你想为你的项目创建数据库吗？", "createDatabasePrimaryButton": "创建数据库", "createDatabaseSecondaryButton": "取消", "extendedThinking": "扩展思考", "extendedThinkingTooltip": "让AI在回答前进行更深入的思考", "firstResponseOnly": "仅首次回复", "always": "始终", "attachFile": "附加文件", "voiceInput": "语音输入", "selectLanguage": "选择语音输入的语言", "languageSelectionDisabled": "录音期间禁用语言选择", "notAvailableInFirefox": "此功能在Firefox中不可用，但在Chrome和Safari中可用", "enhancePrompt": "优化提示", "cleanUpProject": "清理项目", "showPrompt": "显示提示", "hidePrompt": "隐藏提示", "sendButton": "发送", "abortButton": "取消", "inspirationTitle": "需要灵感？试试下面这些：", "cleanUpPrompt": "清理项目，确保没有任何文件超过300行代码。将大文件重构为更小的模块化组件，同时保持全部功能。识别并删除所有不再需要的文件、代码、组件和冗余数据。确保所有组件依旧正确连接并正常工作，避免对现有系统造成干扰。请验证所有修改不会引入错误或破坏现有功能。目标是优化项目的效率、可维护性和清晰度。", "checklistPrompt": "查看我的初始提示，逐步理解目标，并为我创建一个检查清单，已完成的标记绿色，未完成的标记红色。", "personalPortfolioIdea": "创建一个暗色主题的个人作品集网站", "recipeFinderIdea": "构建一个根据食材推荐菜谱的应用", "weatherDashboardIdea": "设计一个带动画背景的天气仪表板", "habitTrackerIdea": "开发一个带进度可视化的习惯追踪器", "loading": "加载中", "error": "错误", "succes": "成功!", "tryAgain": "再试一次", "dashboard": "仪表板", "getStartedTitle": "开始", "getStartedSub": "探索 Biela.dev 的工作原理", "createProject": "创建新项目", "createProjectSub": "从零开始", "editProjectName": "编辑项目名称", "editName": "编辑名称", "uploadProject": "上传项目", "uploadProjectSub": "导入现有项目", "importChat": "导入聊天", "importChatSub": "导入现有的聊天", "createFolder": "创建新文件夹", "createFolderSub": "整理你的项目", "cancel": "取消", "changeFolder": "更改文件夹", "save": "保存", "importing": "正在导入...", "importFolder": "导入文件夹", "giveTitle": "指定标题", "projects": "项目", "searchProjects": "搜索项目...", "becomeAffiliate": "成为合作伙伴", "exclusiveGrowth": "独家成长福利", "lifetimeEarnings": "终身收益", "highCommissions": "高额佣金", "earnCommission": "首次销售可获得50%佣金", "joinAffiliateProgram": "加入我们的合作伙伴计划", "folders": "文件夹", "organizeProjects": "按类别组织你的项目", "createNewFolder": "创建新文件夹", "enterFolderName": "输入文件夹名称", "editFolder": "编辑文件夹", "deleteFolder": "删除文件夹", "all": "全部", "webProjects": "网页项目", "mobilepps": "移动应用", "developmentComparison": "开发比较", "traditionalVsAI": "传统 vs AI", "traditional": "传统", "standardApproach": "标准方法", "developmentCost": "开发成本", "developmentTime": "开发时间", "costSavings": "成本节约", "reducedCosts": "降低的成本", "timeSaved": "节省的时间", "fasterDelivery": "更快交付", "bielaDevAI": "Biela.dev AI", "nextGenDevelopment": "下一代开发", "developmentCosts": "开发费用", "openInGitHub": "在 GitHub 上打开", "downloadProject": "下载项目", "duplicateProject": "复制项目", "openProject": "打开项目", "deleteProject": "删除项目", "confirmDelete": "删除此文件夹？", "invoicePreview": "发票预览", "settings": {"title": "设置", "deployment": {"AdvancedSettings": {"advanced-settings": "高级设置", "configure-advanced-deployment-options": "配置高级部署选项", "server-configuration": "服务器配置", "memory-limit": "内存限制", "region": "区域", "security-settings": "安全设置", "enable-ddos-protection": "启用 DDoS 保护", "protect-against-distributed": "防御分布式拒绝服务攻击", "ip-whitelisting": "IP 白名单", "restrict-acces-to": "限制访问特定 IP 地址", "deployment-options": "部署选项", "auto-deploy": "自动部署", "automatically-deploy-when": "推送到主分支时自动部署", "preview-deployments": "预览部署", "create-preview-deployments": "为拉取请求创建预览部署"}, "BuildSettings": {"build-and-deployment-settings": "构建与部署设置", "build-command": "构建命令", "override": "覆盖", "output-directory": "输出目录", "override2": "覆盖"}, "DatabaseConfiguration": {"database-configuration": "数据库配置", "configure-your-db-connections": "配置您的数据库连接和设置", "database-type": "数据库类型", "connection-string": "连接字符串", "your-db-credentials": "您的数据库凭据已加密并安全存储", "database-settings": "数据库设置", "pool-size": "连接池大小", "require": "必需", "prefer": "优先", "disable": "禁用", "add-database": "添加数据库"}, "DomainSettings": {"domain-settings": "域名设置", "configure-your-custom-domain": "配置您的自定义域名和 SSL 证书", "custom-domain": "自定义域名", "add": "添加", "ssl-certificate": "SSL 证书", "auto-renew-ssl-certificates": "自动续订 SSL 证书", "auto-renew-before-expiry": "在到期前自动续订 SSL 证书", "force-https": "强制 HTTPS", "redirect-all-http-traffic": "将所有 HTTP 流量重定向至 HTTPS", "active-domain": "激活的域名", "remove": "移除"}, "EnvironmentVariables": {"environment-variables": "环境变量", "configure-environment-variables": "配置您的部署环境变量", "all-enviroments": "所有环境", "environment-variables2": "环境变量", "preview": "预览", "development": "开发", "create-new": "新建", "key": "键", "value": "值", "save-variable": "保存变量", "you-can-also-import": "您还可以从 .env 文件中导入变量：", "import-env-file": "导入 .env 文件"}, "ProjectConfiguration": {"project-configuration": "项目配置", "config-your-project-settings": "配置您的项目设置和部署选项", "project-url": "项目 URL", "framework": "框架", "repo": "仓库", "branch": "分支", "main": "main", "development": "development", "staging": "staging"}}, "identity": {"unverified": {"title": "身份验证", "description": "验证您的身份以使用 Biela.dev", "subtitle": "安全的验证过程", "processServers": "所有卡片信息都安全地存储在 Stripe 的服务器上，而不是 Biela 的服务器上", "processCharge": "未经您明确同意，您的卡片将不会被收费", "processBenefits": "Biela.dev 在 2025 年 5 月 15 日之前对已验证账户完全免费", "verifyStripe": "使用信用卡或借记卡验证", "verifyStripeDescription": "连接您的支付方式以进行验证", "verifyNow": "立即验证"}, "verified": {"title": "身份已验证", "description": "您的身份已成功验证", "paymentMethod": "支付方式", "cardEnding": "卡片末尾", "updatePayment": "更新支付方式", "untilDate": "直到 2025 年 5 月 15 日", "freeAccess": "免费访问", "freeAccessDescription": "享受对 Biela.dev 的完整访问权限，无需支付费用", "secureStorage": "安全存储", "secureStorageDescription": "您的卡信息安全地存储在Stripe的服务器上，而不是Biela的服务器上。在没有您明确同意订阅的情况下，您的卡不会被收费。", "subscriptionAvailable": "订阅将从2025年5月15日起提供。"}, "connectingToStripe": "正在连接到 Stripe..."}, "tabs": {"billing": "账单", "profile": "个人资料", "deployment": "部署", "identity": "身份"}}, "help": {"title": "我们如何帮助您？", "searchPlaceholder": "在文档中搜索...", "categories": {"getting-started": {"title": "入门", "description": "了解如何使用 Biela.dev 的基础知识", "articles": ["快速入门指南", "平台概览", "创建您的第一个项目", "理解 AI 开发"]}, "ai-development": {"title": "AI 开发", "description": "掌握 AI 驱动的开发", "articles": ["撰写有效提示", "代码生成最佳实践", "AI 调试技巧", "高级 AI 功能"]}, "project-management": {"title": "项目管理", "description": "组织并管理您的项目", "articles": ["项目结构", "团队协作", "版本控制", "部署选项"]}}, "channels": {"docs": {"name": "文档", "description": "详尽的指南和 API 参考"}, "community": {"name": "社区", "description": "与其他开发者交流"}, "github": {"name": "GitHub", "description": "报告问题并贡献代码"}}, "support": {"title": "还需要帮助吗？", "description": "我们的支持团队全天候 24/7 为您提供帮助，解答您的任何问题或解决问题。", "button": "联系支持"}}, "getStarted": {"title": "了解 Biela.dev 的工作原理", "description": "使用 Biela.dev，您可以在几分钟内创建您的应用程序。我们的 AI 自动化了从设置到部署的整个开发过程。看看我们的 AI 如何轻松构建您的应用程序！", "features": {"docs": {"title": "开发者文档", "description": "通过易于理解的指南、提示和最佳实践，学习如何使用 Biela.dev。适合初学者和经验丰富的开发者！", "cta": "探索开发者文档"}, "support": {"title": "反馈与支持", "description": "如果您需要帮助或有反馈意见，请联系支持团队，帮助我们改进 Biela.dev！", "cta": "发送反馈"}, "platform": {"title": "平台功能", "description": "探索 Biela.dev 提供的强大工具，帮助您轻松创建网站和应用程序。让 AI 为您编写代码！", "cta": "探索功能"}}, "video": {"title": "快速入门视频", "description": "观看 Biela.dev 如何为您构建——轻松创建应用和网站！", "cta": "观看教程"}, "guide": {"title": "快速入门指南", "steps": {"setup": {"title": "立即设置您的项目", "description": "在几秒钟内准备好您的开发环境"}, "generate": {"title": "使用 AI 生成全栈代码", "description": "让 AI 为您编写生产就绪的代码"}, "features": {"title": "即时生成功能", "description": "通过简单的提示添加复杂的功能"}, "editor": {"title": "无代码 & 低代码编辑器", "description": "通过可视化或代码方式修改您的应用"}, "optimize": {"title": "实时优化与测试", "description": "确保您的应用完美运行"}, "deploy": {"title": "一键部署", "description": "通过自动部署立即将您的应用上线"}}}, "faq": {"title": "常见问题", "questions": {"what": {"question": "什么是 Biela.dev？", "answer": "Biela.dev 是一个基于 AI 的平台，帮助您创建网站和应用，即使您不会编程。它自动化了从编写代码到部署的整个开发过程。"}, "experience": {"question": "使用 Biela.dev 需要编程经验吗？", "answer": "不需要！Biela.dev 设计既适用于初学者，也适用于有经验的开发者。您可以在 AI 的帮助下构建，或根据需要自定义生成的代码。"}, "projects": {"question": "我可以创建哪些类型的项目？", "answer": "您可以创建网站、网络应用、移动应用、SaaS 平台、电子商务店铺、管理仪表板等。"}, "edit": {"question": "我可以编辑 Biela.dev 生成的代码吗？", "answer": "可以！Biela.dev 允许您自定义 AI 生成的代码，或使用无代码/低代码编辑器轻松进行修改。"}, "deployment": {"question": "部署如何工作？", "answer": "只需点击一次，Biela.dev 就会将您的项目部署上线，使其在线且可供使用。无需手动设置服务器！"}, "pricing": {"question": "Biela.dev 是免费的吗？", "answer": "Biela.dev 提供基础功能的免费计划。对于更高级的工具和资源，您可以升级到高级计划。"}, "integrations": {"question": "我可以整合第三方工具或数据库吗？", "answer": "可以！Biela.dev 支持与流行数据库（如 MongoDB、Firebase、Supabase）和第三方 API 的整合。"}, "help": {"question": "如果我遇到问题，在哪里可以获得帮助？", "answer": "您可以查看我们的开发者文档、快速入门指南，或通过帮助中心联系支持。"}}}, "cta": {"title": "准备好开始了吗？", "description": "使用 Biela.dev 创建您的第一个项目，体验开发的未来。", "button": "创建新项目"}}, "confirmDeleteProject": "确认删除项目", "confirmRemoveFromFolder": "确认从文件夹中移除", "deleteProjectWarning": "您确定要永久删除 {{projectName}} 吗？", "removeFromFolderWarning": "您确定要将 {{projectName}} 从 {{folderName}} 中移除吗？", "confirm": "确认", "confirmDeleteFolder": "确认删除文件夹", "deleteFolderWarning": "您确定要删除 {{folderName}} 吗？此操作无法撤销。", "folderDeletedSuccessfully": "文件夹删除成功", "downloadChat": "下载聊天记录", "inactiveTitle": "该标签页未激活", "inactiveDescription": "点击下方按钮以激活此标签页并继续使用应用。", "inactiveButton": "使用此标签页", "suggestions": {"weatherDashboard": "创建天气仪表板", "ecommercePlatform": "构建电商平台", "socialMediaApp": "设计社交媒体应用", "portfolioWebsite": "生成作品集网站", "taskManagementApp": "创建任务管理应用", "fitnessTracker": "构建健身追踪器", "recipeSharingPlatform": "设计食谱分享平台", "travelBookingSite": "创建旅行预订网站", "learningPlatform": "构建学习平台", "musicStreamingApp": "设计音乐流媒体应用", "realEstateListing": "创建房地产列表", "jobBoard": "构建招聘网站"}, "pleaseWait": "请稍候...", "projectsInAll": "所有项目", "projectsInCurrentFolder": "{{folderName}} 中的项目", "createYourFirstProject": "创建您的第一个项目", "startCreateNewProjectDescription": "开始构建一些令人惊叹的东西。创建项目后，它们将显示在此处。", "createProjectBtn": "新项目", "publishedToContest": "已发布到比赛", "publishToContest": "发布到比赛", "refreshSubmission": "刷新提交", "contestInformation": "比赛信息", "selectFolder": "选择一个文件夹", "folder": "文件夹", "selectAFolder": "选择一个文件夹", "thisProject": "此项目", "projectDeletedSuccessfully": "项目已成功删除", "projectRemovedFromFolder": "项目已从文件夹中移除", "unnamedProject": "未命名项目", "permanentDeletion": "永久删除", "removeFromFolder": "从 {{folderName}} 中移除", "verifiedAccount": "已验证账户", "hasSubmittedAMinimumOfOneProject": "已提交至少一个项目", "haveAtLeastActiveReferrals": "至少有{{number}}个活跃推荐", "GoToAffiliateDashBoard": "前往联盟仪表板", "LikeOneProjectThatBelongsToAnotherUser": "喜欢属于其他用户的一个项目", "GoToContestPage": "前往比赛页面", "ContestStatus": "比赛状态", "ShowcaseYourBestWork": "展示您的最佳作品", "submissions": "提交", "qualifyConditions": "资格条件", "completed": "已完成", "collapseQualifyConditions": "折叠资格条件", "expandQualifyConditions": "展开资格条件", "basicParticipation": "基本参与", "complete": "已完成", "incomplete": "未完成", "forTop3Places": "为前三名", "deleteSubmission": "删除提交", "view": "查看", "submitMoreProjectsToIncreaseYourChangesOfWining": "提交更多项目以增加获胜机会！", "removeFromContest": "从比赛中移除？", "removeFromContestDescription": "您确定要从比赛中移除<project>{{project}}</project>吗？此操作无法撤销。", "cardVerificationRequired": "需要验证银行卡", "pleaseVerifyCard": "请验证您的银行卡以继续", "unlockFeaturesMessage": "要解锁所有平台功能的完全访问权限，我们需要快速的卡验证。此过程完全安全，并确保在 Biela.dev 上获得流畅的体验。在验证期间不会向您的卡收取任何费用。", "freeVerificationNotice": "验证的好处", "accessToAllFeatures": "完全访问 Biela.dev 功能。", "enhancedFunctionality": "提升的平台性能。", "quickSecureVerification": "安全加密的验证过程。", "noCharges": "*无额外费用或隐藏费用", "verificationOnly": " — 仅验证。", "verifyNow": "立即验证", "DropFilesToUpload": "拖放文件以上传", "projectInfo": {"information": "项目信息", "type": "项目类型", "complexity": "复杂性", "components": "组件", "features": "功能", "confidenceScore": "信心评分", "estimationUncertainty": "估算不确定性", "keyTechnologies": "关键技术"}, "projectMetrics": {"teamComposition": "团队组成", "hoursBreakdown": "工时细分", "timeToMarket": "上市时间", "maintenance": "维护", "aiPowered": "AI 驱动", "developerLevel": "开发者级别", "nextGenAI": "下一代 AI", "keyBenefits": "关键优势", "instantDevelopment": "即时开发", "noMaintenanceCosts": "无需维护费用", "highConfidence": "高信心", "productionReadyCode": "生产就绪代码", "immediate": "立即", "uncertainty": "不确定性", "minimal": "最小"}, "loadingMessages": {"publish": ["获取项目信息...", "正在截图...", "生成摘要和描述..."], "refresh": ["正在检索当前提交数据...", "更新项目截图...", "刷新项目详情..."]}, "errorMessages": {"publish": "无法将 \"{{projectName}}\" 发布到竞赛。处理您的请求时出现问题。", "refresh": "无法刷新 \"{{projectName}}\" 提交。服务器无法更新您的项目信息。"}, "actions": {"refresh": {"title": "刷新提交", "successMessage": "您的项目提交已成功更新。", "buttonText": "查看更新后的提交"}, "publish": {"title": "发布到竞赛", "successMessage": "您的项目已提交到竞赛。", "buttonText": "在竞赛页面查看"}}, "SupabaseConnect": "连接 Supabase", "SupabaseDashboard": "Supabase 控制面板", "RestoreApp": "恢复", "SaveApp": "保存", "ForkChat": "分叉", "BielaTerminal": "Biela终端", "UnitTesting": "单元测试", "InstallDependencies": "安装依赖项", "InstallDependenciesDescription": "使用以下命令安装项目所需的所有包", "BuildProject": "构建项目", "BuildProjectDescription": "使用以下命令编译并优化项目以供生产使用", "StartDevelopment": "开始开发", "StartDevelopmentDescription": "使用以下命令启动开发服务器进行实时预览", "NoProjectFound": "未找到具有该名称的项目。", "NoProjectFoundDescription": " 请检查是否有拼写错误，然后重试。", "ContextLimitReached": "已达到上下文限制", "ClaudeContextDescription1": "您已达到此项目在 Claude 中的上下文容量。别担心 — 我们可以切换到具有更大上下文窗口的 Gemini 模型继续进行。", "ClaudeContextDescription2": "Gemini 模型提供高达 5 倍的上下文容量，使您可以保留所有代码和聊天记录，无缝继续构建项目。", "SelectModelToContinue": "选择模型继续：", "Performance": "性能", "AlternativeLimitExplanation": "AI 似乎已达到该项目的处理限制。大部分空间被导入的文件占用，而不是聊天内容。", "SuggestedSolutions": "建议的解决方案：", "ReimportProject": "作为新项目重新导入", "ReimportProjectDescription": "这将清除聊天记录并释放一些上下文空间，同时保留您的文件。", "BreakIntoProjects": "拆分为多个项目", "BreakIntoProjectsDescription": "将工作拆分为可单独开发的小组件。", "ExportWork": "导出已完成的工作", "ExportWorkDescription": "下载并存档已完成的文件以释放上下文空间。", "AlternativeContextNote": "为了最大限度地利用可用的上下文，请考虑删除未使用的文件或库，并专注于当前开发阶段所需的核心文件。", "ContinueWithSelectedModel": "使用所选模型继续", "Close": "关闭", "AIModel": "AI 模型", "Active": "激活", "Stats": "统计", "Cost": "成本", "ExtendedThinkingDisabledForModel": "该模型不支持扩展思考功能", "ExtendedThinkingAlwaysOn": "此模恆啟", "limitReached": "您已达到限制！", "deleteProjectsSupabase": "请删除一些项目或在 Supabase 中增加您的限制。", "goTo": "前往", "clickProjectSettings": " 点击项目，项目设置，向下滚动并点击", "delete": "删除", "retrying": "正在重试…", "retryConnection": "重试连接", "RegisterPageTitle": "注册 – biela.dev", "RegisterPageDescription": "在 biela.dev 创建账户，解锁所有功能。", "SignUpHeading": "注册", "AlreadyLoggedInRedirectHome": "你已登录！正在跳转到主页...", "PasswordsMismatch": "两次输入的密码不一致。", "FirstNameRequired": "名字为必填项", "LastNameRequired": "姓氏为必填项", "UsernameRequired": "用户名为必填项", "EmailRequired": "邮箱为必填项", "EmailInvalid": "请输入有效的邮箱地址", "TooManyRequests": "请求过多，请稍后再试", "SomethingWentWrongMessage": "出了点问题，请稍后再试", "PasswordRequired": "密码为必填项", "ConfirmPasswordRequired": "请确认你的密码", "AcceptTermsRequired": "你必须同意服务条款和隐私政策", "CaptchaRequired": "请完成验证码", "RegistrationFailed": "注册失败", "EmailConfirmationSent": "确认邮件已发送！请确认邮箱后再登录。", "RegistrationServerError": "注册失败（服务器返回 false）。", "SomethingWentWrong": "出现了问题", "CheckEmailHeading": "请查收你的邮箱以确认注册", "CheckEmailDescription": "我们已向你发送了一封包含确认链接的邮件。", "GoToHomepage": "前往首页", "ReferralCodeOptional": "推荐码（可选）", "EnterReferralCode": "如果你是由其他用户邀请，请输入推荐码。", "PasswordPlaceholder": "密码", "ConfirmPasswordPlaceholder": "确认密码", "CreateAccount": "创建账户", "AlreadyHaveAccountPrompt": "已经有账户？", "Login": "登录", "AcceptTermsPrefix": "我同意", "TermsOfService": "服务条款", "AndSeparator": "和", "PrivacyPolicy": "隐私政策", "LoginPageTitle": "登录 – biela.dev", "LoginPageDescription": "访问你的账户或登录 biela.dev 以使用全部功能。", "LogInHeading": "登录", "EmailOrUsernamePlaceholder": "邮箱 / 用户名", "ForgotPassword?": "忘记密码？", "LoginToProfile": "登录你的个人资料", "UserNotConfirmed": "用户账户尚未确认", "ConfirmEmailNotice": "你需要确认邮箱地址以激活账户。", "ResendConfirmationEmail": "重新发送确认邮件", "ResendConfirmationSuccess": "确认邮件已重新发送！请查收你的邮箱。", "ResendConfirmationError": "确认邮件发送失败。", "LoginSuccess": "登录成功！正在跳转...", "LoginFailed": "登录失败", "LoginWithGoogle": "使用 Google 登录", "LoginWithGitHub": "使用 GitHub 登录", "SignUpWithGoogle": "使用 Google 注册", "SignUpWithGitHub": "使用 GitHub 注册", "Or": "或", "NoAccountPrompt": "还没有账户？", "SignMeUp": "注册账号", "ForgotPasswordPageTitle": "忘记密码 – biela.dev", "ForgotPasswordPageDescription": "重置你的 biela.dev 密码并重新获取访问权限。", "BackToLogin": "返回登录", "ForgotPasswordHeading": "忘记密码", "ForgotPasswordDescription": "请输入你的邮箱地址，我们将发送重置密码的验证链接。", "VerificationLinkSent": "验证链接已发送！请查收你的邮箱。", "EnterYourEmailPlaceholder": "请输入你的邮箱", "Sending": "发送中...", "SendVerificationCode": "发送验证码", "InvalidConfirmationLink": "确认链接无效", "Back": "返回", "ResetPassword": "重置密码", "ResetPasswordDescription": "为你的账户设置一个新密码", "NewPasswordPlaceholder": "新密码", "ConfirmNewPasswordPlaceholder": "确认新密码", "ResetPasswordButton": "重置密码", "PasswordRequirements": "密码必须至少包含 8 个字符，包括一个大写字母、小写字母、数字和特殊字符。", "PasswordUpdatedSuccess": "密码更新成功！", "affiliateDashboard": "联盟仪表板", "userDashboard": "用户仪表板", "returnToAffiliateDashboard": "返回联盟仪表板", "returnToUserDashboard": "返回用户仪表板", "myProfile": "我的资料", "viewAndEditYourProfile": "查看和编辑您的资料", "billing": "账单", "manageYourBillingInformation": "管理您的账单信息", "logout": "登出", "logoutDescription": "从您的账户登出", "SupabaseNotAvailable": "Supabase目前不可用，请稍后再试。", "projectActions": {"invalidSlug": "项目标识无效。", "downloadSuccess": "项目已成功下载！", "downloadError": "项目下载失败。", "exportSuccess": "聊天已导出！请检查您的下载文件夹。", "exportError": "聊天导出失败。", "duplicateSuccess": "聊天已成功复制！", "duplicateError": "聊天复制失败。"}, "enter_new_phone_number": "输入新电话号码", "enter_new_phone_number_below": "请输入你的新电话号码：", "new_phone_placeholder": "新电话号码", "enter_otp_code": "输入验证码", "confirm_phone_message": "为了使用您的账户，请验证手机号。请输入发送到（{{phone}}）的验证码。", "wrong_phone": "手机号错误？", "resend_sms": "重新发送短信", "submit": "提交", "tokensAvailable": "可用令牌", "sectionTitle": "域名管理", "addDomainButton": "添加域名", "connectCustomDomainTitle": "连接自定义域名", "disclaimer": "免责声明：", "disclaimerText": "为了成功验证，您必须正确设置上述所有 DNS 规则", "domainInputDescription": "输入您想连接到此项目的域名。", "domainLabel": "域名", "domainPlaceholder": "example.com", "cancelButton": "取消", "continueButton": "添加域名", "deployingText": "正在部署...", "addingText": "正在添加...", "verifyButtonText": "验证", "configureDnsTitle": "配置 DNS 记录", "configureDnsDescription": "请将以下 DNS 记录添加到您的域名中以验证所有权并连接到此项目。", "tableHeaderType": "类型", "tableHeaderName": "名称", "tableHeaderValue": "值", "note": "注意：", "noteText": "DNS 更改可能需要长达 48 小时才能生效，但通常会在几分钟到几小时内生效。", "backButton": "返回", "showDnsButton": "显示 DNS 设置", "hideDnsButton": "隐藏 DNS 设置", "removeButton": "移除", "dnsSettingsTitle": "域名 DNS 设置", "removeDomainConfirmTitle": "移除域名", "removeConfirmationText": "您确定要移除该域名 ", "importantCleanupTitle": "重要 DNS 清理", "cleanupDescription": "在将域名从项目中移除后，请记得也移除在设置期间创建的 DNS 记录。这有助于保持 DNS 配置清洁并避免潜在冲突。", "confirmRemoveButton": "移除域名", "customConfigTitle": "自定义域名配置", "customConfigDescription": "将您自己的域名连接到该项目。项目始终可通过默认的 Biela 域访问，但自定义域可为用户提供专业品牌体验。", "defaultLabel": "默认", "statusActive": "已激活", "statusPending": "待处理", "lastVerifiedText": "刚刚完成验证", "errorInvalidDomain": "请输入有效的域名（例如 example.com）", "errorDuplicateDomain": "此域名已连接到您的项目", "errorAddFail": "添加域名失败。", "successAdd": "域名添加成功！您的域名已连接到此项目。", "benefitsTitle": "域名优势", "benefitSecurityTitle": "增强安全性", "benefitSecurityDesc": "所有自定义域名都自动启用 SSL 证书保护。", "benefitPerformanceTitle": "性能优越", "benefitPerformanceDesc": "全球 CDN 确保项目快速加载，适用于全球用户。", "benefitBrandingTitle": "专业品牌展示", "benefitBrandingDesc": "使用自有域名，打造一致的品牌体验。", "benefitAnalyticsTitle": "分析集成", "benefitAnalyticsDesc": "自定义域名可无缝集成到分析平台中。", "meta": {"index": {"title": "biela.dev | AI驱动的网站与应用构建器 – 通过提示词构建", "description": "使用biela.dev将您的想法转变为实时网站或应用。利用AI驱动的提示轻松构建定制数字产品。"}, "login": {"title": "登录您的biela.dev账户", "description": "访问您的biela.dev控制面板，管理和构建您的AI生成项目。"}, "register": {"title": "注册biela.dev – 开始使用AI构建", "description": "创建您的biela.dev账户，开始使用AI驱动的提示构建网站和应用。"}, "dashboard": {"title": "您的项目控制面板 – biela.dev", "description": "管理您的AI构建的网站和应用，编辑实时项目，并跟踪您的构建历史—全部集中在一处。"}, "profile": {"title": "你的个人资料 – biela.dev", "description": "查看和更新你的 biela.dev 账户信息，管理偏好设置，个性化你的 AI 开发体验。"}, "billing": {"title": "账单 – biela.dev", "description": "添加信用卡以验证身份并解锁 biela.dev 的全部功能 —— 不会产生任何费用。"}}, "transferProject": "共享副本", "transferSecurityNoteDescription": "接收者将获得该项目副本及其所有相关资源的完整访问权限。您仍将保留对原始项目的访问权限。", "transferProjectDescription": "输入您希望转移此项目副本的用户名称或电子邮件。", "transferProjectLabel": "用户名或电子邮件", "transferProjectPlaceholder": "johns<PERSON> 或 <EMAIL>", "transferButton": "转移", "transferSecurityNote": "安全提示：", "dontHavePermisionToTransfer": "您没有权限转移此项目", "transferProjectUserNotFound": "未找到用户 {{ user }}！", "transferErrorOwnAccount": "你不能将项目转移到你自己的账户。", "transferError": "转移项目时出错", "transferSuccess": "成功转移给 {{ user }}", "enterValidEmailUsername": "请输入用户名或电子邮件", "enterMinValidEmailUsername": "请输入有效的用户名（至少3个字符）或电子邮件地址", "youWillStillHaveAccess": "您仍将可以访问原始项目", "newChangesWillNotAffect": "新的更改不会影响其他用户的项目"}