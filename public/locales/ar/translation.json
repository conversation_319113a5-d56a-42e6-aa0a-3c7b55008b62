{"whatWouldYouLikeToBuild": "حوّل فكرتك إلى موقع ويب أو تطبيق حي خلال دقائق", "whatWouldYouLikeToBuildSubtitle": "إذا استطعت <1>تخيل</1> ذلك، يمكنك <5>برمجته</5>.", "fromIdeaToDeployment": "ابدأ مجانًا. برمج أي شيء. حوّل مهاراتك إلى فرصة مع كل مطالبة.", "codePlaceholder": "إذا استطعت تخيل ذلك، يمكن لـ BIELA برمجته، ماذا سنفعل اليوم؟", "defaultPlaceholder": "كيف يمكنني مساعدتك اليوم؟ دعنا نبدع شيئًا رائعًا معًا", "checkingFeatures": "جارٍ التحقق من الميزات", "checklists": "قوائم المراجعة", "runUnitTestsSuggestionTitle": "اقتراح", "runUnitTestsSuggestionMessage": "هل ترغب في تشغيل اختبارات الوحدة لمشروعك؟", "runUnitTestsPrimaryButton": "تشغيل اختبارات الوحدة", "runUnitTestsSecondaryButton": "إلغاء", "createDatabaseTitle": "إنشاء قاعدة البيانات", "createDatabaseMessage": "هل ترغب في إنشاء قاعدة بيانات لمشروعك؟", "createDatabasePrimaryButton": "إنشاء قاعدة البيانات", "createDatabaseSecondaryButton": "إلغاء", "extendedThinking": "التفكير المتعمق", "extendedThinkingTooltip": "تمكين الذكاء الاصطناعي من التفكير بعمق قبل الرد", "firstResponseOnly": "الرد الأول فقط", "always": "دائمًا", "attachFile": "إرفاق ملف", "voiceInput": "إد<PERSON>ال صوتي", "selectLanguage": "حد<PERSON> اللغة لإدخال الصوت", "languageSelectionDisabled": "تعطيل اختيار اللغة أثناء التسجيل", "notAvailableInFirefox": "هذه الميزة غير متاحة في Firefox، ولكنها متاحة في Chrome وSafari", "enhancePrompt": "تحسين المطالبة", "cleanUpProject": "تنظيف المشروع", "showPrompt": "عرض التلميح", "hidePrompt": "إخفاء التلميح", "sendButton": "إرسال", "abortButton": "إلغاء", "inspirationTitle": "تحتاج إلى إلهام؟ جرب أحد هذه الخيارات:", "cleanUpPrompt": "نظّف المشروع عن طريق التأكد من أن لا يتجاوز أي ملف 300 سطر من الكود. قم بإعادة هيكلة الملفات الكبيرة إلى مكونات أصغر ونمطية مع الحفاظ على الوظائف الكاملة. حدّد واحذف كل الملفات، الكود، المكونات والبيانات الزائدة التي لم تعد ضرورية. تأكد من أن جميع المكونات تظل متصلة بشكل صحيح وتعمل دون انقطاع، لتفادي أي تعطيل للنظام الحالي. حافظ على سلامة الكود من خلال التأكد من أن أي تعديل لا يُدخل أخطاء أو يُفسد الوظائف الحالية. الهدف هو تحسين المشروع من حيث الكفاءة، سهولة الصيانة والوضوح.", "checklistPrompt": "راجع طلبي الأولي، وفهم الهد<PERSON> خطوة بخطوة، وأنشئ لي قائمة مراجعة بعلامة خضراء لكل ما تم إنجازه وعلامة حمراء لما تبقى.", "personalPortfolioIdea": "إنشاء موقع ويب لمحفظة شخصية بموضوع داكن", "recipeFinderIdea": "إنشاء تطبيق للعثور على الوصفات يقترح الوجبات بناءً على المكونات", "weatherDashboardIdea": "تصميم لوحة تحكم للطقس مع خلفيات متحركة", "habitTrackerIdea": "تطوير متتبع للعادات مع عرض التقدم", "loading": "جارٍ التحميل", "error": "خطأ", "succes": "نجاح!", "tryAgain": "حاول مرة أخرى", "dashboard": "لوحة التحكم", "getStartedTitle": "ابد<PERSON>", "getStartedSub": "استكشف كيف يعمل Biela.dev", "createProject": "إنشاء مشروع جديد", "createProjectSub": "ابد<PERSON> من الصفر", "uploadProject": "رفع المشروع", "uploadProjectSub": "استيراد مشروع موجود", "importChat": "استيراد المحادثة", "importChatSub": "استيراد المحادثة للمشروع", "createFolder": "إنشاء مجلد جديد", "createFolderSub": "نظّم مشاريعك", "editProjectName": "تعديل اسم المشروع", "editName": "تعديل الاسم", "cancel": "إلغاء", "changeFolder": "تغيير المجلد", "save": "<PERSON><PERSON><PERSON>", "importing": "جارٍ الاستيراد...", "importFolder": "استيراد المجلد", "giveTitle": "أعطِ عنوانًا", "projects": "المشاريع", "searchProjects": "ابحث عن المشاريع...", "becomeAffiliate": "كن شريكًا", "exclusiveGrowth": "مزايا نمو حصرية", "lifetimeEarnings": "أر<PERSON><PERSON><PERSON> مدى الحياة", "highCommissions": "عمولات عالية", "earnCommission": "اكسب 50% عمولة من أول عملية بيع", "joinAffiliateProgram": "انضم إلى برنامج الشركاء لدينا", "folders": "المجلدات", "organizeProjects": "نظّم مشاريعك حسب الفئة", "createNewFolder": "إنشاء مجلد جديد", "enterFolderName": "أ<PERSON><PERSON><PERSON> اسم المجلد", "editFolder": "تحرير المجلد", "deleteFolder": "<PERSON><PERSON><PERSON> المج<PERSON>د", "all": "الكل", "webProjects": "مشاريع الويب", "mobilepps": "تطبيقات الهاتف المحمول", "developmentComparison": "مقارنة التطوير", "traditionalVsAI": "تقليدي مقابل الذكاء الاصطناعي", "traditional": "تقليدي", "standardApproach": "النهج القياسي", "developmentCost": "تكلفة التطوير", "developmentTime": "مدة التطوير", "costSavings": "توفير التكاليف", "reducedCosts": "تكاليف مخفضة", "timeSaved": "الوقت الموفر", "fasterDelivery": "تسليم أسرع", "bielaDevAI": "Biela.dev الذكاء الاصطناعي", "nextGenDevelopment": "تطوير الجيل القادم", "developmentCosts": "تكاليف التطوير", "openInGitHub": "ا<PERSON><PERSON><PERSON><PERSON>", "downloadProject": "تحميل المشروع", "duplicateProject": "نسخ المشروع", "openProject": "فتح المشروع", "deleteProject": "حذ<PERSON> المشروع", "confirmDelete": "هل تريد حذف هذا المجلد؟", "invoicePreview": "معاينة الفاتورة", "settings": {"title": "الإعدادات", "deployment": {"AdvancedSettings": {"advanced-settings": "الإعدادات المتقدمة", "configure-advanced-deployment-options": "تكوين خيارات النشر المتقدمة", "server-configuration": "إعدادات الخادم", "memory-limit": "ح<PERSON> الذاكرة", "region": "المنطقة", "security-settings": "إعدادات الأمان", "enable-ddos-protection": "تفعيل حماية DDoS", "protect-against-distributed": "الحماية ضد هجمات الحرمان من الخدمة الموزعة", "ip-whitelisting": "القائمة البيضاء لعناوين IP", "restrict-acces-to": "تقييد الوصول لعناوين IP معينة", "deployment-options": "خيارات النشر", "auto-deploy": "النشر التلقائي", "automatically-deploy-when": "النشر التلقائي عند الدفع إلى الفرع الرئيسي", "preview-deployments": "معاينة النشر", "create-preview-deployments": "إنشاء نشرات معاينة لطلبات السحب"}, "BuildSettings": {"build-and-deployment-settings": "إعدادات البناء والنشر", "build-command": "<PERSON><PERSON><PERSON> البناء", "override": "تجاوز", "output-directory": "دليل الإخراج", "override2": "تجاوز"}, "DatabaseConfiguration": {"database-configuration": "إعداد قاعدة البيانات", "configure-your-db-connections": "قم بتكوين اتصالات وإعدادات قاعدة البيانات الخاصة بك", "database-type": "نوع قاعدة البيانات", "connection-string": "سلسلة الاتصال", "your-db-credentials": "بيانات اعتماد قاعدة البيانات الخاصة بك مشفرة ومخزنة بأمان", "database-settings": "إعدادات قاعدة البيانات", "pool-size": "حجم التجمع", "require": "يتطلب", "prefer": "يفضل", "disable": "تعطيل", "add-database": "إضافة قاعدة بيانات"}, "DomainSettings": {"domain-settings": "إعدادات النطاق", "configure-your-custom-domain": "قم بتكوين النطاقات المخصصة وشهادات SSL الخاصة بك", "custom-domain": "النطاق المخصص", "add": "إضافة", "ssl-certificate": "شهادة SSL", "auto-renew-ssl-certificates": "تجديد شهادات SSL تلقائيًا", "auto-renew-before-expiry": "تجديد شهادات SSL تلقائيًا قبل انتهاء صلاحيتها", "force-https": "فرض استخدام HTTPS", "redirect-all-http-traffic": "إعادة توجيه كل حركة مرور HTTP إلى HTTPS", "active-domain": "النطاقات النشطة", "remove": "إزالة"}, "EnvironmentVariables": {"environment-variables": "متغيرات البيئة", "configure-environment-variables": "قم بتكوين متغيرات البيئة لعمليات النشر الخاصة بك", "all-enviroments": "جميع البيئات", "environment-variables2": "متغيرات البيئة", "preview": "معاينة", "development": "التطوير", "create-new": "إنشاء جديد", "key": "المفتاح", "value": "القيمة", "save-variable": "<PERSON><PERSON><PERSON> المتغير", "you-can-also-import": "يمكنك أيضًا استيراد المتغيرات من ملف .env:", "import-env-file": "استيراد ملف .env"}, "ProjectConfiguration": {"project-configuration": "تكوين المشروع", "config-your-project-settings": "قم بتكوين إعدادات مشروعك وخيارات النشر", "project-url": "رابط المشروع", "framework": "الإطار", "repo": "المستودع", "branch": "الفرع", "main": "الرئيسي", "development": "التطوير", "staging": "الإعداد التجريبي"}}, "identity": {"unverified": {"title": "تحقق الهوية", "description": "تحقق من هويتك لاستخدام Biela.dev", "subtitle": "عملية تحقق آمنة", "processServers": "يتم تخزين جميع معلومات البطاقة بأمان على خوادم Stripe، وليس على خوادم Biela", "processCharge": "لن يتم خصم أي مبلغ من بطاقتك دون موافقتك الصريحة على الاشتراك", "processBenefits": "Biela.dev مجاني تمامًا للحسابات التي تم التحقق منها حتى 15 مايو 2025", "verifyStripe": "تحقق باستخدام بطاقة ائتمان أو خصم", "verifyStripeDescription": "قم بتوصيل طريقة الدفع الخاصة بك للتحقق", "verifyNow": "تحقق الآن"}, "verified": {"title": "تم التحقق من الهوية", "description": "تم التحقق من هويتك بنجاح", "paymentMethod": "طريقة الدفع", "cardEnding": "البطاقة تنتهي بـ", "updatePayment": "تحديث طريقة الدفع", "untilDate": "حتى 15 مايو 2025", "freeAccess": "وصول مجاني", "freeAccessDescription": "استمتع بالوصول الكامل إلى Biela.dev دون تكلفة", "secureStorage": "تخزين آمن", "secureStorageDescription": "يتم تخزين معلومات بطاقتك بأمان على خوادم Stripe، وليس على خوادم Biela. لن يتم خصم أي مبلغ من بطاقتك دون موافقتك الصريحة على الاشتراك.", "subscriptionAvailable": "ستكون الاشتراكات متاحة اعتبارًا من 15 مايو 2025."}, "connectingToStripe": "جاري الاتصال بـ Stripe..."}, "tabs": {"billing": "الفواتير", "profile": "الملف الشخصي", "deployment": "النشر", "identity": "الهوية"}}, "help": {"title": "كيف يمكننا مساعدتك؟", "searchPlaceholder": "ابحث في الوثائق...", "categories": {"getting-started": {"title": "المبتدئين", "description": "تعرف على أساسيات استخدام Biela.dev", "articles": ["دليل البدء السريع", "نظرة عامة على المنصة", "إنشاء مشروعك الأول", "فهم تطوير الذكاء الاصطناعي"]}, "ai-development": {"title": "تطوير الذكاء الاصطناعي", "description": "إتقان التطوير المدعوم بالذكاء الاصطناعي", "articles": ["كيفية كتابة التعليمات الفعالة", "أفضل الممارسات في توليد الشفرة", "نصائح لتصحيح أخطاء الذكاء الاصطناعي", "ميزات الذكاء الاصطناعي المتقدمة"]}, "project-management": {"title": "إدارة المشاريع", "description": "نظم وادِر مشاريعك", "articles": ["هيكل المشروع", "التعاون الجماعي", "التحكم في الإصدارات", "خيارات النشر"]}}, "channels": {"docs": {"name": "الوثائق", "description": "أدلة شاملة ومراجع API"}, "community": {"name": "المجتمع", "description": "تواصل مع مطورين آخرين"}, "github": {"name": "GitHub", "description": "أب<PERSON>غ عن المشاكل وساهم في التطوير"}}, "support": {"title": "هل تحتاج إلى مساعدة إضافية؟", "description": "فريق الدعم لدينا متاح على مدار الساعة طوال أيام الأسبوع لمساعدتك في أي استفسار أو مشكلة تواجهها.", "button": "اتصل بالدعم"}}, "getStarted": {"title": "اكتشف كيف يعمل Biela.dev", "description": "أنشئ تطبيقك خلال دقائق باستخدام Biela.dev. يقوم الذكاء الاصطناعي لدينا بأتمتة عملية التطوير بأكملها، من الإعداد إلى النشر. إليك كيف يقوم ذكائنا الاصطناعي ببناء تطبيقك بكل سهولة!", "features": {"docs": {"title": "وثائق المطورين", "description": "تعلم كيفية استخدام Biela.dev من خلال أدلة سهلة المتابعة، ونصائح وأفضل الممارسات. مثالية للمبتدئين والمطورين ذوي الخبرة على حد سواء!", "cta": "استكشف وثائق المطورين"}, "support": {"title": "الدعم والملاحظات", "description": "إذا كنت بحاجة إلى مساعدة أو لديك ملاحظات، فاتصل بفريق الدعم وساعدنا في تحسين Biela.dev!", "cta": "أرسل ملاحظاتك"}, "platform": {"title": "ميزات المنصة", "description": "اكتشف الأدوات القوية التي يقدمها Biela.dev لمساعدتك في إنشاء المواقع والتطبيقات دون عناء. دع الذكاء الاصطناعي يقوم بالبرمجة نيابةً عنك!", "cta": "استكشف الميزات"}}, "video": {"title": "فيديو البدء السريع", "description": "شاهد كيف يقوم Biela.dev ببناء تطبيقك – إنشاء التطبيقات والمواقع بكل سهولة!", "cta": "شاهد الدرس التعليمي"}, "guide": {"title": "دليل البدء السريع", "steps": {"setup": {"title": "قم بإعداد مشروعك على الفور", "description": "جهز بيئة التطوير الخاصة بك في ثوانٍ"}, "generate": {"title": "قم بتوليد كود متكامل باستخدام الذكاء الاصطناعي", "description": "دع الذكاء الاصطناعي يكتب لك شفرة جاهزة للإنتاج"}, "features": {"title": "توليد الميزات على الفور", "description": "أضف ميزات معقدة باستخدام أوامر بسيطة"}, "editor": {"title": "المحرر بدون كود وذو الكود المنخفض", "description": "حرر تطبيقك بصريًا أو من خلال الشفرة"}, "optimize": {"title": "قم بتحسين واختبر في الوقت الحقيقي", "description": "تأكد من أن تطبيقك يعمل بشكل مثالي"}, "deploy": {"title": "نشر بنقرة واحدة", "description": "اجعل تطبيقك متاحًا على الإنترنت فورًا باستخدام النشر التلقائي"}}}, "faq": {"title": "الأسئلة الشائعة", "questions": {"what": {"question": "ما هو Biela.dev؟", "answer": "Biela.dev هي منصة مدعومة بالذكاء الاصطناعي تساعدك على إنشاء مواقع وتطبيقات حتى وإن لم تكن تتقن البرمجة. تقوم بأتمتة عملية التطوير بأكملها، بدءًا من كتابة الشفرة وحتى النشر."}, "experience": {"question": "هل أحتا<PERSON> إلى خبرة برمجية لاستخدام Biela.dev؟", "answer": "لا! تم تصميم Biela.dev لتناسب كل من المبتدئين والمطورين ذوي الخبرة. يمكنك البناء بمساعدة الذكاء الاصطناعي أو تعديل الشفرة المولدة حسب الحاجة."}, "projects": {"question": "ما هي أنواع المشاريع التي يمكنني إنشاؤها؟", "answer": "يمكنك إنشاء مواقع إلكترونية، تطبيقات ويب، تطبيقات جوال، منصات SaaS، متاجر إلكترونية، لوحات إدارة، والمزيد."}, "edit": {"question": "هل يمكنني تعديل الشفرة التي يولدها Biela.dev؟", "answer": "نعم! تتيح لك Biela.dev تعديل الشفرة التي يولدها الذكاء الاصطناعي أو استخدام المحرر بدون كود/بكود لإجراء التعديلات بسهولة."}, "deployment": {"question": "كيف يعمل النشر؟", "answer": "بنقرة واحدة، تقوم Biela.dev بنشر مشروعك، مما يجعله متاحًا على الإنترنت وجاهزًا للاستخدام. لا حاجة لإعداد الخادم يدويًا!"}, "pricing": {"question": "هل Biela.dev مجانية؟", "answer": "تقدم Biela.dev خطة مجانية بميزات أساسية. للحصول على أدوات وموارد أكثر تطورًا، يمكنك الترقية إلى الخطة المميزة."}, "integrations": {"question": "هل يمكنني دمج أدوات أو قواعد بيانات خارجية؟", "answer": "نعم! تدعم Biela.dev الدمج مع قواعد البيانات الشهيرة (MongoDB، Firebase، Supabase) ومع واجهات برمجة التطبيقات الخاصة بالأطراف الثالثة."}, "help": {"question": "أين يمكنني الحصول على المساعدة إذا واجهت مشكلة؟", "answer": "يمكنك الرجوع إلى وثائق المطورين، دليل البدء السريع، أو الاتصال بالدعم عبر مركز المساعدة الخاص بنا."}}}, "cta": {"title": "هل أنت مستعد للبدء؟", "description": "أنشئ مشروعك الأول مع Biela.dev واختبر مستقبل التطوير.", "button": "إنشاء مشروع جديد"}}, "confirmDeleteProject": "تأكيد حذف المشروع", "confirmRemoveFromFolder": "تأكيد الإزالة من المجلد", "deleteProjectWarning": "هل أنت متأكد أنك تريد حذف {{projectName}} نهائيًا؟", "removeFromFolderWarning": "هل أنت متأكد أنك تريد إزالة {{projectName}} من {{folderName}}؟", "confirm": "تأكيد", "confirmDeleteFolder": "ت<PERSON><PERSON>يد حذف المجلد", "deleteFolderWarning": "هل أنت متأكد أنك تريد حذف {{folderName}}؟ لا يمكن التراجع عن هذا الإجراء.", "folderDeletedSuccessfully": "تم حذف الم<PERSON><PERSON>د بنجاح", "downloadChat": "تنزيل الدردشة", "inactiveTitle": "علامة التبويب هذه غير نشطة", "inactiveDescription": "انقر على الزر أدناه لجعل هذه علامة التبويب النشطة ومتابعة استخدام التطبيق.", "inactiveButton": "استخدم هذه العلامة", "suggestions": {"weatherDashboard": "أنشئ لوحة بيانات الطقس", "ecommercePlatform": "أنشئ منصة للتجارة الإلكترونية", "socialMediaApp": "صمم تطبيقاً لوسائل التواصل الاجتماعي", "portfolioWebsite": "أنشئ موقع معرض أعمال", "taskManagementApp": "أنشئ تطبيق إدارة المهام", "fitnessTracker": "أنشئ متتبع للياقة البدنية", "recipeSharingPlatform": "صمم منصة لمشاركة الوصفات", "travelBookingSite": "أنشئ موقع حجز السفر", "learningPlatform": "أنشئ منصة تعليمية", "musicStreamingApp": "صمم تطبيق بث الموسيقى", "realEstateListing": "أنشئ قائمة عقارية", "jobBoard": "أنشئ موقع للوظائف"}, "pleaseWait": "يرجى الانتظار...", "projectsInAll": "المشاريع في الكل", "projectsInCurrentFolder": "المشاريع في {{folderName}}", "createYourFirstProject": "أنشئ مشروعك الأول", "startCreateNewProjectDescription": "ابدأ في بناء شيء مذهل. سيتم عرض مشاريعك هنا بمجرد إنشائها.", "createProjectBtn": "مشروع جديد", "publishedToContest": "تم النشر في المسابقة", "publishToContest": "انشر في المسابقة", "refreshSubmission": "تحديث الإرسال", "contestInformation": "معلومات المسابقة", "selectFolder": "اختر مجلدًا", "folder": "مج<PERSON><PERSON>", "selectAFolder": "اختر مجلدًا", "thisProject": "هذا المشروع", "projectDeletedSuccessfully": "تم حذف المشروع بنجاح", "projectRemovedFromFolder": "تمت إزالة المشروع من المجلد", "unnamedProject": "مشروع بدون اسم", "permanentDeletion": "حذف دائم", "removeFromFolder": "إزالة من {{folderName}}", "verifiedAccount": "حساب موثق", "hasSubmittedAMinimumOfOneProject": "قدم مشروعًا واحدًا على الأقل", "haveAtLeastActiveReferrals": "لديك على الأقل {{number}} إحالات نشطة", "GoToAffiliateDashBoard": "اذهب إلى لوحة تحكم الشركاء", "LikeOneProjectThatBelongsToAnotherUser": "أعجب بمشروع واحد يخص مستخدمًا آخر", "GoToContestPage": "اذه<PERSON> إلى صفحة المسابقة", "ContestStatus": "حالة المسابقة", "ShowcaseYourBestWork": "اعرض أفضل أعمالك", "submissions": "الإرسالات", "qualifyConditions": "شروط التأهل", "completed": "مكتمل", "collapseQualifyConditions": "طي شروط التأهل", "expandQualifyConditions": "توسيع شروط التأهل", "basicParticipation": "مشاركة أساسية", "complete": "مكتمل", "incomplete": "<PERSON>ير مكتمل", "forTop3Places": "لأفضل 3 مراكز", "deleteSubmission": "<PERSON>ذ<PERSON> الإرسال", "view": "<PERSON><PERSON><PERSON>", "submitMoreProjectsToIncreaseYourChangesOfWining": "قدم المزيد من المشاريع لزيادة فرصك في الفوز!", "removeFromContest": "إزالة من المسابقة؟", "removeFromContestDescription": "هل أنت متأكد أنك تريد إزالة <project>{{project}}</project> من المسابقة؟ لا يمكن التراجع عن هذا الإجراء.", "cardVerificationRequired": "التحقق من البطاقة مطلوب", "pleaseVerifyCard": "يرجى التحقق من بطاقتك للمتابعة", "unlockFeaturesMessage": "لفتح الوصول الكامل إلى جميع ميزات المنصة، نطلب منك التحقق من بطاقتك. هذه العملية آمنة تمامًا وتضمن تجربة سلسة على Biela.dev. لن يتم خصم أي رسوم من بطاقتك أثناء التحقق.", "freeVerificationNotice": "فوائد التحقق", "accessToAllFeatures": "وصول كامل إلى ميزات Biela.dev.", "enhancedFunctionality": "أداء محسّن للمنصة.", "quickSecureVerification": "عملية تحقق آمنة ومشفرة.", "noCharges": "*لا توجد رسوم أو تكاليف مخفية", "verificationOnly": " — للتحقق فقط.", "verifyNow": "تحقق الآن", "DropFilesToUpload": "إسقاط الملفات لتحميلها", "projectInfo": {"information": "معلومات المشروع", "type": "نوع المشروع", "complexity": "التعقيد", "components": "المكونات", "features": "الميزات", "confidenceScore": "درجة الثقة", "estimationUncertainty": "عدم اليقين في التقدير", "keyTechnologies": "التقنيات الرئيسية"}, "projectMetrics": {"teamComposition": "تكوين الفريق", "hoursBreakdown": "تفاصيل الساعات", "timeToMarket": "الوقت للوصول إلى السوق", "maintenance": "الصيانة", "aiPowered": "مدعوم بالذكاء الاصطناعي", "developerLevel": "مستوى المطور", "nextGenAI": "الذكاء الاصطناعي الجيل التالي", "keyBenefits": "الفوائد الرئيسية", "instantDevelopment": "التطوير الفوري", "noMaintenanceCosts": "لا توجد تكاليف صيانة", "highConfidence": "ثقة عالية", "productionReadyCode": "كود جاهز للإنتاج", "immediate": "فوري", "uncertainty": "عد<PERSON> اليقين", "minimal": "قليل"}, "loadingMessages": {"publish": ["جلب معلومات المشروع...", "أخذ لقطة شاشة...", "إنشاء ملخص ووصف..."], "refresh": ["استرجاع بيانات التقديم الحالية...", "تحديث لقطة الشاشة للمشروع...", "تحديث تفاصيل المشروع..."]}, "errorMessages": {"publish": "<PERSON>ير قادر على نشر \"{{projectName}}\" في المسابقة. كان هناك مشكلة في معالجة طلبك.", "refresh": "<PERSON>ير قادر على تحديث التقديم \"{{projectName}}\". لم يتمكن الخادم من تحديث معلومات المشروع."}, "actions": {"refresh": {"title": "تحديث التقديم", "successMessage": "تم تحديث تقديم مشروعك بنجاح.", "buttonText": "عرض التقديم المحدث"}, "publish": {"title": "نشر في المسابقة", "successMessage": "تم إرسال مشروعك إلى المسابقة.", "buttonText": "عرض في صفحة المسابقة"}}, "SupabaseConnect": "الاتصال بـ Supabase", "SupabaseDashboard": "لوحة تحكم Supabase", "RestoreApp": "استعادة", "SaveApp": "<PERSON><PERSON><PERSON>", "ForkChat": "تفرع", "BielaTerminal": "م<PERSON><PERSON><PERSON> Biela", "UnitTesting": "اختبار الوحدة", "InstallDependencies": "تثبيت التبعيات", "InstallDependenciesDescription": "يقوم بتثبيت جميع الحزم المطلوبة للمشروع باستخدام", "BuildProject": "بناء المشروع", "BuildProjectDescription": "يتم تجميع وتحسين المشروع للإنتاج باستخدام", "StartDevelopment": "بدء التطوير", "StartDevelopmentDescription": "يشغ<PERSON> خادم التطوير لمعاينة مباشرة باستخدام", "NoProjectFound": "لم يتم العثور على مشروع بهذا الاسم.", "NoProjectFoundDescription": " يرجى التحقق من وجود أخطاء إملائية والمحاولة مرة أخرى.", "ContextLimitReached": "تم الوصول إلى حد السياق", "ClaudeContextDescription1": "لقد وصلت إلى سعة السياق القصوى لهذا المشروع باستخدام Claude. لا تقلق - يمكننا المتابعة باستخدام نموذج Gemini الذي يتمتع بنافذة سياق أكبر بكثير.", "ClaudeContextDescription2": "تقدم نماذج Gemini سعة سياق تصل إلى 5 أضعاف، مما يتيح لك الاحتفاظ بجميع الشيفرات وسجل المحادثات ومتابعة بناء مشروعك دون انقطاع.", "SelectModelToContinue": "اختر نموذجًا للمتابعة:", "Performance": "الأداء", "AlternativeLimitExplanation": "يبدو أن الذكاء الاصطناعي وصل إلى حد المعالجة لهذا المشروع. معظم المساحة يتم استخدامها بواسطة الملفات المستوردة، وليس المحادثة نفسها.", "SuggestedSolutions": "الحلول المقترحة:", "ReimportProject": "إعادة الاستيراد كمشروع جديد", "ReimportProjectDescription": "سيؤدي ذلك إلى مسح سجل المحادثة وتحرير بعض مساحة السياق، مع الحفاظ على ملفاتك.", "BreakIntoProjects": "تقسيم المشروع إلى مشاريع متعددة", "BreakIntoProjectsDescription": "قسّم عملك إلى مكونات أصغر يمكن تطويرها بشكل منفصل.", "ExportWork": "تصدير العمل المكتمل", "ExportWorkDescription": "قم بتنزيل وأرشفة الملفات المكتملة لتحرير مساحة السياق.", "AlternativeContextNote": "للاستفادة القصوى من السياق المتاح، فكر في إزالة أي ملفات أو مكتبات غير مستخدمة والتركيز على الملفات الأساسية المطلوبة للمرحلة الحالية من التطوير.", "ContinueWithSelectedModel": "المتابعة باستخدام النموذج المحدد", "Close": "إغلاق", "AIModel": "نموذج الذكاء الاصطناعي", "Active": "نشط", "Stats": "الإحصائيات", "Cost": "التكلفة", "ExtendedThinkingDisabledForModel": "غير متاح مع هذا النموذج", "ExtendedThinkingAlwaysOn": "دائم التشغيل مع هذا النموذج", "limitReached": "لقد وصلت إلى الحد الأقصى الخاص بك!", "deleteProjectsSupabase": "يرجى حذف بعض المشاريع أو زيادة حدك في Supabase.", "goTo": "ا<PERSON><PERSON><PERSON>", "clickProjectSettings": " انقر على المشروع، إعدادات المشروع، مرر إلى الأسفل ثم انقر", "delete": "<PERSON><PERSON><PERSON>", "retrying": "جارٍ المحاولة…", "retryConnection": "إعادة محاولة الاتصال", "RegisterPageTitle": "التسجيل – biela.dev", "RegisterPageDescription": "أنشئ حسابك على biela.dev وفعّل جميع الميزات.", "SignUpHeading": "تسجيل حساب", "AlreadyLoggedInRedirectHome": "أنت مسجل الدخول بالفعل! جاري التحويل إلى الصفحة الرئيسية...", "PasswordsMismatch": "كلمتا المرور غير متطابقتين.", "FirstNameRequired": "الاسم الأول مطلوب", "LastNameRequired": "اسم العائلة مطلوب", "UsernameRequired": "اسم المستخدم مطلوب", "EmailRequired": "البريد الإلكتروني مطلوب", "EmailInvalid": "ير<PERSON>ى إدخا<PERSON> بريد إلكتروني صالح", "TooManyRequests": "عدد كبير جدًا من الطلبات، يرجى المحاولة لاحقًا", "SomethingWentWrongMessage": "حدث خطأ ما، يرجى المحاولة لاحقًا", "PasswordRequired": "كلمة المرور مطلوبة", "ConfirmPasswordRequired": "يرجى تأكيد كلمة المرور", "AcceptTermsRequired": "يجب الموافقة على شروط الخدمة وسياسة الخصوصية", "CaptchaRequired": "ير<PERSON>ى إكمال اختبار CAPTCHA", "RegistrationFailed": "فشل في التسجيل", "EmailConfirmationSent": "تم إرسال بريد تأكيد! يرجى تأكيد البريد الإلكتروني ثم تسجيل الدخول.", "RegistrationServerError": "فشل في التسجيل (ال<PERSON><PERSON><PERSON> أعاد false).", "SomethingWentWrong": "حد<PERSON> خطأ ما", "CheckEmailHeading": "تحقق من بريدك الإلكتروني لتأكيد التسجيل", "CheckEmailDescription": "لقد أرسلنا لك رسالة تحتوي على رابط تأكيد.", "GoToHomepage": "اذه<PERSON> إلى الصفحة الرئيسية", "ReferralCodeOptional": "رمز الإحالة (اختياري)", "EnterReferralCode": "أدخل رمز الإحالة إذا تمت دعوتك من قبل مستخدم آخر.", "PasswordPlaceholder": "كلمة المرور", "ConfirmPasswordPlaceholder": "تأكيد كلمة المرور", "CreateAccount": "إنشاء حساب", "AlreadyHaveAccountPrompt": "هل لديك حساب بالفعل؟", "Login": "تسجيل الدخول", "AcceptTermsPrefix": "أ<PERSON><PERSON><PERSON><PERSON> على", "TermsOfService": "شروط الخدمة", "AndSeparator": "و", "PrivacyPolicy": "سياسة الخصوصية", "LoginPageTitle": "تسجيل الدخول – biela.dev", "LoginPageDescription": "قم بالوصول إلى حسابك أو سجل الدخول إلى biela.dev لاستخدام جميع الميزات.", "LogInHeading": "تسجيل الدخول", "EmailOrUsernamePlaceholder": "البريد الإلكتروني / اسم المستخدم", "ForgotPassword?": "هل نسيت كلمة المرور؟", "LoginToProfile": "تسجيل الدخول إلى ملفك الشخصي", "UserNotConfirmed": "الحساب غير مؤكد", "ConfirmEmailNotice": "يجب تأكيد بريدك الإلكتروني لتفعيل الحساب.", "ResendConfirmationEmail": "إعادة إرسال رسالة التأكيد", "ResendConfirmationSuccess": "تمت إعادة إرسال رسالة التأكيد! يرجى التحقق من بريدك.", "ResendConfirmationError": "فشل في إعادة إرسال رسالة التأكيد.", "LoginSuccess": "تم تسجيل الدخول بنجاح! جاري التحويل...", "LoginFailed": "فشل في تسجيل الدخول", "LoginWithGoogle": "تسجيل الدخول باستخدام Google", "LoginWithGitHub": "تسجيل الدخول باستخدام GitHub", "SignUpWithGoogle": "سجّل باستخدام Google", "SignUpWithGitHub": "سجّل باستخدام GitHub", "Or": "أو", "NoAccountPrompt": "ليس لديك حساب؟", "SignMeUp": "سجلني", "ForgotPasswordPageTitle": "نسيت كلمة المرور – biela.dev", "ForgotPasswordPageDescription": "أعد تعيين كلمة مرور حسابك على biela.dev واستعد الوصول.", "BackToLogin": "العودة إلى تسجيل الدخول", "ForgotPasswordHeading": "نسيت كلمة المرور", "ForgotPasswordDescription": "أدخل بريدك الإلكتروني وسنرسل لك رابط تحقق لإعادة تعيين كلمة المرور.", "VerificationLinkSent": "تم إرسال رابط التحقق! يرجى التحقق من بريدك.", "EnterYourEmailPlaceholder": "أدخل بريدك الإلكتروني", "Sending": "جارٍ الإرسال...", "SendVerificationCode": "إرسال رمز التحقق", "InvalidConfirmationLink": "رابط التأكيد غير صالح", "Back": "عودة", "ResetPassword": "إعادة تعيين كلمة المرور", "ResetPasswordDescription": "أنشئ كلمة مرور جديدة لحسابك", "NewPasswordPlaceholder": "كلمة مرور جديدة", "ConfirmNewPasswordPlaceholder": "تأكيد كلمة المرور الجديدة", "ResetPasswordButton": "إعادة تعيين كلمة المرور", "PasswordRequirements": "يجب أن تتكون كلمة المرور من 8 أحرف على الأقل، وتحتوي على حرف كبير وحرف صغير ورقم ورمز خاص.", "PasswordUpdatedSuccess": "تم تحديث كلمة المرور بنجاح!", "affiliateDashboard": "لوحة تحكم الشركاء", "userDashboard": "لوحة تحكم المستخدم", "returnToAffiliateDashboard": "العودة إلى لوحة تحكم الشركاء", "returnToUserDashboard": "العودة إلى لوحة تحكم المستخدم", "myProfile": "ملفي الشخصي", "viewAndEditYourProfile": "عرض وتحرير ملفك الشخصي", "billing": "الفوترة", "manageYourBillingInformation": "إدارة معلومات الفوترة الخاصة بك", "logout": "تسجيل الخروج", "logoutDescription": "تسجيل الخروج من حسابك", "SupabaseNotAvailable": "Supabase غير متوفر الآن، يرجى المحاولة مرة أخرى لاحقًا.", "projectActions": {"invalidSlug": "معرّف المشروع غير صالح.", "downloadSuccess": "تم تنزيل المشروع بنجاح!", "downloadError": "فشل في تنزيل المشروع.", "exportSuccess": "تم تصدير المحادثة! تحقق من مجلد التنزيلات.", "exportError": "فشل في تصدير المحادثة.", "duplicateSuccess": "تم نسخ المحادثة بنجاح!", "duplicateError": "فشل في نسخ المحادثة."}, "enter_new_phone_number": "أدخل رقم الهاتف الجديد", "enter_new_phone_number_below": "ير<PERSON>ى إدخال رقم هاتفك الجديد أدناه:", "new_phone_placeholder": "رقم الها<PERSON><PERSON> الجديد", "enter_otp_code": "أد<PERSON><PERSON> رمز التحقق", "confirm_phone_message": "لاستخدام حسابك، يجب تأكيد رقم الهاتف. أدخل رمز التحقق المرسل إلى ({{phone}}).", "wrong_phone": "رقم الهاتف غير صحيح؟", "resend_sms": "إعادة إرسال الرسالة", "submit": "إرسال", "tokensAvailable": "الرموز المتاحة", "sectionTitle": "إدارة النطاقات", "addDomainButton": "إضافة اسم نطاق", "connectCustomDomainTitle": "ربط اسم نطاق مخصص", "disclaimer": "تنويه:", "disclaimerText": "لإجراء التحقق بنجاح، يجب عليك إعداد جميع قواعد DNS المذكورة أعلاه بشكل صحيح", "domainInputDescription": "أدخل اسم النطاق الذي تريد ربطه بهذا المشروع.", "domainLabel": "اسم النطاق", "domainPlaceholder": "example.com", "cancelButton": "إلغاء", "continueButton": "إضافة اسم نطاق", "deployingText": "جاري النشر...", "addingText": "جاري الإضافة...", "verifyButtonText": "تحقق", "configureDnsTitle": "تهيئة سجلات DNS", "configureDnsDescription": "أض<PERSON> السجلات التالية إلى نطاقك للتحقق من الملكية وربطه بالمشروع.", "tableHeaderType": "النوع", "tableHeaderName": "الاسم", "tableHeaderValue": "القيمة", "note": "ملاحظة:", "noteText": "قد تستغرق تغييرات DNS ما يصل إلى 48 ساعة للانتشار. ومع ذلك، غالبًا ما تصبح سارية خلال دقائق أو ساعات قليلة.", "backButton": "رجوع", "showDnsButton": "عرض إعدادات DNS", "hideDnsButton": "إخفاء إعدادات DNS", "removeButton": "إزالة", "dnsSettingsTitle": "إعدادات DNS للنطاق", "removeDomainConfirmTitle": "إزالة اسم النطاق", "removeConfirmationText": "هل أنت متأكد أنك تريد إزالة اسم النطاق ", "importantCleanupTitle": "تنظيف DNS مهم", "cleanupDescription": "بعد إزالة هذا النطاق من مشروعك، تذكر أيضًا إزالة سجلات DNS التي أنشأتها أثناء الإعداد. هذا يساعد في الحفاظ على إعدادات DNS نظيفة ويمنع التعارضات المستقبلية.", "confirmRemoveButton": "إزالة اسم النطاق", "customConfigTitle": "تهيئة نطاق مخصص", "customConfigDescription": "اربط نطاقك الخاص بهذا المشروع. سيظل المشروع متاحًا عبر نطاق Biela الافتراضي، ولكن توفر النطاقات المخصصة تجربة علامة تجارية احترافية للمستخدمين.", "defaultLabel": "افتراضي", "statusActive": "نشط", "statusPending": "قيد الانتظار", "lastVerifiedText": "تم التحقق للتو", "errorInvalidDomain": "ير<PERSON>ى إدخال اسم نطاق صالح (مثل example.com)", "errorDuplicateDomain": "اسم النطاق هذا مرتبط بالفعل بمشروعك", "errorAddFail": "فشل في إضافة اسم النطاق.", "successAdd": "تمت إضافة اسم النطاق بنجاح! تم ربطه بالمشروع.", "benefitsTitle": "مزايا النطاق", "benefitSecurityTitle": "<PERSON><PERSON><PERSON> محسن", "benefitSecurityDesc": "جميع النطاقات المخصصة محمية تلقائيًا بشهادات SSL.", "benefitPerformanceTitle": "أداء سريع", "benefitPerformanceDesc": "تضمن شبكة CDN العالمية تحميلًا سريعًا للمشروع للمستخدمين حول العالم.", "benefitBrandingTitle": "علامة تجارية احترافية", "benefitBrandingDesc": "استخدم نطاقك الخاص لتجربة علامة تجارية متناسقة.", "benefitAnalyticsTitle": "تكامل التحليلات", "benefitAnalyticsDesc": "تعمل النطاقات المخصصة بسلاسة مع منصات التحليلات.", "meta": {"index": {"title": "biela.dev | مُنشئ مواقع الويب وتطبيقات ذكاء الاصطناعي – بناء باستخدام تعليمات", "description": "قم بتحويل أفكارك إلى مواقع ويب حية أو تطبيقات باستخدام biela.dev. استخدم تعليمات مدعومة بالذكاء الاصطناعي لبناء منتجات رقمية مخصصة بسهولة."}, "login": {"title": "تسجيل الدخول إلى حسابك biela.dev", "description": "قم بالوصول إلى لوحة تحكم biela.dev الخاصة بك لניהול وبناء مشاريعك المنشأة بواسطة الذكاء الاصطناعي."}, "register": {"title": "التسجيل في biela.dev – البدء في بناء باستخدام الذكاء الاصطناعي", "description": "أنشئ حسابك على biela.dev لبدء بناء مواقع الويب وتطبيقات باستخدام تعليمات مدعومة بالذكاء الاصطناعي."}, "dashboard": {"title": "لوحة تحكم مشاريعك – biela.dev", "description": "إدارة مواقع الويب وتطبيقاتك المنشأة بواسطة الذكاء الاصطناعي، وتحرير مشاريعك الحية، و تتبع تاريخ بنائك – كل شيء في مكان واحد."}, "profile": {"title": "ملفك الشخصي – biela.dev", "description": "اعرض وحدث تفاصيل حسابك في biela.dev، وقم بإدارة التفضيلات، وخصص تجربتك مع تطوير الذكاء الاصطناعي."}, "billing": {"title": "الفوترة – biela.dev", "description": "أضف بطاقتك الائتمانية للتحقق من هويتك والوصول الكامل إلى ميزات biela.dev – لن يتم فرض أي رسوم."}}, "transferProject": "مشاركة نسخة", "transferSecurityNoteDescription": "سيتلقى المستلم حق الوصول الكامل إلى نسخة من هذا المشروع وجميع موارده المرتبطة. ستظل لديك إمكانية الوصول إلى المشروع الأصلي.", "transferProjectDescription": "أدخل اسم المستخدم أو البريد الإلكتروني للشخص الذي تريد نقل نسخة من هذا المشروع إليه.", "transferProjectLabel": "اسم المستخدم أو البريد الإلكتروني", "transferProjectPlaceholder": "johns<PERSON> أو <EMAIL>", "transferButton": "نقل", "transferSecurityNote": "ملاحظة أمان:", "dontHavePermisionToTransfer": "ليس لديك إذن لنقل هذا المشروع", "transferProjectUserNotFound": "المستخدم {{ user }} غير موجود!", "transferErrorOwnAccount": "لا يمكنك نقل مشروع إلى حسابك الخاص.", "transferError": "حد<PERSON> خطأ أثناء نقل المشروع", "transferSuccess": "تم النقل بنجاح إلى {{ user }}", "enterValidEmailUsername": "ير<PERSON>ى إدخال اسم مستخدم أو بريد إلكتروني", "enterMinValidEmailUsername": "ير<PERSON>ى إدخال اسم مستخدم صالح (3 أحرف على الأقل) أو عنوان بريد إلكتروني", "youWillStillHaveAccess": "ستظل قادرًا على الوصول إلى المشروع الأصلي", "newChangesWillNotAffect": "لن تؤثر التغييرات الجديدة على مشروع المستخدم الآخر"}