{"whatWouldYouLikeToBuild": "İdeyanızı dəqiqələr ərzində canlı vebsayt və ya tətbiqə çevirin", "whatWouldYouLikeToBuildSubtitle": "Əgər onu <1>təsəvvür edə bilirsiniz</1>, onu <5>kodlaya bilərsiniz</5>.", "fromIdeaToDeployment": "Pul<PERSON>z başlayın. Hə<PERSON> şeyi kodlaşdırın. Bacarıqlarınızı hər sorğu ilə fürsətə çevirin.", "codePlaceholder": "Əgər xəyal edə bilirsinizsə, BIELA onu kodlaşdıra bilər, bu gün nə edək?", "defaultPlaceholder": "Bu gün sizə necə kömək edə bilərəm? Gəlin birlikdə möhtəşəm bir şey edək", "checkingFeatures": "Xüsus<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yox<PERSON>ır", "checklists": "<PERSON><PERSON><PERSON><PERSON>ı", "runUnitTestsSuggestionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "runUnitTestsSuggestionMessage": "Layihəniz üçün vahid testləri işə salmaq istəyirsinizmi?", "runUnitTestsPrimaryButton": "<PERSON><PERSON><PERSON> işə salın", "runUnitTestsSecondaryButton": "İmtina et", "createDatabaseTitle": "V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createDatabaseMessage": "Layihəniz üçün verilənlər bazası yaratmaq istəyirsinizmi?", "createDatabasePrimaryButton": "Veril<PERSON><PERSON><PERSON><PERSON><PERSON> bazası yaradın", "createDatabaseSecondaryButton": "İmtina et", "extendedThinking": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "extendedThinkingTooltip": "<PERSON>üni intellektin cavab verməzdən əvvəl daha dərindən düşünməsinə icazə verin", "firstResponseOnly": "Yalnız ilk cavab", "always": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachFile": "Fayl əlavə et", "voiceInput": "<PERSON><PERSON><PERSON><PERSON> giriş", "selectLanguage": "<PERSON><PERSON><PERSON><PERSON> giriş <PERSON>ün dili seçin", "languageSelectionDisabled": "<PERSON><PERSON><PERSON> zamanı dil seçimi deaktiv edilib", "notAvailableInFirefox": "Bu funksiya Firefox-da mövcud <PERSON>, lakin Chrome və Safari-də mövcuddur", "enhancePrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> tə<PERSON>", "cleanUpProject": "<PERSON><PERSON>əni təmizlə", "showPrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hidePrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "abortButton": "<PERSON><PERSON>ğ<PERSON> et", "inspirationTitle": "<PERSON>l<PERSON> lazımdır? B<PERSON>lardan birini sınayın:", "cleanUpPrompt": "Layihəni təmizləyin: Heç bir faylın 300 sətirdən çox kod ehtiva etmədiyinə əmin olun. Böyük faylları daha kiçik, modul komponentlərə refaktor edin, eyni funksionallığı qoruyaraq. Artıq istifadə edilməyən faylları, kodu, komponentləri və lazımsız məlumatları müəyyən edib silin. Bütün komponentlərin düzgün əlaqəli və işlək qalmasına diqqət yetirin, mövcud sistemdə kəsintilərə yol verməyin. Kodun bütövlüyünü qoruyun və dəyişikliklərin səhvlərə və ya mövcud xüsusiyyətlərin pozulmasına səbəb olmadığını yoxlayın. Məqsəd layihəni səmərəlilik, asan saxlanılma və aydınlıq üçün optimallaşdırmaqdır.", "checklistPrompt": "<PERSON><PERSON><PERSON> təlimatımı nəzərdən keçirin, məqsədi addım-addım başa düşün və mənim üçün yoxlanma siyahısı hazırlayın: Edilən hər şey üçün yaşıl işarə və hələ görülməsi lazım olanlar üçün qırmızı işarə ilə.", "personalPortfolioIdea": "Qaranlıq mövzulu şəxsi portfel veb saytı yaradın", "recipeFinderIdea": "<PERSON><PERSON><PERSON><PERSON><PERSON> əsaslanaraq yeməkləri təklif edən resept tapma tətbiqi yaradın", "weatherDashboardIdea": "Animasiya fonları ilə hava vəziyyəti paneli dizayn edin", "habitTrackerIdea": "İrəliləyişin vizuallaşdırıldığı vərdiş izləyici yaradın", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>", "succes": "Uğur!", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>əhd E<PERSON>", "dashboard": "Panel", "getStartedTitle": "Başlayın", "getStartedSub": "Biela.dev-in necə işlədiyini kəşf edin", "createProject": "<PERSON><PERSON>", "createProjectSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>n", "uploadProject": "<PERSON><PERSON><PERSON><PERSON>", "uploadProjectSub": "<PERSON><PERSON><PERSON><PERSON><PERSON> layihəni idxal edin", "importChat": "Sohbeti idxal edin", "importChatSub": "Möv<PERSON>d sohbeti idxal edin", "createFolder": "<PERSON><PERSON> qovluq ya<PERSON>ın", "createFolderSub": "Lay<PERSON><PERSON><PERSON><PERSON><PERSON>zi təşkil edin", "editProjectName": "Layihə adını redaktə et", "editName": "Adı redaktə et", "cancel": "<PERSON><PERSON>ğ<PERSON> et", "changeFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>ə<PERSON>", "save": "<PERSON><PERSON>a saxla", "importing": "İdxal olunur...", "importFolder": "Qovluğu idxal edin", "giveTitle": "Başlıq verin", "projects": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "searchProjects": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>n...", "becomeAffiliate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ş olun", "exclusiveGrowth": "Eksklüziv İnkişaf Faydaları", "lifetimeEarnings": "<PERSON><PERSON><PERSON><PERSON>", "highCommissions": "Yüksək Komissiyalar", "earnCommission": "İlk satışınızdan 50% komissiya qazanın", "joinAffiliateProgram": "Tərəfdaş Proqramımıza Qoşulun", "folders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "organizeProjects": "Layihələ<PERSON>zi kateqoriyalara görə təşkil edin", "createNewFolder": "<PERSON><PERSON> qovluq ya<PERSON>ın", "enterFolderName": "Qovluq adını daxil edin", "editFolder": "Qovluğu redaktə edin", "deleteFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>", "all": "Hamısı", "webProjects": "<PERSON><PERSON>", "mobilepps": "<PERSON><PERSON>", "developmentComparison": "İnkişaf <PERSON>", "traditionalVsAI": "Ə<PERSON><PERSON><PERSON><PERSON><PERSON> vs Süni İntellekt", "traditional": "Ən<PERSON><PERSON><PERSON>vi", "standardApproach": "<PERSON><PERSON>", "developmentCost": "İnkişaf Xərcləri", "developmentTime": "İnkişaf Vaxtı", "costSavings": "Xərclərin Azaldılması", "reducedCosts": "Azaldılmış Xərclər", "timeSaved": "<PERSON><PERSON><PERSON><PERSON><PERSON> Edilmiş Vaxt", "fasterDelivery": "<PERSON><PERSON>", "bielaDevAI": "Biela.dev Süni İntellekt", "nextGenDevelopment": "Növbəti Nəsil İnkişaf", "developmentCosts": "İnkişaf Xərcləri", "openInGitHub": "GitHub<PERSON><PERSON><PERSON>n", "downloadProject": "<PERSON><PERSON><PERSON><PERSON>", "duplicateProject": "Layihəni Təkrarlayın", "openProject": "Layihəni Açın", "deleteProject": "<PERSON><PERSON><PERSON><PERSON>", "confirmDelete": "Bu qovluğu silək?", "invoicePreview": "Sizin fakturanız", "settings": {"title": "<PERSON><PERSON><PERSON>", "deployment": {"AdvancedSettings": {"advanced-settings": "Ətraflı Ayarlar", "configure-advanced-deployment-options": "Ətraflı yerləşdirmə seçimlərini tənzimləyin", "server-configuration": "Server <PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memory-limit": "<PERSON><PERSON><PERSON><PERSON>", "region": "Region", "security-settings": "Təhlükəsizlik Ayarları", "enable-ddos-protection": "DDoS Qorumasını aktivləşdirin", "protect-against-distributed": "Dağ<PERSON><PERSON><PERSON><PERSON><PERSON>ş xidməti rədd etmə hücumlarına qarşı qoruyun", "ip-whitelisting": "IP Ağ Siyahısı", "restrict-acces-to": "Xüsusi IP ünvanlarına giriş məhdudlaşdırın", "deployment-options": "Yerl<PERSON>şdirmə Seçimləri", "auto-deploy": "Avtomatik Yerləşdirmə", "automatically-deploy-when": "<PERSON><PERSON><PERSON> buda<PERSON>a göndərərkən avtomatik yerləşdirmə", "preview-deployments": "Əvvəl<PERSON><PERSON><PERSON><PERSON><PERSON>dirm<PERSON><PERSON><PERSON><PERSON>", "create-preview-deployments": "Pull requestlər üçün əvvəlcədən yerləşdirmə yaradın"}, "BuildSettings": {"build-and-deployment-settings": "Yığma və Yerləşdirmə Ayarları", "build-command": "<PERSON><PERSON><PERSON><PERSON>", "override": "Üstünə yaz", "output-directory": "Çıxış Kataloqu", "override2": "Üstünə yaz"}, "DatabaseConfiguration": {"database-configuration": "Verilənlər Bazası Konfiqurasiyası", "configure-your-db-connections": "Verilə<PERSON><PERSON><PERSON>r bazası bağlantılarınızı və ayarlarınızı tənzimləyin", "database-type": "Veri<PERSON>ə<PERSON><PERSON><PERSON><PERSON>ı Növü", "connection-string": "Bağlantı Sətiri", "your-db-credentials": "Verilə<PERSON><PERSON>ər bazası giriş məlumatlarınız şifrələnir və təhlükəsiz saxlanılır", "database-settings": "Verilən<PERSON>ər Bazası Ayarları", "pool-size": "Hovuz Ölçüsü", "require": "<PERSON><PERSON><PERSON><PERSON><PERSON> et", "prefer": "<PERSON>vvəl tut", "disable": "Deaktiv et", "add-database": "Verilən<PERSON>ər Bazası Əlavə Et"}, "DomainSettings": {"domain-settings": "<PERSON><PERSON>", "configure-your-custom-domain": "Xüsusi domenlərinizi və SSL sertifikatlarınızı tənzimləyin", "custom-domain": "<PERSON><PERSON><PERSON><PERSON>", "add": "Əlavə et", "ssl-certificate": "SSL Sertifikatı", "auto-renew-ssl-certificates": "SSL sertifikatlarını avtomatik yenilə", "auto-renew-before-expiry": "Serti<PERSON><PERSON><PERSON>ın müddəti bitmədən avtomatik yenilə", "force-https": "HTTPS-i məcburi et", "redirect-all-http-traffic": "Bütün HTTP trafiki HTTPS-ə yönləndir", "active-domain": "Aktiv <PERSON>", "remove": "Sil"}, "EnvironmentVariables": {"environment-variables": "<PERSON><PERSON><PERSON>", "configure-environment-variables": "Yerləşdirmələriniz üçün ətraf mühit dəyişənlərini tənzimləyin", "all-enviroments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "environment-variables2": "<PERSON><PERSON><PERSON>", "preview": "Əvvəl<PERSON><PERSON><PERSON><PERSON>n baxış", "development": "İnkişaf", "create-new": "<PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save-variable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yadda saxla", "you-can-also-import": ".env faylından da dəyişənləri idxal edə bilərsiniz:", "import-env-file": ".env Faylını İdxal Et"}, "ProjectConfiguration": {"project-configuration": "Layihə Konfiqurasiyası", "config-your-project-settings": "<PERSON><PERSON>ə a<PERSON>larınızı və yerləşdirmə seçimlərinizi tənzimləyin", "project-url": "Layihə URL-i", "framework": "Ç<PERSON><PERSON><PERSON><PERSON>ə", "repo": "<PERSON><PERSON>", "branch": "Budaq", "main": "əsas", "development": "inkişaf", "staging": "staging"}}, "identity": {"unverified": {"title": "Şəxsiyyət Təsdiqi", "description": "Biela.dev istifadə etmək üçün şəxsiyyətinizi təsdiq edin", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z Təsdiq Prosesi", "processServers": "Bütün kart məlumatları Biela-nın serverlərində deyil, Stripe serverlərində təhlükəsiz sa<PERSON>lanılır", "processCharge": "Abunəlik üçün açıq razılığınız olmadan kartınızdan ödəniş alınmayacaq", "processBenefits": "Biela.dev, təsdiq edilmiş hesablar üçün 15 May 2025-ci ilə qədər tamamilə pulsuzdur", "verifyStripe": "Kredit və ya Debit Kart ilə Təsdiqlə", "verifyStripeDescription": "Təsdiq üçün ödəniş üsulunuzu bağlayın", "verifyNow": "İndi Təsdiq Edin"}, "verified": {"title": "Şəxsiyyət Təsdiqləndi", "description": "Şəxsiyyətiniz müvəffəqiyyətlə təsdiqləndi", "paymentMethod": "Ödəniş üsulu", "cardEnding": "<PERSON><PERSON><PERSON>n sonu", "updatePayment": "Ödəniş üsulunu yenilə", "untilDate": "15 May 2025-ci ilə qədər", "freeAccess": "<PERSON><PERSON><PERSON><PERSON>", "freeAccessDescription": "Biela.dev-ə tam daxil olmaq imkanı ilə heç bir xərc ödəmədən faydalanın", "secureStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "secureStorageDescription": "Kart məlumatlarınız Stripe serverlərində təhlükəsiz şəkildə saxlanılır, Biela-nın serverlərində deyil. Abunəlik üçün açıq razılığınız olmadan kartınızdan ödəniş tutulmayacaq.", "subscriptionAvailable": "Abunəliklər 15 may 2025 tarixindən etibarən əlçatan olacaq."}, "connectingToStripe": "Stripe ilə əlaqə qurulur..."}, "tabs": {"billing": "<PERSON><PERSON><PERSON>", "profile": "Profil", "deployment": "Ye<PERSON>əşdirmə", "identity": "Şəxsiyyət"}}, "help": {"title": "Sizə necə kömək edə bilərik?", "searchPlaceholder": "Sənədləşməni axtarın...", "categories": {"getting-started": {"title": "Başlanğıc üçün", "description": "Biela.dev-dən istifadə etməyin əsaslarını öyrənin", "articles": ["Sürətli Başlanğıc Təlimatı", "Platforma Ümumi Baxışı", "<PERSON><PERSON>", "Süni İntellekt İnkişafını Anlamaq"]}, "ai-development": {"title": "Süni İntellekt İnkişafı", "description": "Süni intellektlə gücləndirilən inkişafı mənimsəyin", "articles": ["Effektiv Təkliflərin Yazılması", "Kod Yaradılması Üçün Ən Yaxşı Təcrübələr", "Süni İntellektin Səhvlərinin Aradan Qaldırılması Üçün Məsləhətlər", "Ətraflı Süni İntellekt Xüsusiyyətləri"]}, "project-management": {"title": "<PERSON><PERSON>ə <PERSON>et<PERSON>ə<PERSON>", "description": "Layihələ<PERSON>zi təşkil edin və idarə edin", "articles": ["Layihə Strukturlaşdırması", "Komanda Əməkdaşlığı", "Versiya İdarəetməsi", "Yerl<PERSON>şdirmə Seçimləri"]}}, "channels": {"docs": {"name": "Sənədləşmə", "description": "Ətraflı təlimatlar və API istinadları"}, "community": {"name": "İcma", "description": "<PERSON><PERSON><PERSON><PERSON> inkişaf etdiricilərlə əlaqə qurun"}, "github": {"name": "GitHub", "description": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON>əri bildirin və töhfə verin"}}, "support": {"title": "Hələ də köməyə ehtiyacınız var?", "description": "Dəstək komandamız 24/7 hər hansı sual və ya problem üçün sizə kömək etməyə hazırdır.", "button": "Dəstəyə Müraciət Edin"}}, "getStarted": {"title": "Biela.dev-in necə işlədiyini araşdırın", "description": "Biela.dev ilə tətbiqinizi dəqiqələr ərzində yaradın. Süni intellektimiz quraşdırmadan yerləşdirməyə qədər bütün inkişaf prosesini avtomatlaşdırır. Süni intellektimizin tətbiqinizi asanlıqla necə yaratdığını burada görürsünüz!", "features": {"docs": {"title": "İnkişaf etdirici Sənədləşməsi", "description": "Biela.dev-dən necə istifadə edəcəyinizi asan başa düşülən təlimatlar, məsləhətlər və ən yaxşı təcrübələrlə öyrənin. Həm ba<PERSON>, həm də təcrübəli inkişaf etdiricilər üçün mükəmməldir!", "cta": "İnkişaf etdirici sənədləşməsini araşdırın"}, "support": {"title": "Geribildirim və Dəstək", "description": "Köməyə ehtiyacınız var və ya geribildirim vermək istəyirsiniz? Dəstəklə əlaqə saxlayın və Biela.dev-i təkmilləşdirməyə kömək edin!", "cta": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform": {"title": "Platforma Xüsusiyyətləri", "description": "Biela.dev-in, veb saytlar və tətbiqləri asanlıqla yaratmağa kömək edən güclü alətlərini kəşf edin. Kodlaşdırmanı süni intellektə buraxın!", "cta": "Xüsusiy<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "video": {"title": "Sürətli Başlanğıc Videosu", "description": "Biela.dev-in sizin üçün necə tətbiq və veb yarattığını izləyin — asanlıqla!", "cta": "Təlimatı izləyin"}, "guide": {"title": "Sürətli Başlanğıc Təlimatı", "steps": {"setup": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> dərhal qurun", "description": "İnkişaf mühitinizi saniyələr ərzində hazır edin"}, "generate": {"title": "Süni intellektlə tam yığma kod yaradın", "description": "Süni intellektin sizin üçün istehsalata hazır kod yazmasına icazə verin"}, "features": {"title": "D<PERSON>rhal Xüsusiyyət Yaradılması", "description": "Sadə təkliflərlə mürəkkəb xüsusiyyətlər əlavə edin"}, "editor": {"title": "Kod yazmadan və ya az kodla redaktor", "description": "Tətbiqinizi vizual və ya kod vasitəsilə dəyişdirin"}, "optimize": {"title": "Real-vaxtda optimallaşdırın və test edin", "description": "Tətbiqinizin mükəmməl işlədiyinə əmin olun"}, "deploy": {"title": "Bir kliklə yerləşdirin", "description": "Avtomatik yerləşdirmə ilə dərhal yayımlanın"}}}, "faq": {"title": "Tez-tez Verilən <PERSON>", "questions": {"what": {"question": "Biela.dev nədir?", "answer": "Biela.dev, kodla<PERSON><PERSON><PERSON>rma bilmə<PERSON>ə<PERSON>z belə, veb saytlar və tətbiqlər yaratmağa kömək edən süni intellektlə işləyən bir platformadır. O, kod yazılmasından yerləşdirməyə qədər bütün inkişaf prosesini avtomatlaşdırır."}, "experience": {"question": "Biela.dev-dən istifadə etmək üçün kodlaşdırma təcrübəsinə ehtiyac varmı?", "answer": "Xeyr! Biela.dev həm b<PERSON><PERSON>, həm də təcrübəli inkişaf etdiricilər üçün nəzərdə tutulub. Siz süni intellektin köməyi ilə qura bilərsiniz və ya yaranan kodu ehtiyacınıza görə düzəldə bilərsiniz."}, "projects": {"question": "Hansı növ layihələr yarada bilərəm?", "answer": "<PERSON><PERSON>, veb tətbiqlər, mobil tətbiqlər, SaaS platformaları, e-ticar<PERSON>t <PERSON>azaları, idarəetmə panelləri və daha çox şey yarada bilərsiniz."}, "edit": {"question": "Biela.dev tərəfindən yaradılan kodu redaktə edə bilərəmmi?", "answer": "Bəli! Biela.dev, süni intellekt tərəfindən yaradılan kodu fərdiləşdirməyə və ya asan redaktə üçün kod yazmadan/az kodlu redaktordan istifadə etməyə imkan verir."}, "deployment": {"question": "Yerlə<PERSON>dirmə necə işləyir?", "answer": "<PERSON>ir kliklə, Biela.dev layihə<PERSON><PERSON> yerl<PERSON>dir<PERSON>, onu canlı və istifadəyə hazır edir. Əl ilə server quraşdırmağa ehtiyac yoxdur!"}, "pricing": {"question": "Biela.dev pulsuz istifadə edilə bilərmi?", "answer": "Biela.dev ə<PERSON>s xü<PERSON>yətlərlə pulsuz plan təklif edir. Daha inkişaf etmiş alətlər və resurslar üçün isə ödənişli plana keçid edə bilərsiniz."}, "integrations": {"question": "<PERSON><PERSON><PERSON><PERSON><PERSON> tərəf alətləri və ya verilənlər bazalarını inteqrasiya edə bilərəmmi?", "answer": "Bəli! Biela.dev, populyar verilənlər bazaları (MongoDB, Firebase, Supabase) və üçüncü tərəf API-ləri ilə inteqrasiyanı dəstəkləyir."}, "help": {"question": "<PERSON><PERSON><PERSON><PERSON> çəkirəmsə, kömək haradan ala bilərəm?", "answer": "İnkişaf etdirici sənədləşməmizə, S<PERSON>r<PERSON>tl<PERSON> Başlanğıc Təlimatına baxa və ya kömək mərkəzimiz vasitəsilə dəstəklə əlaqə saxlaya bilərsiniz."}}}, "cta": {"title": "Başlamağa hazırsınız?", "description": "Biela.dev ilə ilk layihənizi yaradın və inkişafın gələcəyini yaşayın.", "button": "<PERSON><PERSON>"}}, "confirmDeleteProject": "<PERSON><PERSON><PERSON><PERSON> silinməsini təsdiqləyin", "confirmRemoveFromFolder": "Qovluqdan çıxarılmasını təsdiqləyin", "deleteProjectWarning": "{{projectName}} lay<PERSON><PERSON><PERSON>i daimi olaraq silmək istədiyinizə əminsinizmi?", "removeFromFolderWarning": "{{projectName}} lay<PERSON><PERSON><PERSON>i {{folderName}} qovluğundan çıxarmaq istədiyinizə əminsinizmi?", "confirm": "T<PERSON>sdiqlə", "confirmDeleteFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON> silinməsini təsdiqləyin", "deleteFolderWarning": "{{folderName}} qovluğunu silmək istədiyinizə əminsinizmi? Bu əməliyyatı geri qaytarmaq mümkün deyil.", "folderDeletedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>i", "downloadChat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> endir", "inactive.title": "Bu vərəq qeyri-aktivdir", "inactive.description": "Bu vərəqi aktiv etmək və tətbiqdən istifadəni davam etdirmək üçün aşağıdakı düyməyə klikləyin.", "inactive.button": "Bu vərəqi istifadə et", "suggestions": {"weatherDashboard": "<PERSON>va proqnozu lövhəsi ya<PERSON>ın", "ecommercePlatform": "E-ticarət platforması qurun", "socialMediaApp": "Sosial media tətbiqi dizayn edin", "portfolioWebsite": "Portfolio vebsaytı yaradın", "taskManagementApp": "Tapşırıq idarəetmə tətbiqi yaradın", "fitnessTracker": "Fitness izləyicisi qurun", "recipeSharingPlatform": "Resept paylaşım platforması dizayn edin", "travelBookingSite": "<PERSON>ə<PERSON>ət rezervasiya saytı yaradın", "learningPlatform": "Öyrənmə platforması qurun", "musicStreamingApp": "Musiqi axını tətbiqi dizayn edin", "realEstateListing": "<PERSON><PERSON><PERSON> siyahısı yaradın", "jobBoard": "İş elanları saytı yaradın"}, "pleaseWait": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>...", "projectsInAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectsInCurrentFolder": "{{folderName}} qovluğundakı layihələr", "createYourFirstProject": "<PERSON><PERSON>n", "startCreateNewProjectDescription": "Mö<PERSON><PERSON><PERSON>əm bir şey yaratmağa başlayın. Layih<PERSON>ləriniz yaradıldıqdan sonra burada göstəriləcək.", "createProjectBtn": "<PERSON><PERSON>", "publishedToContest": "Müsabiqəyə göndərildi", "publishToContest": "Müsabiqəyə göndər", "refreshSubmission": "Təq<PERSON><PERSON>ı yenilə", "contestInformation": "Müsabiqə haqqında məlumat", "selectFolder": "<PERSON><PERSON><PERSON><PERSON>", "folder": "Qovluq", "selectAFolder": "<PERSON><PERSON> qovluq seçin", "thisProject": "bu layihə", "projectDeletedSuccessfully": "<PERSON><PERSON><PERSON> u<PERSON> silindi", "projectRemovedFromFolder": "<PERSON><PERSON>ə qovluqdan çıxarıldı", "unnamedProject": "<PERSON><PERSON><PERSON><PERSON>", "permanentDeletion": "<PERSON><PERSON> silm<PERSON>", "removeFromFolder": "{{folderName}}-dən <PERSON>ı<PERSON>n", "verifiedAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hesab", "hasSubmittedAMinimumOfOneProject": "Ən azı bir layihə təqdim edib", "haveAtLeastActiveReferrals": "Ən azı {{number}} aktiv yönləndirmələriniz var", "GoToAffiliateDashBoard": "Tə<PERSON>əfdaş panelinə keçin", "LikeOneProjectThatBelongsToAnotherUser": "Başqa bir istifadəçiyə məxsus bir layihəni bəyənin", "GoToContestPage": "Müsabiqə səhifəsinə keçin", "ContestStatus": "Müsabiqə statusu", "ShowcaseYourBestWork": "Ən yaxşı işinizi nümayiş etdirin", "submissions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "qualifyConditions": "Uyğunluq <PERSON>", "completed": "Tamamlandı", "collapseQualifyConditions": "Uyğunluq şərtlə<PERSON> yığışdırın", "expandQualifyConditions": "Uyğunluq şə<PERSON>lə<PERSON> genişləndirin", "basicParticipation": "<PERSON><PERSON><PERSON>", "complete": "Tamamlandı", "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forTop3Places": "İlk 3 yer üçün", "deleteSubmission": "Təq<PERSON>matı silin", "view": "Baxış", "submitMoreProjectsToIncreaseYourChangesOfWining": "Qazanma şansınızı artırmaq üçün daha çox layihə təqdim edin!", "removeFromContest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>dən çıxarın?", "removeFromContestDescription": "Siz <project>{{project}}</project> lay<PERSON>ə<PERSON>i müsabiqədən çıxarmaq istədiyinizə əminsinizmi? Bu əməliyyat geri qaytarıla bilməz.", "cardVerificationRequired": "Kart təsdiqi tələb olunur", "pleaseVerifyCard": "Davam etmək üçün kartınızı təsdiqləyin", "unlockFeaturesMessage": "Bütün platforma funksiyalarına tam giriş əldə etmək üçün kartınızı yoxlamağınızı tələb edirik. Bu proses tamamilə təhlükəsizdir və Biela.dev-də rahat təcrübə təmin edir. Yoxlama zamanı kartınızdan heç bir ödəniş tutulmayacaq.", "freeVerificationNotice": "Doğru<PERSON>a <PERSON>", "accessToAllFeatures": "Biela.dev funksiyalarına tam giriş.", "enhancedFunctionality": "Təkmilləşdirilmiş platforma performansı.", "quickSecureVerification": "T<PERSON>hl<PERSON><PERSON><PERSON><PERSON>z və şifrələnmiş yoxlama prosesi.", "noCharges": "*Heç bir ödə<PERSON> və ya gizli xərclər yoxdur", "verificationOnly": " — yalnız yoxlama üçün.", "verifyNow": "İndi təsdiqlə", "DropFilesToUpload": "Yükləmək üçün faylları buraxın", "inactiveTitle": "Bu tab aktiv deyil", "inactiveDescription": "Tətbiqdən istifadə etməyə davam etmək üçün aşağıdakı düyməni klikləyin və bu tabı aktiv edin.", "inactiveButton": "Bu tabı istifadə et", "projectInfo": {"information": "<PERSON><PERSON><PERSON>ə<PERSON>ı", "type": "<PERSON><PERSON>ə Növü", "complexity": "Ç<PERSON><PERSON>lik", "components": "Komponentlər", "features": "<PERSON>ü<PERSON><PERSON><PERSON><PERSON><PERSON>tlər", "confidenceScore": "İnam Nöqtəsi", "estimationUncertainty": "<PERSON>ə<PERSON><PERSON> qeyri-müəyyənlik", "keyTechnologies": "<PERSON><PERSON><PERSON>"}, "projectMetrics": {"teamComposition": "<PERSON><PERSON><PERSON>", "hoursBreakdown": "Saatların <PERSON>ü<PERSON>ə<PERSON>", "timeToMarket": "Bazarə Yüksəlmə Vaxtı", "maintenance": "Baxım", "aiPowered": "AI ilə Gücləndirilmiş", "developerLevel": "Təkmilləşdirici Səviyyəsi", "nextGenAI": "Növbəti Nə<PERSON>l AI", "keyBenefits": "<PERSON><PERSON><PERSON>", "instantDevelopment": "<PERSON><PERSON><PERSON>li <PERSON>", "noMaintenanceCosts": "<PERSON>xım <PERSON>", "highConfidence": "Yüksək İnam", "productionReadyCode": "İstehsalata Hazır <PERSON>", "immediate": "<PERSON><PERSON><PERSON><PERSON>", "uncertainty": "Qeyri-Müəyyənlik", "minimal": "Minimal"}, "loadingMessages": {"publish": ["<PERSON><PERSON>ə məlumatlarını əldə edir...", "Ekran şək<PERSON> çəkilir...", "Xülasə və təsvir yaradılır..."], "refresh": ["Cari təqdimat məlumatlarını əldə edir...", "Lay<PERSON>ə ekran görüntüsü ye<PERSON>ənir...", "<PERSON><PERSON><PERSON> detallarını yeniləyir..."]}, "errorMessages": {"publish": "\"{{projectName}}\" lay<PERSON><PERSON><PERSON>i müsabiqəyə dərc etmək mümkün olmadı. Sizin tələb işlənərkən problem yaranıb.", "refresh": "\"{{projectName}}\" təqdimatını yeniləmək mümkün olmadı. Server layihə məlumatlarını yeniləyə bilmədi."}, "actions": {"refresh": {"title": "Təqdimatı Yeniləyir", "successMessage": "Layihə təqdimatınız müvəffəqiyyətlə yeniləndi.", "buttonText": "Yenilənmiş Təqdimatı Görüntülə"}, "publish": {"title": "Müsabiqəyə Dərc Edir", "successMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> müsabiqəyə təqdim edilib.", "buttonText": "Müsabiqə Səhifəsində Görüntülə"}}, "SupabaseConnect": "Supabase", "SupabaseDashboard": "Supabase", "RestoreApp": "Bərpa et", "SaveApp": "<PERSON><PERSON>a saxla", "ForkChat": "<PERSON>əp <PERSON>mək", "BielaTerminal": "Biela Terminalı", "UnitTesting": "<PERSON><PERSON><PERSON>", "InstallDependencies": "Asılılıqları quraşdır", "InstallDependenciesDescription": "<PERSON><PERSON>ə üçün lazım olan bütün paketləri istifadə edərək quraşdırır", "BuildProject": "<PERSON><PERSON><PERSON><PERSON> qur", "BuildProjectDescription": "Layihəni istehsal üçün tərtib və optimallaşdırır", "StartDevelopment": "İnkişafı başlat", "StartDevelopmentDescription": "Canlı önizləmə üçün inkişaf serverini işə salır", "ContextLimitReached": "Kontekst limiti aşılıb", "ClaudeContextDescription1": "Bu layihə üçün Claude ilə kontekst həddinə çatmısınız. Narahat olmayın – daha geniş kontekst pəncərəsinə sahib Gemini modelinə keçərək davam edə bilərik.", "ClaudeContextDescription2": "Gemini modelləri 5 dəfə daha çox kontekst tutumu təklif edir, bu da bütün kodunuzu, söhbət tarixçənizi saxlamağa və layihənizi fasiləsiz davam etdirməyə imkan verir.", "SelectModelToContinue": "Davam etmək üçün model seçin:", "Performance": "Performans", "AlternativeLimitExplanation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu layihə üçün süni intellektin emal limiti dolub. Əksər yer idxal edilmiş fayllar tərəfindən istifadə olunur, s<PERSON><PERSON><PERSON><PERSON><PERSON> özü deyil.", "SuggestedSolutions": "<PERSON><PERSON><PERSON><PERSON><PERSON> həllər:", "ReimportProject": "Yeni layihə kimi yenidən idxal et", "ReimportProjectDescription": "<PERSON><PERSON>, söhbət ta<PERSON>çəsini siləcək və kontekst sahəsini boşaldacaq, lakin fayllarınız qorunacaq.", "BreakIntoProjects": "Bir neçə layihəyə böl", "BreakIntoProjectsDescription": "İşinizi ayrıca inkişaf etdirilə bilən kiçik hissələrə bölün.", "ExportWork": "Tamamlanmış işi ixrac et", "ExportWorkDescription": "Kontekst sahəsini boşaltmaq üçün tama<PERSON>lanmış faylları endirin və arxivləşdirin.", "AlternativeContextNote": "Mövcud kontekstdən maksimum istifadə etmək üçün istifadə olunmayan faylları və kitabxanaları silməyi və cari inkişaf mərhələsi üçün lazım olan əsas fayllara diqqət yetirməyi düşünün.", "ContinueWithSelectedModel": "Seçilmiş modellə davam et", "Close": "Bağla", "AIModel": "Süni İntellekt Modeli", "Active": "Aktiv", "Stats": "Statistika", "Cost": "<PERSON><PERSON><PERSON><PERSON><PERSON>ğ", "ExtendedThinkingDisabledForModel": "Bu model <PERSON><PERSON><PERSON><PERSON> mö<PERSON>d <PERSON>l", "ExtendedThinkingAlwaysOn": "Bu model ilə həmişə aktivdir", "NoProjectFound": "Bu adla heç bir layihə tapılmadı.", "NoProjectFoundDescription": " <PERSON><PERSON><PERSON><PERSON><PERSON> olma<PERSON> yazılış səhvlərini yoxlayın və yenidən cəhd edin.", "limitReached": "Limitinizə çatdınız!", "deleteProjectsSupabase": "<PERSON><PERSON><PERSON><PERSON><PERSON> olmasa bəzi layihələri silin və ya Supabase limitinizi artırın.", "goTo": "Keçid et", "clickProjectSettings": " layih<PERSON><PERSON><PERSON>likləyin, <PERSON><PERSON><PERSON>, aşağ<PERSON> diyirləyin və klikləyin", "delete": "Sil", "retrying": "<PERSON><PERSON><PERSON><PERSON>n cəhd edilir…", "retryConnection": "Əlaqəni yenidən sınayın", "RegisterPageTitle": "Qeydiyyat – biela.dev", "RegisterPageDescription": "biela.dev platformasında hesab yaradın və bütün funksiyalara çıxış əldə edin.", "SignUpHeading": "Qeydiyyatdan keç", "AlreadyLoggedInRedirectHome": "<PERSON>ıq daxil olmusunuz! Ana səhifəyə yönləndirilirsiniz...", "PasswordsMismatch": "<PERSON><PERSON>ar <PERSON><PERSON><PERSON> gə<PERSON>.", "FirstNameRequired": "Ad tələb olunur", "LastNameRequired": "<PERSON><PERSON>d tələb olu<PERSON>r", "UsernameRequired": "İstifadəçi adı tələb olunur", "EmailRequired": "E-poçt tələb olunur", "EmailInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>, düzgün e-poçt ünvanı daxil edin", "TooManyRequests": "Ç<PERSON> sayda sorğu <PERSON>, zəhmət olmasa bir az sonra yenidən cəhd edin", "SomethingWentWrongMessage": "<PERSON>ir xəta baş verdi, zəhmət olmasa bir az sonra yenidən cəhd edin", "PasswordRequired": "<PERSON><PERSON> tələb olu<PERSON>r", "ConfirmPasswordRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>, parolu təsdiq edin", "AcceptTermsRequired": "Xidmət şərtləri və Məxfilik siyasəti ilə razılaşmalısınız", "CaptchaRequired": "<PERSON><PERSON><PERSON><PERSON>t olmasa CAPTCHA-nı tamamlayın", "RegistrationFailed": "Qeydiyyat uğursuz oldu", "EmailConfirmationSent": "Təsdiq e-poçtu göndərildi! Zəhmət olmasa e-poçtunuzu təsdiqləyin və daxil olun.", "RegistrationServerError": "Qeydiyyat uğ<PERSON>u (server false cavab verdi).", "SomethingWentWrong": "<PERSON><PERSON>sə yanlış getdi", "CheckEmailHeading": "Qeydiyyatı təsdiqləmək üçün e-poçtunuzu yoxlayın", "CheckEmailDescription": "Təsdiq linki olan e-poçt göndərmişik.", "GoToHomepage": "Ana səhifəyə keç", "ReferralCodeOptional": "Tövsiyə kodu (isteğe bağlı)", "EnterReferralCode": "<PERSON><PERSON>ər sizi dəvət edən biri varsa, onun göndərdiyi kodu daxil edin.", "PasswordPlaceholder": "<PERSON><PERSON>", "ConfirmPasswordPlaceholder": "<PERSON><PERSON><PERSON> təsdiqlə", "CreateAccount": "<PERSON><PERSON><PERSON><PERSON>", "AlreadyHaveAccountPrompt": "<PERSON>ıq he<PERSON> var?", "Login": "Daxil ol", "AcceptTermsPrefix": "<PERSON><PERSON><PERSON> razıyam", "TermsOfService": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AndSeparator": "və", "PrivacyPolicy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoginPageTitle": "Daxil ol – biela.dev", "LoginPageDescription": "Hesabınıza daxil olun və ya biela.dev-də giriş edərək bütün funksiyalardan istifadə edin.", "LogInHeading": "Daxil ol", "EmailOrUsernamePlaceholder": "E-poçt / İstifadəçi adı", "ForgotPassword?": "<PERSON><PERSON><PERSON>?", "LoginToProfile": "Profilə daxil olun", "UserNotConfirmed": "İstifadəçi hesabı təsdiqlənməyib", "ConfirmEmailNotice": "Hesabınızı aktivləşdirmək üçün e-poçtunuzu təsdiqləməlisiniz.", "ResendConfirmationEmail": "Təsdiq e-poçtunu yenidən göndər", "ResendConfirmationSuccess": "Təsdiq e-poçtu yenidən göndərildi! Zəhmət olmasa e-poçtunuzu yoxlayın.", "ResendConfirmationError": "Təsdiq e-poçtu göndərilə bilmədi.", "LoginSuccess": "Uğurla daxil oldunuz! Yönləndirilirsiniz...", "LoginFailed": "<PERSON><PERSON><PERSON> uğursuz oldu", "LoginWithGoogle": "Google ilə daxil ol", "LoginWithGitHub": "<PERSON>it<PERSON><PERSON> ilə daxil ol", "SignUpWithGoogle": "Google ilə qeydiyyatdan keçin", "SignUpWithGitHub": "<PERSON><PERSON><PERSON><PERSON> ilə qeydiyya<PERSON>dan keçin", "Or": "və ya", "NoAccountPrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur?", "SignMeUp": "Qeydiyyatdan keç", "ForgotPasswordPageTitle": "<PERSON><PERSON><PERSON> biela.dev", "ForgotPasswordPageDescription": "biela.dev hesabın<PERSON>zın parolunu sıfırlayın və yenidən giriş əldə edin.", "BackToLogin": "<PERSON><PERSON><PERSON><PERSON> qayıt", "ForgotPasswordHeading": "<PERSON><PERSON><PERSON>", "ForgotPasswordDescription": "E-poçtunuzu daxil edin və parolunuzu sıfırlamaq üçün təsdiq linki göndərək.", "VerificationLinkSent": "Təsdiq linki göndərildi! Zəhmət olmasa e-poçtunuzu yoxlayın.", "EnterYourEmailPlaceholder": "E-poçtunuzu daxil edin", "Sending": "Göndərilir...", "SendVerificationCode": "<PERSON>ə<PERSON><PERSON><PERSON> kodunu göndər", "InvalidConfirmationLink": "Təsdiq linki etibarsızdır", "Back": "<PERSON><PERSON>", "ResetPassword": "<PERSON><PERSON><PERSON>", "ResetPasswordDescription": "Hesabınız üçün yeni parol yaradın", "NewPasswordPlaceholder": "<PERSON><PERSON> parol", "ConfirmNewPasswordPlaceholder": "Yeni parolu təsdiqlə", "ResetPasswordButton": "<PERSON><PERSON><PERSON>", "PasswordRequirements": "Parol ən az 8 simvoldan ibarət olmalı və böyük hərf, kiçik hərf, rəqəm və xüsusi simvol daxil etməlidir.", "PasswordUpdatedSuccess": "<PERSON><PERSON> ye<PERSON>ləndi!", "affiliateDashboard": "Tərəfdaş Paneli", "userDashboard": "İstifadəçi Paneli", "returnToAffiliateDashboard": "Tərəfdaş panelinə qayıt", "returnToUserDashboard": "İstifadəçi panelinə qayıt", "myProfile": "Profilim", "viewAndEditYourProfile": "Profilinizi görüntüləyin və redaktə edin", "billing": "Faktura", "manageYourBillingInformation": "Faktura məlumatlarınızı idarə edin", "logout": "Çıxış", "logoutDescription": "Hesabınızdan çıxış edin", "SupabaseNotAvailable": "Supabase hazırda mö<PERSON>, x<PERSON><PERSON> edirik bir az sonra yenidən cəhd edin.", "projectActions": {"invalidSlug": "Layihə slug-u etibarsızdır.", "downloadSuccess": "<PERSON><PERSON>ə uğurla endirildi!", "downloadError": "Layihəni endirmək alınmadı.", "exportSuccess": "Söhbət ixrac edildi! Yükləmələr qovluğunu yoxlayın.", "exportError": "Söhbəti ixrac etmək alınmadı.", "duplicateSuccess": "S<PERSON><PERSON>bət uğurla kopyalandı!", "duplicateError": "S<PERSON><PERSON>bəti k<PERSON>alamaq alınmadı."}, "enter_new_phone_number": "Yeni telefon nömrəsini daxil edin", "enter_new_phone_number_below": "<PERSON><PERSON><PERSON><PERSON><PERSON> olmasa yeni telefon nömrənizi daxil edin:", "new_phone_placeholder": "Yeni telefon nömrəsi", "enter_otp_code": "OTP Kodunu Da<PERSON>l Edin", "confirm_phone_message": "Hesa<PERSON>dan istifadə üçün telefon nömrənizi təsdiqləməlisiniz. Kod ({{phone}}) nömrəsinə göndərildi.", "wrong_phone": "Yanlış telefon nömrəsi?", "resend_sms": "SMS-i yenidən göndər", "submit": "T<PERSON>sdiqlə", "tokensAvailable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sectionTitle": "<PERSON><PERSON>", "addDomainButton": "Domen Adı Əlavə Et", "connectCustomDomainTitle": "Xüsusi Domen Adı Bağla", "disclaimer": "Qeyd:", "disclaimerText": "Uğurlu təsdiqləmə üçün yuxarıdakı bütün DNS qaydalarını düzgün təyin etməlisiniz", "domainInputDescription": "Bu layihəyə bağlamaq istədiyiniz domen adını daxil edin.", "domainLabel": "Domen <PERSON>ı", "domainPlaceholder": "example.com", "cancelButton": "<PERSON><PERSON>ğ<PERSON> et", "continueButton": "Domen Adı Əlavə Et", "deployingText": "Yayımlanır...", "addingText": "<PERSON><PERSON>ə olunur...", "verifyButtonText": "T<PERSON>sdiqlə", "configureDnsTitle": "DNS Qeydlərini Konfiqurasiya Et", "configureDnsDescription": "Sahibliyi təsdiqləmək və layihəyə bağlamaq üçün domeninizə aşağıdakı DNS qeydlərini əlavə edin.", "tableHeaderType": "Növ", "tableHeaderName": "Ad", "tableHeaderValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "note": "Qeyd:", "noteText": "DNS dəyişikliklərinin yayılması 48 saata qədər çəkə bilər. Lakin adətən bir neçə dəqiqə və ya saat ərzində qüvvəyə minir.", "backButton": "<PERSON><PERSON>", "showDnsButton": "DNS Ayarlarını Göstər", "hideDnsButton": "DNS Ayarlarını Gizlət", "removeButton": "Sil", "dnsSettingsTitle": "Domen DNS Ayarları", "removeDomainConfirmTitle": "Domen Adını Sil", "removeConfirmationText": "Domen adını silmək istədiyinizə əminsiniz? ", "importantCleanupTitle": "Vacib DNS Təmizliyi", "cleanupDescription": "Bu domeni layihədən sildikdən sonra, konfiqurasiya zamanı əlavə etdiyiniz DNS qeydlərini də silməyi unutmayın. Bu, təmiz DNS konfiqurasiyasını qorumağa və gələcək problemlərin qarşısını almağa kömək edir.", "confirmRemoveButton": "Domen Adını Sil", "customConfigTitle": "Xüsusi Domen Konfiqurasiyası", "customConfigDescription": "Layihənizə öz domen adlarınızı bağlayın. Layihə Biela-nın əsas domeni vasitəsilə əlçatan olacaq, lakin xüsusi domenlər istifadəçilər üçün peşəkar marka təcrübəsi təmin edir.", "defaultLabel": "Varsayılan", "statusActive": "Aktiv", "statusPending": "Gözləmədə", "lastVerifiedText": "Az öncə təsdiqləndi", "errorInvalidDomain": "<PERSON><PERSON><PERSON><PERSON><PERSON> olmasa keçərli domen adı daxil edin (məsələn, example.com)", "errorDuplicateDomain": "Bu domen adı artıq layihəyə qoşulub", "errorAddFail": "Domen adını əlavə etmək mümkün olmadı.", "successAdd": "Domen adı uğurla əlavə edildi! Layihəyə bağlandı.", "benefitsTitle": "<PERSON><PERSON>", "benefitSecurityTitle": "Təkmill<PERSON>şdirilmiş Təhlükəsizlik", "benefitSecurityDesc": "Bütün xüsusi domenlər avtomatik olaraq SSL sertifikatları ilə qorunur.", "benefitPerformanceTitle": "Yüksək Performans", "benefitPerformanceDesc": "Qlobal CDN layihənizin sürətli yüklənməsini təmin edir.", "benefitBrandingTitle": "Peşəkar Brend", "benefitBrandingDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> brend təcrübəsi üçün öz domeninizi istifadə edin.", "benefitAnalyticsTitle": "Analitika İnteqrasiyası", "benefitAnalyticsDesc": "<PERSON><PERSON><PERSON><PERSON> domenlər analitika platformaları ilə problemsiz işləyir.", "meta": {"index": {"title": "biela.dev | AI-əsas Web və App Yaradıcı – Təkliflərdən Yaradın", "description": "Biela.dev ilə fikirlərinizi canlı veb saytlara və tətbiqlərə çevrən. AI-əsas təkliflərdən asanlıqla xüsusi digital məhsullar yaradın."}, "login": {"title": "biela.dev <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Daxil Olun", "description": "AI-<PERSON><PERSON><PERSON> lay<PERSON>ə<PERSON>ərinizi idarə edin və biela.dev panelinə daxil olun."}, "register": {"title": "biela.dev <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> – AI ilə Yaradın", "description": "biela.dev hesabınızı yaradın və AI-əsas təkliflərdən istifadə edərək veb saytlar və tətbiqlər yaradın."}, "dashboard": {"title": "Layihələriniz Paneli – biela.dev", "description": "AI-<PERSON><PERSON><PERSON> layihələrinizi idarə edin, canlı layihələri redaktə edin və inkişaf tarixinizi izləyin."}, "profile": {"title": "Profiliniz – biela.dev", "description": "biela.dev hesab mə<PERSON><PERSON>n<PERSON>zı baxın və yeniləyin, seçimlərinizi idarə edin və süni intellekt təcrübənizi fərdiləşdirin."}, "billing": {"title": "Ödəniş – biela.dev", "description": "Şəxsiyyətinizi təsdiqləmək və biela.dev funksiyalarının tam istifadəsini açmaq üçün kredit kartınızı əlavə edin – heç bir ödəniş edilməyəcək."}}, "transferProject": "Nüsxəni <PERSON>", "transferSecurityNoteDescription": "Qəbul edən şəxs bu layihənin və ona aid bütün resursların nüsxəsinə tam giriş əldə edəcək. Siz hələ də orijinal layihəyə girişə malik olacaqsınız.", "transferProjectDescription": "Layihənin bir nüsxəsini ötürmək istədiyiniz şəxsin istifadəçi adını və ya e-poçt ünvanını daxil edin.", "transferProjectLabel": "İstifadəçi adı və ya e-poçt", "transferProjectPlaceholder": "johns<PERSON> və ya <EMAIL>", "transferButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transferSecurityNote": "Təhlükəsizlik qeydi:", "dontHavePermisionToTransfer": "Bu layihəni köçürmək icazəniz yoxdur", "transferProjectUserNotFound": "{{ user }} istifadəçisi tapılmadı!", "transferErrorOwnAccount": "Bir layihəni öz hesabınıza köçürə bilməzsiniz.", "transferError": "Layihəni köçürərkən səhv baş verdi", "transferSuccess": "{{ user }} istifadəçisinə uğurla köçürüldü", "enterValidEmailUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON> olma<PERSON> istifadəçi adı və ya e-poçt daxil edin", "enterMinValidEmailUsername": "<PERSON><PERSON><PERSON><PERSON>t olmasa etibarlı istifadəçi adı (ən azı 3 simvol) və ya e-poçt ünvanı daxil edin", "youWillStillHaveAccess": "Siz hələ də orijinal layihəyə giriş əldə edəcəksiniz", "newChangesWillNotAffect": "Yeni dəyiş<PERSON><PERSON>lər digər istifadəçinin layihəsinə təsir etməyəcək"}