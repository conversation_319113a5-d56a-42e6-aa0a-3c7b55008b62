{"Claude37Description": "Leistungsstärkste KI für Premium-Websites", "Claude37FeatureFirst": "Hochwertiger Code", "Claude37FeatureSecond": "Erweiterte Funktionalität", "Claude37FeatureThird": "Überlegenes Design", "Claude37Performance": "Ausgezeichnet", "Claude37Cost": "Premium", "Claude37ContextWindow": "Standard-Kontextfenster (200K Tokens)", "GeminiProDescription": "Vielseitige KI mit starker Leistung und großem Kontext", "GeminiProFeatureFirst": "Zuverlässiger Code", "GeminiProFeatureSecond": "Verarbeitet komplexe Eingaben", "GeminiProFeatureThird": "Gute Designqualität", "GeminiProPerformance": "<PERSON><PERSON> gut", "GeminiProCost": "Gehobenes Mittelklasse", "GeminiProContextWindow": "Großes Kontextfenster (1M+ Tokens)", "GeminiFlashDescription": "Schnelle und kostengünstige Option", "GeminiFlashFeatureFirst": "<PERSON><PERSON><PERSON>", "GeminiFlashFeatureSecond": "Grundfunktionen", "GeminiFlashFeatureThird": "Einfache Designs", "GeminiFlashPerformance": "Gut", "GeminiFlashCost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GeminiFlashContextWindow": "Großes Kontextfenster (1M+ Tokens)"}