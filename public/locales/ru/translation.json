{"whatWouldYouLikeToBuild": "Превратите вашу идею в рабочий веб-сайт или приложение за считанные минуты", "whatWouldYouLikeToBuildSubtitle": "Если вы можете <1>вообразить</1> это, вы можете <5>закодировать</5> это.", "fromIdeaToDeployment": "Начни бесплатно. Программируй всё, что угодно. Превращай навыки в возможности с каждым запросом.", "codePlaceholder": "Если вы можете себе это представить, BIELA может это закодировать. Что будем делать сегодня?", "defaultPlaceholder": "Как я могу вам помочь сегодня? Давайте вместе создадим что-то удивительное", "checkingFeatures": "Проверка функционала", "checklists": "Контрольные списки", "runUnitTestsSuggestionTitle": "Предложение", "runUnitTestsSuggestionMessage": "Хотите запустить модульные тесты для вашего проекта?", "runUnitTestsPrimaryButton": "Запустить модульные тесты", "runUnitTestsSecondaryButton": "Отменить", "createDatabaseTitle": "Создание базы данных", "createDatabaseMessage": "Хотите создать базу данных для вашего проекта?", "createDatabasePrimaryButton": "Создать базу данных", "createDatabaseSecondaryButton": "Отменить", "extendedThinking": "Расширенное мышление", "extendedThinkingTooltip": "Позволяет ИИ думать более глубоко перед ответом", "firstResponseOnly": "Только первый ответ", "always": "Всегда", "attachFile": "Прикрепить файл", "voiceInput": "Голосовой ввод", "selectLanguage": "Выберите язык для голосового ввода", "languageSelectionDisabled": "Выбор языка отключен во время записи", "notAvailableInFirefox": "Эта функция недоступна в Firefox, но доступна в Chrome и Safari", "enhancePrompt": "Улучшить подсказку", "cleanUpProject": "Очистить проект", "showPrompt": "Показать подсказку", "hidePrompt": "Скрыть подсказку", "sendButton": "Отправить", "abortButton": "Отмена", "inspirationTitle": "Нужна вдохновление? Попробуйте один из этих вариантов:", "cleanUpPrompt": "Очистите проект, убедившись, что ни один файл не содержит более 300 строк кода. Разбейте большие файлы на меньшие модульные компоненты, сохраняя полную функциональность. Определите и удалите все неиспользуемые файлы, код, компоненты и избыточные данные, которые больше не нужны. Убедитесь, что все компоненты остаются правильно связанными и функционируют, чтобы не нарушить работу существующей системы. Сохраните целостность кода, проверяя, что никакие изменения не вызывают ошибок или не нарушают текущие функции. Цель — оптимизировать проект с точки зрения эффективности, удобства обслуживания и ясности.", "checklistPrompt": "Изучите мой первоначальный запрос, поймите цель шаг за шагом и создайте для меня контрольный список с зеленой галочкой для выполненного и красной для того, что еще осталось сделать.", "personalPortfolioIdea": "Создайте персональный портфолио-сайт с темной темой", "recipeFinderIdea": "Создайте приложение для поиска рецептов, которое предлагает блюда на основе имеющихся ингредиентов", "weatherDashboardIdea": "Разработайте метеодашборд с анимированными фонами", "habitTrackerIdea": "Создайте трекер привычек с визуализацией прогресса", "loading": "Загрузка", "error": "Ошибка", "succes": "Успех!", "tryAgain": "Попробуйте снова", "dashboard": "Панель управления", "getStartedTitle": "Начать", "getStartedSub": "Узнайте, как работает Biela.dev", "createProject": "Создать новый проект", "createProjectSub": "Начните с нуля", "editProjectName": "Редактировать название проекта", "editName": "Редактировать имя", "uploadProject": "Загрузить проект", "uploadProjectSub": "Импортировать существующий проект", "importChat": "Импортировать чат", "importChatSub": "Импортировать существующий чат", "createFolder": "Создать новую папку", "createFolderSub": "Организуйте свои проекты", "cancel": "Отмена", "changeFolder": "Изменить папку", "save": "Сохранить", "importing": "Импортируется...", "importFolder": "Импортировать папку", "giveTitle": "Задайте название", "projects": "Проекты", "searchProjects": "Поиск проектов...", "becomeAffiliate": "Стать партнером", "exclusiveGrowth": "Эксклюзивные возможности роста", "lifetimeEarnings": "Пожизненный доход", "highCommissions": "Высокие комиссионные", "earnCommission": "Получите 50% комиссионных с первой продажи", "joinAffiliateProgram": "Присоединиться к нашей партнерской программе", "folders": "Папки", "organizeProjects": "Организуйте свои проекты по категориям", "createNewFolder": "Создать новую папку", "enterFolderName": "Введите название папки", "editFolder": "Редактировать папку", "deleteFolder": "Удалить папку", "all": "Все", "webProjects": "Веб-проекты", "mobilepps": "Мобильные приложения", "developmentComparison": "Сравнение разработки", "traditionalVsAI": "Традиционное против ИИ", "traditional": "Традиционное", "standardApproach": "Стандартный подход", "developmentCost": "Стоимость разработки", "developmentTime": "Время разработки", "costSavings": "Экономия средств", "reducedCosts": "Сниженные затраты", "timeSaved": "Сэкономленное время", "fasterDelivery": "Быстрая доставка", "bielaDevAI": "Biela.dev ИИ", "nextGenDevelopment": "Разработка нового поколения", "developmentCosts": "Расходы на разработку", "openInGitHub": "Открыть на GitHub", "downloadProject": "Скачать проект", "duplicateProject": "Дублировать проект", "openProject": "Открыть проект", "deleteProject": "Удалить проект", "confirmDelete": "Удалить эту папку?", "invoicePreview": "Предварительный просмотр счета", "settings": {"title": "Настройки", "deployment": {"AdvancedSettings": {"advanced-settings": "Расширенные настройки", "configure-advanced-deployment-options": "Настроить расширенные параметры развертывания", "server-configuration": "Конфигурация сервера", "memory-limit": "Ограничение памяти", "region": "Регион", "security-settings": "Настройки безопасности", "enable-ddos-protection": "Включить защиту от DDoS", "protect-against-distributed": "Защищать от распределённых атак отказа в обслуживании", "ip-whitelisting": "Белый список IP", "restrict-acces-to": "Ограничить доступ к определённым IP-адресам", "deployment-options": "Параметры развертывания", "auto-deploy": "Автоматическое развертывание", "automatically-deploy-when": "Автоматически развертывать при пуше в основную ветку", "preview-deployments": "Просмотреть предварительные развертывания", "create-preview-deployments": "Создать предварительные развертывания для pull request'ов"}, "BuildSettings": {"build-and-deployment-settings": "Настройки сборки и развертывания", "build-command": "Команда сборки", "override": "Переопределить", "output-directory": "Выходной каталог", "override2": "Переопределить"}, "DatabaseConfiguration": {"database-configuration": "Конфигурация базы данных", "configure-your-db-connections": "Настройте подключения и параметры вашей базы данных", "database-type": "Тип базы данных", "connection-string": "Строка подключения", "your-db-credentials": "Ваши учетные данные базы данных зашифрованы и надежно хранятся", "database-settings": "Настройки базы данных", "pool-size": "Размер пула", "require": "Требуется", "prefer": "Предпочтительно", "disable": "Отключить", "add-database": "Добавить базу данных"}, "DomainSettings": {"domain-settings": "Настройки домена", "configure-your-custom-domain": "Настройте ваши пользовательские домены и SSL-сертификаты", "custom-domain": "Пользовательский домен", "add": "Добавить", "ssl-certificate": "SSL-сертификат", "auto-renew-ssl-certificates": "Автоматически обновлять SSL-сертификаты", "auto-renew-before-expiry": "Автоматически обновлять SSL-сертификаты до истечения срока", "force-https": "Принудительно использовать HTTPS", "redirect-all-http-traffic": "Перенаправлять весь HTTP-трафик на HTTPS", "active-domain": "Активные домены", "remove": "Удалить"}, "EnvironmentVariables": {"environment-variables": "Переменные окружения", "configure-environment-variables": "Настройте переменные окружения для ваших развертываний", "all-enviroments": "Все окружения", "environment-variables2": "Переменные окружения", "preview": "Предварительный просмотр", "development": "Разработка", "create-new": "Создать новый", "key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Значение", "save-variable": "Сохранить переменную", "you-can-also-import": "Вы также можете импортировать переменные из файла .env:", "import-env-file": "Импортировать файл .env"}, "ProjectConfiguration": {"project-configuration": "Конфигурация проекта", "config-your-project-settings": "Настройте параметры вашего проекта и опции развертывания", "project-url": "URL проекта", "framework": "Фреймворк", "repo": "Репозиторий", "branch": "Ветка", "main": "main", "development": "development", "staging": "staging"}}, "identity": {"unverified": {"title": "Проверка личности", "description": "Подтвердите свою личность, чтобы использовать Biela.dev", "subtitle": "Безопасный процесс проверки", "processServers": "Вся информация о карте хранится безопасно на серверах Stripe, а не на серверах Biela", "processCharge": "Вашу карту не будут списывать без вашего явного согласия на подписку", "processBenefits": "Biela.dev полностью бесплатен для проверенных аккаунтов до 15 мая 2025 года", "verifyStripe": "Подтвердить с помощью кредитной или дебетовой карты", "verifyStripeDescription": "Подключите ваш способ оплаты для проверки", "verifyNow": "Проверить сейчас"}, "verified": {"title": "Личность подтверждена", "description": "Ваша личность успешно подтверждена", "paymentMethod": "Способ оплаты", "cardEnding": "Карта, заканчивающаяся на", "updatePayment": "Обновить способ оплаты", "untilDate": "До 15 мая 2025 года", "freeAccess": "Бесплатный доступ", "freeAccessDescription": "Пользуйтесь полным доступом к Biela.dev бесплатно", "secureStorage": "Безопасное хранилище", "secureStorageDescription": "Информация о вашей карте хранится безопасно на серверах Stripe, а не на серверах Biela. С вашей карты не будет списана сумма без вашего явного согласия на подписку.", "subscriptionAvailable": "Подписки будут доступны с 15 мая 2025 года."}, "connectingToStripe": "Подключение к Stripe..."}, "tabs": {"billing": "Оплата", "profile": "Профиль", "deployment": "Развертывание", "identity": "Идентичность"}}, "help": {"title": "Чем мы можем вам помочь?", "searchPlaceholder": "Поиск в документации...", "categories": {"getting-started": {"title": "Начало работы", "description": "Узнайте основы использования Biela.dev", "articles": ["Краткое руководство", "Обзор платформы", "Создание вашего первого проекта", "Понимание разработки с ИИ"]}, "ai-development": {"title": "Разработка с ИИ", "description": "Освойте разработку с поддержкой ИИ", "articles": ["Эффективное написание запросов", "Лучшие практики генерации кода", "Советы по отладке ИИ", "Расширенные возможности ИИ"]}, "project-management": {"title": "Управление проектами", "description": "Организуйте и управляйте своими проектами", "articles": ["Структура проекта", "Работа в команде", "Контроль версий", "Опции развертывания"]}}, "channels": {"docs": {"name": "Документация", "description": "Полные руководства и справочные материалы по API"}, "community": {"name": "Сообщество", "description": "Общайтесь с другими разработчиками"}, "github": {"name": "GitHub", "description": "Сообщайте о проблемах и вносите свой вклад"}}, "support": {"title": "Нужна дополнительная помощь?", "description": "Наша служба поддержки работает круглосуточно, чтобы помочь вам с любыми вопросами или проблемами.", "button": "Связаться с поддержкой"}}, "getStarted": {"title": "Узнайте, как работает Biela.dev", "description": "Создайте своё приложение за несколько минут с помощью Biela.dev. Наш ИИ автоматизирует весь процесс разработки — от настройки до развертывания. Вот как наш ИИ легко создает ваше приложение!", "features": {"docs": {"title": "Документация для разработчиков", "description": "Узнайте, как использовать Biela.dev, с помощью понятных руководств, советов и лучших практик. Идеально подходит как для начинающих, так и для опытных разработчиков!", "cta": "Изучить документацию для разработчиков"}, "support": {"title": "Отзывы и поддержка", "description": "Если вам нужна помощь или у вас есть отзывы, свяжитесь с нашей службой поддержки и помогите нам улучшить Biela.dev!", "cta": "Отправить отзыв"}, "platform": {"title": "Функции платформы", "description": "Откройте для себя мощные инструменты, которые предлагает Biela.dev, чтобы помочь вам создавать сайты и приложения без усилий. Доверьте кодирование ИИ!", "cta": "Изучить функции"}}, "video": {"title": "Видео быстрого старта", "description": "Посмотрите, как Biela.dev создает для вас приложения и сайты без усилий!", "cta": "Смотреть учебное видео"}, "guide": {"title": "Руководство быстрого старта", "steps": {"setup": {"title": "Мгновенно настройте свой проект", "description": "Подготовьте свою среду разработки за считанные секунды"}, "generate": {"title": "Сгенерируйте full-stack код с помощью ИИ", "description": "Позвольте ИИ написать код, готовый к использованию в производстве"}, "features": {"title": "Мгновенная генерация функционала", "description": "Добавляйте сложные функции с помощью простых запросов"}, "editor": {"title": "Редактор без кода и с минимальным кодом", "description": "Редактируйте свое приложение визуально или через код"}, "optimize": {"title": "Оптимизируйте и тестируйте в реальном времени", "description": "Убедитесь, что ваше приложение работает безупречно"}, "deploy": {"title": "Разверните приложение одним кликом", "description": "Мгновенно выведите ваше приложение в онлайн с автоматическим развертыванием"}}}, "faq": {"title": "Часто задаваемые вопросы", "questions": {"what": {"question": "Что такое Biela.dev?", "answer": "Biela.dev — это платформа на основе ИИ, которая помогает вам создавать сайты и приложения, даже если вы не умеете программировать. Она автоматизирует весь процесс разработки — от написания кода до развертывания."}, "experience": {"question": "Нужен ли опыт программирования для использования Biela.dev?", "answer": "Нет! Biela.dev разработана как для новичков, так и для опытных разработчиков. Вы можете создавать с помощью ИИ или настраивать сгенерированный код по мере необходимости."}, "projects": {"question": "Какие проекты я могу создать?", "answer": "Вы можете создавать сайты, веб-приложения, мобильные приложения, SaaS-платформы, интернет-магазины, административные панели и многое другое."}, "edit": {"question": "Могу ли я редактировать код, сгенерированный Biela.dev?", "answer": "Да! Biela.dev позволяет вам настраивать сгенерированный ИИ код или использовать редактор no-code/low-code для легкого внесения изменений."}, "deployment": {"question": "Как работает развертывание?", "answer": "Одним кликом Biela.dev развертывает ваш проект, делая его доступным онлайн и готовым к использованию. Ручная настройка сервера не требуется!"}, "pricing": {"question": "Biela.dev бесплатна?", "answer": "Biela.dev предлагает бесплатный тарифный план с базовыми функциями. Для более продвинутых инструментов и ресурсов вы можете перейти на премиум-план."}, "integrations": {"question": "Могу ли я интегрировать сторонние инструменты или базы данных?", "answer": "Да! Biela.dev поддерживает интеграцию с популярными базами данных (MongoDB, Firebase, Supabase) и сторонними API."}, "help": {"question": "Где я могу получить помощь, если у меня возникнут проблемы?", "answer": "Вы можете обратиться к нашей документации для разработчиков, руководству быстрого старта или связаться со службой поддержки через наш центр помощи."}}}, "cta": {"title": "Готовы начать?", "description": "Создайте свой первый проект с Biela.dev и испробуйте будущее разработки.", "button": "Создать новый проект"}}, "confirmDeleteProject": "Подтвердить удаление проекта", "confirmRemoveFromFolder": "Подтвердить удаление из папки", "deleteProjectWarning": "Вы уверены, что хотите навсегда удалить {{projectName}}?", "removeFromFolderWarning": "Вы уверены, что хотите удалить {{projectName}} из {{folderName}}?", "confirm": "Подтвердить", "confirmDeleteFolder": "Подтвердить удаление папки", "deleteFolderWarning": "Вы уверены, что хотите удалить {{folderName}}? Это действие невозможно отменить.", "folderDeletedSuccessfully": "Папка успешно удалена", "downloadChat": "Скачать чат", "inactiveTitle": "Эта вкладка неактивна", "inactiveDescription": "Нажмите кнопку ниже, чтобы сделать эту вкладку активной и продолжить использование приложения.", "inactiveButton": "Использовать эту вкладку", "suggestions": {"weatherDashboard": "Создайте панель погоды", "ecommercePlatform": "Создайте платформу для электронной коммерции", "socialMediaApp": "Разработайте приложение для социальных сетей", "portfolioWebsite": "Создайте сайт-портфолио", "taskManagementApp": "Создайте приложение для управления задачами", "fitnessTracker": "Создайте фитнес-трекер", "recipeSharingPlatform": "Разработайте платформу для обмена рецептами", "travelBookingSite": "Создайте сайт для бронирования путешествий", "learningPlatform": "Создайте образовательную платформу", "musicStreamingApp": "Разработайте приложение для потоковой передачи музыки", "realEstateListing": "Создайте каталог недвижимости", "jobBoard": "Создайте сайт с вакансиями"}, "pleaseWait": "Пожалуйста, подождите...", "projectsInAll": "Проекты во всех", "projectsInCurrentFolder": "Проекты в {{folderName}}", "createYourFirstProject": "Создайте свой первый проект", "startCreateNewProjectDescription": "Начните создавать что-то удивительное. Ваши проекты будут отображаться здесь после их создания.", "createProjectBtn": "Новый проект", "publishedToContest": "Опубликовано в конкурсе", "publishToContest": "Опубликовать в конкурсе", "refreshSubmission": "Обновить отправку", "contestInformation": "Информация о конкурсе", "selectFolder": "Выберите папку", "folder": "Папка", "selectAFolder": "Выберите папку", "thisProject": "этот проект", "projectDeletedSuccessfully": "Проект успешно удален", "projectRemovedFromFolder": "Проект удален из папки", "unnamedProject": "Безымянный проект", "permanentDeletion": "Окончательное удаление", "removeFromFolder": "Удалить из {{folderName}}", "verifiedAccount": "Подтвержденный аккаунт", "hasSubmittedAMinimumOfOneProject": "Отправил как минимум один проект", "haveAtLeastActiveReferrals": "Имеется как минимум {{number}} активных рефералов", "GoToAffiliateDashBoard": "Перейти в партнерскую панель", "LikeOneProjectThatBelongsToAnotherUser": "Понравился проект, принадлежащий другому пользователю", "GoToContestPage": "Перейти на страницу конкурса", "ContestStatus": "Статус конкурса", "ShowcaseYourBestWork": "Покажите свою лучшую работу", "submissions": "Отправки", "qualifyConditions": "Условия квалификации", "completed": "Завершено", "collapseQualifyConditions": "Свернуть условия квалификации", "expandQualifyConditions": "Развернуть условия квалификации", "basicParticipation": "Базовое участие", "complete": "Завершено", "incomplete": "Не завершено", "forTop3Places": "Для первых 3 мест", "deleteSubmission": "Удалить отправку", "view": "Просмотр", "submitMoreProjectsToIncreaseYourChangesOfWining": "Отправьте больше проектов, чтобы увеличить свои шансы на победу!", "removeFromContest": "Удалить из конкурса?", "removeFromContestDescription": "Вы уверены, что хотите удалить <project>{{project}}</project> из конкурса? Это действие нельзя отменить.", "cardVerificationRequired": "Требуется проверка карты", "pleaseVerifyCard": "Пожалуйста, подтвердите свою карту, чтобы продолжить", "unlockFeaturesMessage": "Чтобы получить полный доступ ко всем функциям платформы, мы требуем быстрой проверки карты. Этот процесс полностью безопасен и обеспечивает плавный опыт работы на Biela.dev. Во время проверки с вашей карты не будет списано никаких средств.", "freeVerificationNotice": "Преимущества верификации", "accessToAllFeatures": "Полный доступ к функциям Biela.dev.", "enhancedFunctionality": "Улучшенная производительность платформы.", "quickSecureVerification": "Безопасный и зашифрованный процесс проверки.", "noCharges": "*Без комиссий и скрытых платежей", "verificationOnly": " — только проверка.", "verifyNow": "Подтвердить сейчас", "DropFilesToUpload": "Перетащите файлы для загрузки", "projectInfo": {"information": "Информация о проекте", "type": "Тип проекта", "complexity": "Сложность", "components": "Компоненты", "features": "Особенности", "confidenceScore": "Оценка уверенности", "estimationUncertainty": "Неопределенность оценки", "keyTechnologies": "Ключевые технологии"}, "projectMetrics": {"teamComposition": "Состав команды", "hoursBreakdown": "Распределение часов", "timeToMarket": "Время до выхода на рынок", "maintenance": "Обслуживание", "aiPowered": "С использованием ИИ", "developerLevel": "Уровень разработчика", "nextGenAI": "ИИ следующего поколения", "keyBenefits": "Ключевые преимущества", "instantDevelopment": "Мгновенная разработка", "noMaintenanceCosts": "Отсутствие затрат на обслуживание", "highConfidence": "Высокая уверенность", "productionReadyCode": "Код готов к производству", "immediate": "Немедленно", "uncertainty": "Неопределенность", "minimal": "Минимально"}, "loadingMessages": {"publish": ["Получение информации о проекте...", "Создание снимка экрана...", "Генерация резюме и описания..."], "refresh": ["Получение текущих данных о подаче...", "Обновление снимка экрана проекта...", "Обновление деталей проекта..."]}, "errorMessages": {"publish": "Не удалось опубликовать \"{{projectName}}\" в конкурс. Произошла ошибка при обработке вашего запроса.", "refresh": "Не удалось обновить подачу \"{{projectName}}\". Сервер не смог обновить информацию о проекте."}, "actions": {"refresh": {"title": "Обновление подачи", "successMessage": "Ваша подача проекта успешно обновлена.", "buttonText": "Просмотр обновленной подачи"}, "publish": {"title": "Публикация в конкурс", "successMessage": "Ваш проект был подан на конкурс.", "buttonText": "Просмотреть на странице конкурса"}}, "SupabaseConnect": "Supabase", "SupabaseDashboard": "Supabase", "RestoreApp": "Восстановить", "SaveApp": "Сохранить", "ForkChat": "Форк", "BielaTerminal": "<PERSON>ер<PERSON><PERSON><PERSON><PERSON> B<PERSON>", "UnitTesting": "Модульное тестирование", "InstallDependencies": "Установить зависимости", "InstallDependenciesDescription": "Устанавливает все необходимые пакеты для проекта с помощью", "BuildProject": "Собрать проект", "BuildProjectDescription": "Компилирует и оптимизирует проект для продакшена с помощью", "StartDevelopment": "Начать разработку", "StartDevelopmentDescription": "Запускает сервер разработки для предварительного просмотра в реальном времени с помощью", "NoProjectFound": "Проект с таким названием не найден.", "NoProjectFoundDescription": " Пожалуйста, проверьте орфографию и попробуйте снова.", "LotOfContext": "Ух ты, как много контекста!", "LotOfContextDescription1": "Похоже, ИИ достиг предела обработки для этого проекта. Большую часть пространства занимают импортированные файлы, а не сам чат. Повторный импорт проекта как нового удалит историю чата и освободит достаточно места для ещё нескольких запросов — но учтите, что файлы всё равно будут занимать большую часть доступного пространства.", "LotOfContextDescription2": "Чтобы всё работало бесперебойно, попробуйте повторно импортировать сейчас для чистого старта.", "limitReached": "Вы достигли своего лимита!", "deleteProjectsSupabase": "Пожалуйста, удалите некоторые проекты или увеличьте свой лимит в Supabase.", "goTo": "Перейти к", "clickProjectSettings": " нажмите на проект, Параметры проекта, прокрутите вниз и нажмите", "delete": "Удалить", "retrying": "Повторная попытка…", "retryConnection": "Повторить подключение", "RegisterPageTitle": "Регистрация – biela.dev", "RegisterPageDescription": "Создайте учетную запись на biela.dev и получите доступ ко всем функциям.", "SignUpHeading": "Регистрация", "AlreadyLoggedInRedirectHome": "Вы уже вошли в систему! Перенаправление на главную страницу...", "PasswordsMismatch": "Пароли не совпадают.", "FirstNameRequired": "Имя обязательно", "LastNameRequired": "Фамилия обязательна", "UsernameRequired": "Имя пользователя обязательно", "EmailRequired": "Электронная почта обязательна", "EmailInvalid": "Пожалуйста, введите действительный адрес электронной почты", "TooManyRequests": "Слишком много запросов, пожалуйста, попробуйте позже", "SomethingWentWrongMessage": "Что-то пошло не так, пожалуйста, попробуйте позже", "PasswordRequired": "Пароль обязателен", "ConfirmPasswordRequired": "Пожалуйста, подтвердите пароль", "AcceptTermsRequired": "Вы должны принять Условия обслуживания и Политику конфиденциальности", "CaptchaRequired": "Пожалуйста, пройдите CAPTCHA", "RegistrationFailed": "Регистрация не удалась", "EmailConfirmationSent": "Письмо с подтверждением отправлено! Подтвердите свою почту, а затем войдите в систему.", "RegistrationServerError": "Ошибка регистрации (сервер вернул false).", "SomethingWentWrong": "Что-то пошло не так", "CheckEmailHeading": "Проверьте почту для подтверждения регистрации", "CheckEmailDescription": "Мы отправили вам письмо с подтверждающей ссылкой.", "GoToHomepage": "Перейти на главную", "ReferralCodeOptional": "Реферальный код (необязательно)", "EnterReferralCode": "Введите реферальный код, если вас пригласил другой пользователь.", "PasswordPlaceholder": "Пароль", "ConfirmPasswordPlaceholder": "Подтвердите пароль", "CreateAccount": "Создать аккаунт", "AlreadyHaveAccountPrompt": "Уже есть аккаунт?", "Login": "Войти", "AcceptTermsPrefix": "Я принимаю", "TermsOfService": "Условия обслуживания", "AndSeparator": "и", "PrivacyPolicy": "Политику конфиденциальности", "LoginPageTitle": "Вход – biela.dev", "LoginPageDescription": "Войдите в свою учетную запись или зарегистрируйтесь на biela.dev, чтобы получить доступ ко всем функциям.", "LogInHeading": "Вход", "EmailOrUsernamePlaceholder": "Эл. почта / Имя пользователя", "ForgotPassword?": "Забыли пароль?", "LoginToProfile": "Вход в профиль", "UserNotConfirmed": "Учетная запись не подтверждена", "ConfirmEmailNotice": "Вам нужно подтвердить адрес электронной почты для активации аккаунта.", "ResendConfirmationEmail": "Отправить письмо с подтверждением снова", "ResendConfirmationSuccess": "Письмо с подтверждением отправлено повторно! Проверьте вашу почту.", "ResendConfirmationError": "Не удалось отправить письмо с подтверждением.", "LoginSuccess": "Успешный вход! Перенаправление...", "LoginFailed": "Ошибка входа", "LoginWithGoogle": "Войти через Google", "LoginWithGitHub": "Войти через GitHub", "SignUpWithGoogle": "Зарегистрироваться через Google", "SignUpWithGitHub": "Зарегистрироваться через GitHub", "Or": "ИЛИ", "NoAccountPrompt": "Нет аккаунта?", "SignMeUp": "Зарегистрироваться", "ForgotPasswordPageTitle": "Забыли пароль – biela.dev", "ForgotPasswordPageDescription": "Сбросьте пароль своей учетной записи biela.dev и восстановите доступ.", "BackToLogin": "Назад ко входу", "ForgotPasswordHeading": "Забыли пароль", "ForgotPasswordDescription": "Введите свой e-mail, и мы отправим вам ссылку для восстановления пароля.", "VerificationLinkSent": "Ссылка для подтверждения отправлена! Проверьте свою почту.", "EnterYourEmailPlaceholder": "Введите вашу почту", "Sending": "Отправка...", "SendVerificationCode": "Отправить код подтверждения", "InvalidConfirmationLink": "Недействительная ссылка подтверждения", "Back": "Назад", "ResetPassword": "Сбросить пароль", "ResetPasswordDescription": "Создайте новый пароль для вашей учетной записи", "NewPasswordPlaceholder": "Новый пароль", "ConfirmNewPasswordPlaceholder": "Подтвердите новый пароль", "ResetPasswordButton": "Сбросить пароль", "PasswordRequirements": "Пароль должен содержать не менее 8 символов, включая заглавную букву, строчную букву, цифру и специальный символ.", "PasswordUpdatedSuccess": "Пароль успешно обновлён!", "affiliateDashboard": "Панель партнёра", "userDashboard": "Панель пользователя", "returnToAffiliateDashboard": "Вернуться к панели партнёра", "returnToUserDashboard": "Вернуться к панели пользователя", "myProfile": "Мой профиль", "viewAndEditYourProfile": "Просмотреть и редактировать профиль", "billing": "Выставление счетов", "manageYourBillingInformation": "Управление платёжной информацией", "logout": "Выйти", "logoutDescription": "Выйти из своей учётной записи", "SupabaseNotAvailable": "Supabase в настоящее время недоступен. Пожалуйста, попробуйте позже.", "projectActions": {"invalidSlug": "Недопустимый идентификатор проекта.", "downloadSuccess": "Проект успешно загружен!", "downloadError": "Не удалось загрузить проект.", "exportSuccess": "Чат экспортирован! Проверьте папку загрузок.", "exportError": "Не удалось экспортировать чат.", "duplicateSuccess": "Чат успешно дублирован!", "duplicateError": "Не удалось дублировать чат."}, "enter_new_phone_number": "Введите новый номер", "enter_new_phone_number_below": "Введите новый номер телефона ниже:", "new_phone_placeholder": "Новый номер телефона", "enter_otp_code": "Введите OTP код", "confirm_phone_message": "Чтобы использовать аккаунт, подтвердите номер. Введите код, отправленный на ({{phone}}).", "wrong_phone": "Неправильный номер?", "resend_sms": "Повторно отправить SMS", "submit": "Отправить", "tokensAvailable": "доступные токены", "sectionTitle": "Управление Домена", "addDomainButton": "Добавить Имя Домена", "connectCustomDomainTitle": "Подключить Пользовательский Домен", "disclaimer": "Отказ от ответственности:", "disclaimerText": "Для успешной проверки необходимо правильно настроить все указанные выше правила DNS", "domainInputDescription": "Введите доменное имя, которое вы хотите подключить к этому проекту.", "domainLabel": "Имя Домена", "domainPlaceholder": "example.com", "cancelButton": "Отмена", "continueButton": "Добавить Имя Домена", "deployingText": "Развёртывание...", "addingText": "Добавление...", "verifyButtonText": "Проверить", "configureDnsTitle": "Настройка DNS-записей", "configureDnsDescription": "Добавьте следующие DNS-записи в ваш домен, чтобы подтвердить владение и подключить его к этому проекту.", "tableHeaderType": "Тип", "tableHeaderName": "Имя", "tableHeaderValue": "Значение", "note": "Примечание:", "noteText": "Изменения DNS могут распространяться до 48 часов. Однако чаще всего они вступают в силу в течение нескольких минут или часов.", "backButton": "Назад", "showDnsButton": "Показать Настройки DNS", "hideDnsButton": "Скрыть Настройки DNS", "removeButton": "Удалить", "dnsSettingsTitle": "Настройки DNS Домена", "removeDomainConfirmTitle": "Удалить Имя Домена", "removeConfirmationText": "Вы уверены, что хотите удалить доменное имя ", "importantCleanupTitle": "Важная Очистка DNS", "cleanupDescription": "После удаления этого домена из проекта не забудьте также удалить созданные DNS-записи. Это поможет поддерживать чистую конфигурацию DNS и избежать возможных конфликтов в будущем.", "confirmRemoveButton": "Удалить Имя Домена", "customConfigTitle": "Настройка Пользовательского Домена", "customConfigDescription": "Подключите свои собственные домены к проекту. Проект всегда будет доступен через домен по умолчанию от Biela, но пользовательские домены обеспечивают профессиональный имидж для ваших пользователей.", "defaultLabel": "По умолчанию", "statusActive": "Акти<PERSON><PERSON>н", "statusPending": "В ожидании", "lastVerifiedText": "Последняя проверка только что", "errorInvalidDomain": "Пожалуйста, введите корректное доменное имя (например, example.com)", "errorDuplicateDomain": "Это доменное имя уже подключено к вашему проекту", "errorAddFail": "Не удалось добавить доменное имя.", "successAdd": "Имя домена успешно добавлено! Домен подключён к проекту.", "benefitsTitle": "Преимущества Домена", "benefitSecurityTitle": "Повышенная Безопасность", "benefitSecurityDesc": "Все пользовательские домены автоматически защищены SSL-сертификатами.", "benefitPerformanceTitle": "Высокая Производительность", "benefitPerformanceDesc": "Глобальная CDN обеспечивает быструю загрузку проекта для пользователей по всему миру.", "benefitBrandingTitle": "Профессиональный Брендинг", "benefitBrandingDesc": "Используйте собственный домен для узнаваемого бренда.", "benefitAnalyticsTitle": "Интеграция с Аналитикой", "benefitAnalyticsDesc": "Пользовательские домены отлично работают с платформами аналитики.", "meta": {"index": {"title": "biela.dev | AI-Powered Web & App Builder – Build with Prompts", "description": "Преобразуйте свои идеи в живые веб-сайты или приложения с помощью biela.dev. Используйте AI-драйвенные подсказки для легкого создания настраиваемых цифровых продуктов."}, "login": {"title": "Вход в свою учетную запись biela.dev", "description": "Доступ к панели управления biela.dev для управления и создания проектов, созданных с помощью ИИ."}, "register": {"title": "Зарегистрируйтесь на biela.dev – Начните создавать с ИИ", "description": "Создайте свою учетную запись biela.dev, чтобы начать создавать веб-сайты и приложения с помощью AI-драйвенных подсказок."}, "dashboard": {"title": "Панель управления проектами – biela.dev", "description": "Управляйте своими веб-сайтами и приложениями, созданными с помощью ИИ, редактируйте живые проекты и отслеживайте свой историй создания – все в одном месте."}, "profile": {"title": "Ваш профиль – biela.dev", "description": "Просматривайте и обновляйте данные своего аккаунта biela.dev, управляйте настройками и персонализируйте свой опыт в разработке ИИ."}, "billing": {"title": "Оплата – biela.dev", "description": "Добавьте свою кредитную карту для подтверждения личности и получения полного доступа к функциям biela.dev — списания не будет."}}, "transferProject": "Поделиться копией", "transferSecurityNoteDescription": "Получатель получит полный доступ к копии этого проекта и всем связанным с ним ресурсам.", "transferProjectDescription": "Введите имя пользователя или адрес электронной почты человека, которому вы хотите передать копию этого проекта.", "transferProjectLabel": "Имя пользователя или электронная почта", "transferProjectPlaceholder": "johns<PERSON> или <EMAIL>", "transferButton": "Передать", "transferSecurityNote": "Примечание по безопасности:", "dontHavePermisionToTransfer": "У вас нет прав на передачу этого проекта", "transferProjectUserNotFound": "Пользователь {{ user }} не найден!", "transferErrorOwnAccount": "Вы не можете передать проект на свой собственный аккаунт.", "transferError": "Ошибка при передаче проекта", "transferSuccess": "Успешно передано пользователю {{ user }}", "enterValidEmailUsername": "Пожалуйста, введите имя пользователя или электронную почту", "enterMinValidEmailUsername": "Пожалуйста, введите действительное имя пользователя (не менее 3 символов) или адрес электронной почты", "youWillStillHaveAccess": "У вас по-прежнему будет доступ к исходному проекту", "newChangesWillNotAffect": "Новые изменения не повлияют на проект другого пользователя"}