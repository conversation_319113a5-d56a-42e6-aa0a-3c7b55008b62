{"Claude37Description": "IA mais poderosa para sites premium", "Claude37FeatureFirst": "Código de alta qualidade", "Claude37FeatureSecond": "Funcionalidade avançada", "Claude37FeatureThird": "Design superior", "Claude37Performance": "Excelente", "Claude37Cost": "Premium", "Claude37ContextWindow": "Janela de contexto padrão (200K tokens)", "GeminiProDescription": "IA versátil com grandes capacidades e amplo contexto", "GeminiProFeatureFirst": "<PERSON><PERSON><PERSON> con<PERSON>", "GeminiProFeatureSecond": "Lida com entradas complexas", "GeminiProFeatureThird": "Boa qualidade de design", "GeminiProPerformance": "<PERSON><PERSON> bom", "GeminiProCost": "Premium intermediário", "GeminiProContextWindow": "Janela de contexto grande (1M+ tokens)", "GeminiFlashDescription": "Opção mais rápida e econômica", "GeminiFlashFeatureFirst": "Geração rápida", "GeminiFlashFeatureSecond": "Funcionalidade básica", "GeminiFlashFeatureThird": "Designs simples", "GeminiFlashPerformance": "Bo<PERSON>", "GeminiFlashCost": "Econômico", "GeminiFlashContextWindow": "Janela de contexto grande (1M+ tokens)"}