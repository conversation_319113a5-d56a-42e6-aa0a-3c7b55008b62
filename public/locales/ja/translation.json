{"whatWouldYouLikeToBuild": "あなたのアイデアを数分でライブウェブサイトやアプリに変えよう", "whatWouldYouLikeToBuildSubtitle": "もしあなたが<1>想像</1>できるなら、<5>コード化</5>もできる。", "fromIdeaToDeployment": "無料で始めよう。何でもコードに。スキルをチャンスに変えよう。", "codePlaceholder": "もしあなたが想像できるなら、BIELAはそれをコード化できます。今日は何をしましょうか？", "defaultPlaceholder": "今日はどのようにお手伝いできますか？一緒に素晴らしいことを実現しましょう", "checkingFeatures": "機能をチェック中", "checklists": "チェックリスト", "runUnitTestsSuggestionTitle": "提案", "runUnitTestsSuggestionMessage": "プロジェクトのユニットテストを実行しますか？", "runUnitTestsPrimaryButton": "ユニットテストを実行する", "runUnitTestsSecondaryButton": "却下する", "createDatabaseTitle": "データベースの作成", "createDatabaseMessage": "プロジェクト用のデータベースを作成しますか？", "createDatabasePrimaryButton": "データベースを作成する", "createDatabaseSecondaryButton": "却下する", "extendedThinking": "拡張思考", "extendedThinkingTooltip": "AIが応答する前により深く考えるようにする", "firstResponseOnly": "最初の応答のみ", "always": "常に", "attachFile": "ファイルを添付", "voiceInput": "音声入力", "selectLanguage": "音声入力の言語を選択", "languageSelectionDisabled": "録音中は言語選択が無効", "notAvailableInFirefox": "この機能はFirefoxでは利用できませんが、ChromeとSafariでは利用できます", "enhancePrompt": "プロンプトを強化", "cleanUpProject": "プロジェクトを整理", "showPrompt": "プロンプトを表示する", "hidePrompt": "プロンプトを隠す", "sendButton": "送信", "abortButton": "キャンセル", "inspirationTitle": "インスピレーションが必要ですか？ 次の例を試してみてください：", "cleanUpPrompt": "各ファイルが300行を超えないようにプロジェクトをクリーンアップしてください。大きなファイルは、完全な機能性を保ちながら、小さなモジュールコンポーネントにリファクタリングします。不要なファイル、コード、コンポーネント、冗長なデータをすべて特定し削除してください。既存のシステムに影響を与えずに、すべてのコンポーネントが正しく接続され動作していることを確認してください。変更がエラーを引き起こさないか、既存の機能を壊さないかを検証し、コードの整合性を維持してください。目標は、効率性、保守性、明瞭さの点でプロジェクトを最適化することです。", "checklistPrompt": "最初のプロンプトを確認し、目的を一つ一つ理解して、完了した項目には緑のチェック、残っている項目には赤のチェックを付けたチェックリストを作成してください。", "personalPortfolioIdea": "ダークテーマの個人ポートフォリオサイトを作成する", "recipeFinderIdea": "材料に基づいて料理を提案するレシピ検索アプリを構築する", "weatherDashboardIdea": "アニメーション背景付きの天気ダッシュボードをデザインする", "habitTrackerIdea": "進捗の可視化ができる習慣トラッカーを開発する", "loading": "読み込み中", "error": "エラー", "succes": "成功！", "tryAgain": "もう一度試してください", "dashboard": "ダッシュボード", "getStartedTitle": "はじめる", "getStartedSub": "Biela.devの仕組みを探る", "createProject": "新しいプロジェクトを作成する", "createProjectSub": "ゼロから始める", "editProjectName": "プロジェクト名を編集", "editName": "名前を編集", "uploadProject": "プロジェクトをアップロードする", "uploadProjectSub": "既存のプロジェクトをインポートする", "importChat": "チャットをインポートする", "importChatSub": "既存のチャットをインポートする", "createFolder": "新しいフォルダを作成する", "createFolderSub": "プロジェクトを整理する", "cancel": "キャンセル", "changeFolder": "フォルダを変更する", "save": "保存する", "importing": "インポート中...", "importFolder": "フォルダをインポートする", "giveTitle": "タイトルを付ける", "projects": "プロジェクト", "searchProjects": "プロジェクトを検索する...", "becomeAffiliate": "アフィリエイトになる", "exclusiveGrowth": "限定の成長特典", "lifetimeEarnings": "生涯収入", "highCommissions": "高いコミッション", "earnCommission": "初回販売で50%のコミッションを獲得する", "joinAffiliateProgram": "アフィリエイトプログラムに参加する", "folders": "フォルダ", "organizeProjects": "プロジェクトをカテゴリー別に整理する", "createNewFolder": "新しいフォルダを作成する", "enterFolderName": "フォルダ名を入力する", "editFolder": "フォルダを編集する", "deleteFolder": "フォルダを削除する", "all": "すべて", "webProjects": "ウェブプロジェクト", "mobilepps": "モバイルアプリ", "developmentComparison": "開発の比較", "traditionalVsAI": "従来型 vs AI", "traditional": "従来型", "standardApproach": "標準的なアプローチ", "developmentCost": "開発コスト", "developmentTime": "開発時間", "costSavings": "コスト削減", "reducedCosts": "削減されたコスト", "timeSaved": "節約された時間", "fasterDelivery": "より速い納品", "bielaDevAI": "Biela.dev AI", "nextGenDevelopment": "次世代開発", "developmentCosts": "開発費用", "openInGitHub": "GitHubで開く", "downloadProject": "プロジェクトをダウンロードする", "duplicateProject": "プロジェクトを複製する", "openProject": "プロジェクトを開く", "deleteProject": "プロジェクトを削除する", "confirmDelete": "このフォルダを削除しますか？", "invoicePreview": "請求書のプレビュー", "settings": {"title": "設定", "deployment": {"AdvancedSettings": {"advanced-settings": "詳細設定", "configure-advanced-deployment-options": "詳細なデプロイオプションを設定する", "server-configuration": "サーバー構成", "memory-limit": "メモリ制限", "region": "リージョン", "security-settings": "セキュリティ設定", "enable-ddos-protection": "DDoS保護を有効にする", "protect-against-distributed": "分散型サービス拒否攻撃から保護する", "ip-whitelisting": "IPホワイトリスト", "restrict-acces-to": "特定のIPアドレスへのアクセスを制限する", "deployment-options": "デプロイオプション", "auto-deploy": "自動デプロイ", "automatically-deploy-when": "メインブランチへのプッシュ時に自動的にデプロイする", "preview-deployments": "プレビュー展開を表示する", "create-preview-deployments": "プルリクエスト用のプレビュー展開を作成する"}, "BuildSettings": {"build-and-deployment-settings": "ビルド＆デプロイ設定", "build-command": "ビルドコマンド", "override": "上書きする", "output-directory": "出力ディレクトリ", "override2": "上書きする"}, "DatabaseConfiguration": {"database-configuration": "データベース構成", "configure-your-db-connections": "データベース接続と設定を構成する", "database-type": "データベースの種類", "connection-string": "接続文字列", "your-db-credentials": "あなたのデータベース認証情報は暗号化され、安全に保存されています", "database-settings": "データベース設定", "pool-size": "プールサイズ", "require": "必須", "prefer": "推奨", "disable": "無効にする", "add-database": "データベースを追加する"}, "DomainSettings": {"domain-settings": "ドメイン設定", "configure-your-custom-domain": "カスタムドメインとSSL証明書を設定する", "custom-domain": "カスタムドメイン", "add": "追加する", "ssl-certificate": "SSL証明書", "auto-renew-ssl-certificates": "SSL証明書を自動更新する", "auto-renew-before-expiry": "有効期限前に自動的にSSL証明書を更新する", "force-https": "HTTPSを強制する", "redirect-all-http-traffic": "すべてのHTTPトラフィックをHTTPSにリダイレクトする", "active-domain": "アクティブなドメイン", "remove": "削除する"}, "EnvironmentVariables": {"environment-variables": "環境変数", "configure-environment-variables": "デプロイ用の環境変数を設定する", "all-enviroments": "すべての環境", "environment-variables2": "環境変数", "preview": "プレビュー", "development": "開発", "create-new": "新規作成", "key": "キー", "value": "値", "save-variable": "変数を保存する", "you-can-also-import": ".envファイルからも変数をインポートできます：", "import-env-file": ".envファイルをインポートする"}, "ProjectConfiguration": {"project-configuration": "プロジェクト構成", "config-your-project-settings": "プロジェクトの設定とデプロイオプションを構成する", "project-url": "プロジェクトのURL", "framework": "フレームワーク", "repo": "リポジトリ", "branch": "ブランチ", "main": "main", "development": "development", "staging": "staging"}}, "identity": {"unverified": {"title": "身分証明", "description": "Biela.devを利用するために身分を証明してください", "subtitle": "安全な認証プロセス", "processServers": "カード情報はBielaのサーバーではなく、Stripeのサーバーに安全に保存されます", "processCharge": "サブスクリプションに対する明示的な同意なしにカードに料金は請求されません", "processBenefits": "Biela.devは2025年5月15日まで認証済みアカウントに対して完全に無料です", "verifyStripe": "クレジットカードまたはデビットカードで確認", "verifyStripeDescription": "認証のために支払い方法を接続してください", "verifyNow": "今すぐ認証"}, "verified": {"title": "身分証明済み", "description": "あなたの身分は正常に確認されました", "paymentMethod": "支払い方法", "cardEnding": "カードの末尾", "updatePayment": "支払い方法の更新", "untilDate": "2025年5月15日まで", "freeAccess": "無料アクセス", "freeAccessDescription": "Biela.devのフルアクセスを無料でお楽しみください", "secureStorage": "安全な保存", "secureStorageDescription": "あなたのカード情報はBielaのサーバーではなく、Stripeのサーバーに安全に保存されます。明示的な同意なしにサブスクリプションのためにカードが請求されることはありません。", "subscriptionAvailable": "サブスクリプションは2025年5月15日から利用可能になります。"}, "connectingToStripe": "Stripeに接続中..."}, "tabs": {"billing": "請求", "profile": "プロフィール", "deployment": "デプロイ", "identity": "アイデンティティ"}}, "help": {"title": "どのようにお手伝いできますか？", "searchPlaceholder": "ドキュメントを検索する...", "categories": {"getting-started": {"title": "はじめに", "description": "Biela.devの基本的な使い方を学びましょう", "articles": ["クイックスタートガイド", "プラットフォーム概要", "最初のプロジェクトの作成", "AI開発の理解"]}, "ai-development": {"title": "AI開発", "description": "AIを活用した開発をマスターしましょう", "articles": ["効果的なプロンプトの書き方", "コード生成のベストプラクティス", "AIデバッグのヒント", "高度なAI機能"]}, "project-management": {"title": "プロジェクト管理", "description": "プロジェクトを整理して管理しましょう", "articles": ["プロジェクトの構造", "チームでの協力", "バージョン管理", "デプロイオプション"]}}, "channels": {"docs": {"name": "ドキュメント", "description": "包括的なガイドとAPIリファレンス"}, "community": {"name": "コミュニティ", "description": "他の開発者とつながりましょう"}, "github": {"name": "GitHub", "description": "問題を報告し、貢献しましょう"}}, "support": {"title": "それでもお困りですか？", "description": "当社のサポートチームは24時間365日、あなたの質問や問題に対応しています。", "button": "サポートに連絡する"}}, "getStarted": {"title": "Biela.devの仕組みを探る", "description": "Biela.devを使えば、数分であなたのアプリが作成できます。当社のAIは、セットアップからデプロイまで、開発プロセス全体を自動化します。これが、当社のAIがあなたのアプリを手軽に構築する方法です！", "features": {"docs": {"title": "開発者向けドキュメント", "description": "わかりやすいガイド、ヒント、ベストプラクティスでBiela.devの使い方を学びましょう。初心者にも、経験豊富な開発者にも最適です！", "cta": "開発者ドキュメントを探る"}, "support": {"title": "フィードバックとサポート", "description": "お困りの場合やご意見がある場合は、サポートに連絡してBiela.devの改善にご協力ください。", "cta": "フィードバックを送る"}, "platform": {"title": "プラットフォーム機能", "description": "Biela.devが提供する強力なツールを活用して、手間なくウェブサイトやアプリを作成しましょう。AIにコード作成を任せましょう！", "cta": "機能を探る"}}, "video": {"title": "クイックスタートビデオ", "description": "Biela.devがどのようにあなたのために構築するかをご覧ください―手間なくアプリとウェブを作成！", "cta": "チュートリアルを見る"}, "guide": {"title": "クイックスタートガイド", "steps": {"setup": {"title": "プロジェクトを即座にセットアップする", "description": "数秒で開発環境を整えましょう"}, "generate": {"title": "AIでフルスタックコードを生成する", "description": "AIにプロダクションレディのコードを書かせましょう"}, "features": {"title": "瞬時に機能を生成する", "description": "シンプルなプロンプトで複雑な機能を追加しましょう"}, "editor": {"title": "ノーコード＆ローコードエディター", "description": "ビジュアルまたはコードでアプリを編集しましょう"}, "optimize": {"title": "リアルタイムで最適化・テストする", "description": "アプリが完璧に動作することを確認しましょう"}, "deploy": {"title": "ワンクリックでデプロイする", "description": "自動デプロイで即座にライブにしましょう"}}}, "faq": {"title": "よくある質問", "questions": {"what": {"question": "Biela.devとは何ですか？", "answer": "Biela.devは、プログラミングができなくてもウェブサイトやアプリを作成するのを支援するAI搭載プラットフォームです。コード作成からデプロイまで、開発プロセス全体を自動化します。"}, "experience": {"question": "Biela.devを使うのにプログラミング経験は必要ですか？", "answer": "いいえ！Biela.devは初心者にも経験豊富な開発者にも対応するよう設計されています。AIの支援を受けて構築するか、生成されたコードを必要に応じてカスタマイズできます。"}, "projects": {"question": "どのような種類のプロジェクトを作成できますか？", "answer": "ウェブサイト、ウェブアプリ、モバイルアプリ、SaaSプラットフォーム、eコマースサイト、管理ダッシュボードなど、さまざまなものを作成できます。"}, "edit": {"question": "Biela.devが生成したコードは編集できますか？", "answer": "はい！Biela.devは、AIが生成したコードをカスタマイズしたり、ノーコード/ローコードエディターを使って簡単に変更することができます。"}, "deployment": {"question": "デプロイはどのように機能しますか？", "answer": "ワンクリックでBiela.devがプロジェクトをデプロイし、オンラインにして利用可能にします。サーバーの手動設定は必要ありません！"}, "pricing": {"question": "Biela.devは無料ですか？", "answer": "Biela.devは基本機能を備えた無料プランを提供しています。より高度なツールやリソースが必要な場合は、プレミアムプランにアップグレードできます。"}, "integrations": {"question": "サードパーティのツールやデータベースは統合できますか？", "answer": "はい！Biela.devは、MongoDB、Firebase、Supabaseなどの人気データベースやサードパーティAPIとの統合に対応しています。"}, "help": {"question": "問題が発生した場合、どこでサポートを受けられますか？", "answer": "開発者向けドキュメント、クイックスタートガイドを参照するか、ヘルプセンターを通じてサポートに連絡してください。"}}}, "cta": {"title": "始める準備はできましたか？", "description": "Biela.devで最初のプロジェクトを作成し、開発の未来を体験してください。", "button": "新しいプロジェクトを作成する"}}, "confirmDeleteProject": "プロジェクトの削除を確認", "confirmRemoveFromFolder": "フォルダーからの削除を確認", "deleteProjectWarning": "{{projectName}} を完全に削除してもよろしいですか？", "removeFromFolderWarning": "{{projectName}} を {{folderName}} から削除してもよろしいですか？", "confirm": "確認", "confirmDeleteFolder": "フォルダーの削除を確認", "deleteFolderWarning": "{{folderName}} を削除してもよろしいですか？この操作は元に戻せません。", "folderDeletedSuccessfully": "フォルダーが正常に削除されました", "downloadChat": "チャットをダウンロード", "inactiveTitle": "このタブは非アクティブです", "inactiveDescription": "下のボタンをクリックしてこのタブをアクティブにし、アプリの使用を続けてください。", "inactiveButton": "このタブを使用する", "suggestions": {"weatherDashboard": "天気ダッシュボードを作成する", "ecommercePlatform": "Eコマースプラットフォームを構築する", "socialMediaApp": "ソーシャルメディアアプリをデザインする", "portfolioWebsite": "ポートフォリオサイトを生成する", "taskManagementApp": "タスク管理アプリを作成する", "fitnessTracker": "フィットネストラッカーを構築する", "recipeSharingPlatform": "レシピ共有プラットフォームをデザインする", "travelBookingSite": "旅行予約サイトを作成する", "learningPlatform": "学習プラットフォームを構築する", "musicStreamingApp": "音楽ストリーミングアプリをデザインする", "realEstateListing": "不動産リストを作成する", "jobBoard": "求人掲示板を構築する"}, "pleaseWait": "お待ちください...", "projectsInAll": "すべてのプロジェクト", "projectsInCurrentFolder": "{{folderName}} のプロジェクト", "createYourFirstProject": "最初のプロジェクトを作成", "startCreateNewProjectDescription": "素晴らしいものを作り始めましょう。プロジェクトを作成すると、ここに表示されます。", "createProjectBtn": "新しいプロジェクト", "publishedToContest": "コンテストに公開", "publishToContest": "コンテストに公開する", "refreshSubmission": "送信を更新", "contestInformation": "コンテスト情報", "selectFolder": "フォルダーを選択", "folder": "フォルダー", "selectAFolder": "フォルダーを選択", "thisProject": "このプロジェクト", "projectDeletedSuccessfully": "プロジェクトが正常に削除されました", "projectRemovedFromFolder": "フォルダーからプロジェクトが削除されました", "unnamedProject": "名前のないプロジェクト", "permanentDeletion": "完全削除", "removeFromFolder": "{{folderName}} から削除", "verifiedAccount": "認証済みアカウント", "hasSubmittedAMinimumOfOneProject": "少なくとも1つのプロジェクトを提出しました", "haveAtLeastActiveReferrals": "少なくとも{{number}}件のアクティブな紹介があります", "GoToAffiliateDashBoard": "アフィリエイトダッシュボードに移動", "LikeOneProjectThatBelongsToAnotherUser": "他のユーザーのプロジェクトに「いいね」をする", "GoToContestPage": "コンテストページに移動", "ContestStatus": "コンテストのステータス", "ShowcaseYourBestWork": "最高の作品を披露する", "submissions": "提出物", "qualifyConditions": "資格条件", "completed": "完了", "collapseQualifyConditions": "資格条件を折りたたむ", "expandQualifyConditions": "資格条件を展開する", "basicParticipation": "基本的な参加", "complete": "完了", "incomplete": "未完了", "forTop3Places": "トップ3のために", "deleteSubmission": "提出物を削除", "view": "表示", "submitMoreProjectsToIncreaseYourChangesOfWining": "勝つチャンスを増やすために、さらに多くのプロジェクトを提出してください！", "removeFromContest": "コンテストから削除しますか？", "removeFromContestDescription": "本当に<project>{{project}}</project>をコンテストから削除しますか？この操作は元に戻せません。", "cardVerificationRequired": "カード認証が必要です", "pleaseVerifyCard": "続行するにはカードを認証してください", "unlockFeaturesMessage": "すべてのプラットフォーム機能への完全なアクセスを解除するには、カードの迅速な確認が必要です。このプロセスは完全に安全で、Biela.devでスムーズな体験を保証します。確認中にカードに請求されることはありません。", "freeVerificationNotice": "検証の利点", "accessToAllFeatures": "Biela.dev の機能への完全アクセス。", "enhancedFunctionality": "プラットフォームのパフォーマンス向上。", "quickSecureVerification": "安全で暗号化された検証プロセス。", "noCharges": "*追加料金や隠れた費用はありません", "verificationOnly": " — 検証のみ。", "verifyNow": "今すぐ認証", "DropFilesToUpload": "アップロードするファイルをドロップ", "projectInfo": {"information": "プロジェクト情報", "type": "プロジェクトの種類", "complexity": "複雑さ", "components": "コンポーネント", "features": "特徴", "confidenceScore": "信頼スコア", "estimationUncertainty": "見積りの不確実性", "keyTechnologies": "主要技術"}, "projectMetrics": {"teamComposition": "チーム構成", "hoursBreakdown": "作業時間の内訳", "timeToMarket": "市場投入までの時間", "maintenance": "メンテナンス", "aiPowered": "AI搭載", "developerLevel": "開発者レベル", "nextGenAI": "次世代AI", "keyBenefits": "主な利点", "instantDevelopment": "即時開発", "noMaintenanceCosts": "メンテナンス費用なし", "highConfidence": "高い信頼性", "productionReadyCode": "本番対応コード", "immediate": "即時", "uncertainty": "不確実性", "minimal": "最小限"}, "loadingMessages": {"publish": ["プロジェクト情報を取得中...", "スクリーンショットを作成中...", "要約と説明を生成中..."], "refresh": ["現在の提出データを取得中...", "プロジェクトのスクリーンショットを更新中...", "プロジェクトの詳細を更新中..."]}, "errorMessages": {"publish": "「{{projectName}}」をコンテストに公開できませんでした。リクエストの処理中に問題が発生しました。", "refresh": "「{{projectName}}」の提出物を更新できませんでした。サーバーがプロジェクト情報を更新できませんでした。"}, "actions": {"refresh": {"title": "提出物の更新中", "successMessage": "プロジェクトの提出が正常に更新されました。", "buttonText": "更新された提出物を見る"}, "publish": {"title": "コンテストへの公開中", "successMessage": "プロジェクトがコンテストに提出されました。", "buttonText": "コンテストページで表示"}}, "SupabaseConnect": "Supabase に接続", "SupabaseDashboard": "Supabase ダッシュボード", "RestoreApp": "復元", "SaveApp": "保存", "ForkChat": "フォーク", "BielaTerminal": "Bielaターミナル", "UnitTesting": "ユニットテスト", "InstallDependencies": "依存関係をインストール", "InstallDependenciesDescription": "プロジェクトに必要なすべてのパッケージを次の方法でインストールします", "BuildProject": "プロジェクトをビルド", "BuildProjectDescription": "プロジェクトを本番用にコンパイルおよび最適化します（使用する方法）", "StartDevelopment": "開発を開始", "StartDevelopmentDescription": "ライブプレビュー用に開発サーバーを起動します（使用）", "NoProjectFound": "その名前のプロジェクトは見つかりませんでした。", "NoProjectFoundDescription": " スペルミスがないか確認して、もう一度お試しください。", "LotOfContext": "うわっ、すごい量のコンテキストですね！", "LotOfContextDescription1": "このプロジェクトではAIが処理の限界に達したようです。チャット自体ではなく、インポートされたファイルがほとんどの容量を使用しています。プロジェクトを新規として再インポートするとチャット履歴が削除され、あと少しだけプロンプト用のスペースが確保されます — ただし、ファイルは引き続き大部分の容量を占有します。", "LotOfContextDescription2": "スムーズに動作させるには、今すぐ再インポートして新たに開始してください。", "limitReached": "上限に達しました！", "deleteProjectsSupabase": "プロジェクトをいくつか削除するか、Su<PERSON>baseの上限を増やしてください。", "goTo": "移動先", "clickProjectSettings": " プロジェクトをクリックし、プロジェクト設定に移動し、下までスクロールしてクリック", "delete": "削除", "retrying": "再試行中…", "retryConnection": "接続を再試行", "RegisterPageTitle": "登録 – biela.dev", "RegisterPageDescription": "biela.dev にアカウントを作成して、すべての機能を利用しましょう。", "SignUpHeading": "サインアップ", "AlreadyLoggedInRedirectHome": "すでにログインしています！ホームにリダイレクト中...", "PasswordsMismatch": "パスワードが一致しません。", "FirstNameRequired": "名は必須項目です", "LastNameRequired": "姓は必須項目です", "UsernameRequired": "ユーザー名は必須です", "EmailRequired": "メールアドレスは必須です", "EmailInvalid": "有効なメールアドレスを入力してください", "TooManyRequests": "リクエストが多すぎます。しばらくしてから再試行してください", "SomethingWentWrongMessage": "問題が発生しました。後でもう一度お試しください", "PasswordRequired": "パスワードは必須です", "ConfirmPasswordRequired": "パスワードを確認してください", "AcceptTermsRequired": "利用規約およびプライバシーポリシーへの同意が必要です", "CaptchaRequired": "CAPTCHA を完了してください", "RegistrationFailed": "登録に失敗しました", "EmailConfirmationSent": "確認メールを送信しました！メールを確認してからログインしてください。", "RegistrationServerError": "登録に失敗しました（サーバーが false を返しました）。", "SomethingWentWrong": "問題が発生しました", "CheckEmailHeading": "登録を確認するためにメールをチェックしてください", "CheckEmailDescription": "確認リンクを含むメールを送信しました。", "GoToHomepage": "ホームページへ", "ReferralCodeOptional": "紹介コード（任意）", "EnterReferralCode": "既存ユーザーから招待された場合は紹介コードを入力してください。", "PasswordPlaceholder": "パスワード", "ConfirmPasswordPlaceholder": "パスワード確認", "CreateAccount": "アカウントを作成", "AlreadyHaveAccountPrompt": "すでにアカウントをお持ちですか？", "Login": "ログイン", "AcceptTermsPrefix": "私は同意します", "TermsOfService": "利用規約", "AndSeparator": "と", "PrivacyPolicy": "プライバシーポリシー", "LoginPageTitle": "ログイン – biela.dev", "LoginPageDescription": "アカウントにアクセス、または biela.dev にログインしてすべての機能を利用しましょう。", "LogInHeading": "ログイン", "EmailOrUsernamePlaceholder": "メールアドレス / ユーザー名", "ForgotPassword?": "パスワードをお忘れですか？", "LoginToProfile": "プロフィールにログイン", "UserNotConfirmed": "アカウントが確認されていません", "ConfirmEmailNotice": "アカウントを有効化するにはメールアドレスの確認が必要です。", "ResendConfirmationEmail": "確認メールを再送する", "ResendConfirmationSuccess": "確認メールを再送しました！受信トレイを確認してください。", "ResendConfirmationError": "確認メールの再送に失敗しました。", "LoginSuccess": "ログイン成功！リダイレクト中...", "LoginFailed": "ログインに失敗しました", "LoginWithGoogle": "Google でログイン", "LoginWithGitHub": "GitHub でログイン", "SignUpWithGoogle": "Googleで登録", "SignUpWithGitHub": "GitHubで登録", "Or": "または", "NoAccountPrompt": "アカウントをお持ちでないですか？", "SignMeUp": "サインアップする", "ForgotPasswordPageTitle": "パスワードをお忘れですか – biela.dev", "ForgotPasswordPageDescription": "biela.dev のパスワードをリセットしてアクセスを回復しましょう。", "BackToLogin": "ログインに戻る", "ForgotPasswordHeading": "パスワードをお忘れですか", "ForgotPasswordDescription": "メールアドレスを入力すると、パスワードリセット用の確認リンクをお送りします。", "VerificationLinkSent": "確認リンクを送信しました！メールを確認してください。", "EnterYourEmailPlaceholder": "メールアドレスを入力", "Sending": "送信中...", "SendVerificationCode": "確認コードを送信", "InvalidConfirmationLink": "無効な確認リンクです", "Back": "戻る", "ResetPassword": "パスワードをリセット", "ResetPasswordDescription": "新しいパスワードを設定してください", "NewPasswordPlaceholder": "新しいパスワード", "ConfirmNewPasswordPlaceholder": "新しいパスワードを確認", "ResetPasswordButton": "パスワードをリセット", "PasswordRequirements": "パスワードは8文字以上で、大文字、小文字、数字、記号を含める必要があります。", "PasswordUpdatedSuccess": "パスワードが正常に更新されました！", "affiliateDashboard": "アフィリエイトダッシュボード", "userDashboard": "ユーザーダッシュボード", "returnToAffiliateDashboard": "アフィリエイトダッシュボードに戻る", "returnToUserDashboard": "ユーザーダッシュボードに戻る", "myProfile": "マイプロフィール", "viewAndEditYourProfile": "プロフィールを表示・編集する", "billing": "請求", "manageYourBillingInformation": "請求情報を管理する", "logout": "ログアウト", "logoutDescription": "アカウントからログアウトする", "SupabaseNotAvailable": "Supabaseは現在利用できません。後でもう一度お試しください。", "projectActions": {"invalidSlug": "プロジェクトスラッグが無効です。", "downloadSuccess": "プロジェクトが正常にダウンロードされました！", "downloadError": "プロジェクトのダウンロードに失敗しました。", "exportSuccess": "チャットがエクスポートされました！ダウンロードフォルダを確認してください。", "exportError": "チャットのエクスポートに失敗しました。", "duplicateSuccess": "チャットが正常に複製されました！", "duplicateError": "チャットの複製に失敗しました。"}, "enter_new_phone_number": "新しい電話番号を入力", "enter_new_phone_number_below": "新しい電話番号を入力してください：", "new_phone_placeholder": "新しい電話番号", "enter_otp_code": "OTPコードを入力", "confirm_phone_message": "アカウントを使用するには電話番号の確認が必要です。({{phone}}) に送られたコードを入力してください。", "wrong_phone": "電話番号が違いますか？", "resend_sms": "SMSを再送", "submit": "送信", "tokensAvailable": "利用可能なトークン", "sectionTitle": "ドメイン管理", "addDomainButton": "ドメイン名を追加", "connectCustomDomainTitle": "カスタムドメインを接続", "disclaimer": "免責事項：", "disclaimerText": "正常に検証するには、上記のすべての DNS ルールを正しく設定する必要があります", "domainInputDescription": "このプロジェクトに接続したいドメイン名を入力してください。", "domainLabel": "ドメイン名", "domainPlaceholder": "example.com", "cancelButton": "キャンセル", "continueButton": "ドメイン名を追加", "deployingText": "デプロイ中...", "addingText": "追加中...", "verifyButtonText": "確認", "configureDnsTitle": "DNSレコードの設定", "configureDnsDescription": "以下のDNSレコードをドメインに追加して所有権を確認し、このプロジェクトに接続してください。", "tableHeaderType": "タイプ", "tableHeaderName": "名前", "tableHeaderValue": "値", "note": "注意：", "noteText": "DNSの変更は最大48時間かかることがありますが、通常は数分から数時間で反映されます。", "backButton": "戻る", "showDnsButton": "DNS設定を表示", "hideDnsButton": "DNS設定を非表示", "removeButton": "削除", "dnsSettingsTitle": "ドメインDNS設定", "removeDomainConfirmTitle": "ドメイン名の削除", "removeConfirmationText": "本当にこのドメイン名を削除しますか ", "importantCleanupTitle": "重要なDNSクリーンアップ", "cleanupDescription": "このプロジェクトからドメインを削除した後、設定時に作成したDNSレコードも削除してください。これにより、DNS設定がクリーンに保たれ、将来的な衝突を防ぐことができます。", "confirmRemoveButton": "ドメイン名を削除", "customConfigTitle": "カスタムドメインの設定", "customConfigDescription": "独自ドメインをこのプロジェクトに接続します。プロジェクトは常にBielaのデフォルトドメインでアクセス可能ですが、カスタムドメインを使えば、ユーザーにプロフェッショナルなブランド体験を提供できます。", "defaultLabel": "デフォルト", "statusActive": "アクティブ", "statusPending": "保留中", "lastVerifiedText": "直前に確認済み", "errorInvalidDomain": "有効なドメイン名を入力してください（例：example.com）", "errorDuplicateDomain": "このドメイン名はすでにプロジェクトに接続されています", "errorAddFail": "ドメイン名の追加に失敗しました。", "successAdd": "ドメイン名が正常に追加されました！このプロジェクトに接続されました。", "benefitsTitle": "ドメインの利点", "benefitSecurityTitle": "高度なセキュリティ", "benefitSecurityDesc": "すべてのカスタムドメインは自動的にSSL証明書で保護されます。", "benefitPerformanceTitle": "高速パフォーマンス", "benefitPerformanceDesc": "グローバルCDNにより、世界中のユーザーに高速な読み込みが可能です。", "benefitBrandingTitle": "プロフェッショナルなブランディング", "benefitBrandingDesc": "一貫したブランド体験のために独自ドメインを使用できます。", "benefitAnalyticsTitle": "分析統合", "benefitAnalyticsDesc": "カスタムドメインは分析プラットフォームとシームレスに連携します。", "meta": {"index": {"title": "biela.dev | AIパワードウェブ＆アプリビルダー – プロンプトで構築", "description": "biela.devでアイデアをライブウェブサイトやアプリに変換します。AI駆動のプロンプトを使用して、カスタムデジタル製品を手軽に構築します。"}, "login": {"title": "biela.devアカウントにログイン", "description": "biela.devダッシュボードにアクセスして、AI生成プロジェクトを管理および構築します。"}, "register": {"title": "biela.devに登録 – AIでの構築を開始", "description": "biela.devアカウントを作成して、AIパワードのプロンプトを使用してウェブサイトやアプリの構築を開始します。"}, "dashboard": {"title": "プロジェクトダッシュボード – biela.dev", "description": "AI構築のウェブサイトやアプリを管理し、ライブプロジェクトを編集し、構築履歴を追跡—すべて一つの場所で。"}, "profile": {"title": "あなたのプロフィール – biela.dev", "description": "biela.dev のアカウント情報を確認・更新し、設定を管理して、AI 開発の体験をパーソナライズしましょう。"}, "billing": {"title": "請求情報 – biela.dev", "description": "本人確認と biela.dev の全機能を利用するためにクレジットカードを追加してください — 課金は行われません。"}}, "transferProject": "コピーを共有", "transferSecurityNoteDescription": "受信者は、このプロジェクトのコピーおよびすべての関連リソースへの完全なアクセス権を得ます。", "transferProjectDescription": "このプロジェクトのコピーを転送したい相手のユーザー名またはメールアドレスを入力してください。", "transferProjectLabel": "ユーザー名またはメールアドレス", "transferProjectPlaceholder": "johnsmith または <EMAIL>", "transferButton": "転送", "transferSecurityNote": "セキュリティ注意：", "dontHavePermisionToTransfer": "このプロジェクトを転送する権限がありません", "transferProjectUserNotFound": "ユーザー {{ user }} が見つかりませんでした！", "transferErrorOwnAccount": "プロジェクトを自分のアカウントに移行することはできません。", "transferError": "プロジェクトの転送中にエラーが発生しました", "transferSuccess": "{{ user }} に正常に転送されました", "enterValidEmailUsername": "ユーザー名またはメールアドレスを入力してください", "enterMinValidEmailUsername": "有効なユーザー名（最低3文字）またはメールアドレスを入力してください", "youWillStillHaveAccess": "元のプロジェクトには引き続きアクセスできます", "newChangesWillNotAffect": "新しい変更は他のユーザーのプロジェクトに影響しません"}