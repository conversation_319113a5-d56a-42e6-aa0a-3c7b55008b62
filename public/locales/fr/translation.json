{"whatWouldYouLikeToBuild": "Transformez votre idée en site web ou application en quelques minutes", "whatWouldYouLikeToBuildSubtitle": "Si tu peux <1>l'imaginer</1>, tu peux <5>le coder</5>.", "fromIdeaToDeployment": "Commence gratuitement. Code n'importe quoi. Transforme tes compétences en opportunités à chaque prompt.", "codePlaceholder": "Si vous pouvez l'imaginer, BIELA peut le coder, que devons-nous faire aujourd'hui ?", "defaultPlaceholder": "Comment puis-je vous aider aujourd'hui ? Faisons quelque chose d'extraordinaire ensemble", "checkingFeatures": "Vérification des fonctionnalités", "checklists": "Listes de vérification", "runUnitTestsSuggestionTitle": "Suggestion", "runUnitTestsSuggestionMessage": "<PERSON><PERSON><PERSON><PERSON>-vous exécuter des tests unitaires pour votre projet ?", "runUnitTestsPrimaryButton": "Exécuter les tests unitaires", "runUnitTestsSecondaryButton": "Annuler", "createDatabaseTitle": "Création de base de données", "createDatabaseMessage": "<PERSON><PERSON><PERSON><PERSON>-vous créer une base de données pour votre projet ?", "createDatabasePrimaryButton": "<PERSON><PERSON>er la base de données", "createDatabaseSecondaryButton": "Annuler", "extendedThinking": "Réflexion approfondie", "extendedThinkingTooltip": "Permettre à l'IA de réfléchir plus en profondeur avant de répondre", "firstResponseOnly": "Première réponse uniquement", "always": "Toujours", "attachFile": "<PERSON><PERSON><PERSON> un fichier", "voiceInput": "Entrée vocale", "selectLanguage": "Sélectionner la langue pour l'entrée vocale", "languageSelectionDisabled": "Sélection de la langue désactivée pendant l'enregistrement", "notAvailableInFirefox": "Cette fonctionnalité n'est pas disponible sur Firefox, mais elle l'est sur Chrome et Safari", "enhancePrompt": "Améliorer l'invite", "cleanUpProject": "Nettoyer le projet", "showPrompt": "Afficher l'invite", "hidePrompt": "Masquer l'invite", "sendButton": "Envoyer", "abortButton": "Annuler", "inspirationTitle": "Besoin d'inspiration ? Essayez l'une de ces suggestions :", "cleanUpPrompt": "Nettoyez le projet en vous assurant qu'aucun fichier individuel ne dépasse 300 lignes de code. Refactorez les fichiers volumineux en composants modulaires plus petits tout en conservant l'intégralité des fonctionnalités. Identifiez et supprimez tous les fichiers, codes, composants et données redondants qui ne sont plus nécessaires. Veillez à ce que tous les composants restent correctement connectés et fonctionnels, afin d'éviter toute interruption du système existant. Maintenez l'intégrité du code en vérifiant qu'aucune modification n'introduit d'erreurs ou ne casse les fonctionnalités actuelles. L'objectif est d'optimiser le projet en termes d'efficacité, de maintenabilité et de clarté.", "checklistPrompt": "Examinez mon invite initiale, comprenez l'objectif point par point et créez pour moi une liste de vérification avec une coche verte pour tout ce qui a été réalisé et une coche rouge pour ce qui reste à faire.", "personalPortfolioIdea": "Créez un site web de portfolio personnel avec un thème sombre", "recipeFinderIdea": "Créez une application de recherche de recettes qui suggère des plats en fonction des ingrédients", "weatherDashboardIdea": "Concevez un tableau de bord météo avec des arrière-plans animés", "habitTrackerIdea": "Développez un outil de suivi des habitudes avec visualisation des progrès", "loading": "Chargement", "error": "<PERSON><PERSON><PERSON>", "succes": "Succès !", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "dashboard": "Tableau de bord", "getStartedTitle": "Commencer", "getStartedSub": "Découvrez comment fonctionne Biela.dev", "createProject": "Créer un nouveau projet", "createProjectSub": "Commencez à partir de zéro", "uploadProject": "Téléverser un projet", "uploadProjectSub": "Importer un projet existant", "importChat": "Importer le chat dans le projet", "importChatSub": "Importer un chat existant", "createFolder": "<PERSON><PERSON>er un nouveau dossier", "createFolderSub": "Organisez vos projets", "cancel": "Annuler", "changeFolder": "Changer de dossier", "save": "Enregistrer", "importing": "Importation en cours...", "importFolder": "Importer le dossier", "giveTitle": "Don<PERSON>z un titre", "projects": "Projets", "searchProjects": "Rechercher des projets...", "becomeAffiliate": "<PERSON><PERSON><PERSON> affili<PERSON>", "exclusiveGrowth": "Avantages exclusifs de croissance", "lifetimeEarnings": "Gains à vie", "highCommissions": "Hautes commissions", "earnCommission": "Gagnez 50 % de commission sur votre première vente", "joinAffiliateProgram": "Rejoignez notre programme d'affiliation", "folders": "Dossiers", "organizeProjects": "Organisez vos projets par catégorie", "createNewFolder": "<PERSON><PERSON>er un nouveau dossier", "enterFolderName": "Entrez le nom du dossier", "editProjectName": "Modifier le nom du projet", "editName": "Modifier le nom", "editFolder": "Modifier le dossier", "deleteFolder": "Supp<PERSON>er le dossier", "all": "Tous", "webProjects": "Projets web", "mobilepps": "Applications mobiles", "developmentComparison": "Comparaison de développement", "traditionalVsAI": "Traditionnel vs IA", "traditional": "Traditionnel", "standardApproach": "Approche standard", "developmentCost": "Coût de développement", "developmentTime": "Temps de développement", "costSavings": "Économies de coûts", "reducedCosts": "Coûts réduits", "timeSaved": "Temps économisé", "fasterDelivery": "Livraison plus rapide", "bielaDevAI": "Biela.dev IA", "nextGenDevelopment": "Développement de nouvelle génération", "developmentCosts": "Coûts de développement", "openInGitHub": "<PERSON><PERSON><PERSON><PERSON><PERSON> sur GitHub", "downloadProject": "Télécharger le projet", "duplicateProject": "<PERSON><PERSON><PERSON><PERSON> le projet", "openProject": "<PERSON><PERSON><PERSON><PERSON><PERSON> le projet", "deleteProject": "Supprimer le projet", "confirmDelete": "Supprimer ce dossier ?", "invoicePreview": "Votre facture", "settings": {"title": "Paramètres", "deployment": {"AdvancedSettings": {"advanced-settings": "Paramètres avancés", "configure-advanced-deployment-options": "Configurer les options de déploiement avancées", "server-configuration": "Configuration du serveur", "memory-limit": "Limite <PERSON> m<PERSON>", "region": "Région", "security-settings": "Paramètres de sécurité", "enable-ddos-protection": "Activer la protection DDoS", "protect-against-distributed": "Protéger contre les attaques par déni de service distribué", "ip-whitelisting": "Liste blanche d'IP", "restrict-acces-to": "Restreindre l'accès à des adresses IP spécifiques", "deployment-options": "Options de déploiement", "auto-deploy": "Déploiement automatique", "automatically-deploy-when": "Déployer automatiquement lors d'un push sur la branche principale", "preview-deployments": "Prévisualiser les déploiements", "create-preview-deployments": "Créer des déploiements de prévisualisation pour les pull requests"}, "BuildSettings": {"build-and-deployment-settings": "Paramètres de build et de déploiement", "build-command": "Commande de build", "override": "<PERSON><PERSON>lace<PERSON>", "output-directory": "Répertoire de sortie", "override2": "<PERSON><PERSON>lace<PERSON>"}, "DatabaseConfiguration": {"database-configuration": "Configuration de la base de données", "configure-your-db-connections": "Configurer vos connexions et paramètres de base de données", "database-type": "Type de base de données", "connection-string": "Chaîne de connexion", "your-db-credentials": "Vos identifiants de base de données sont cryptés et stockés en toute sécurité", "database-settings": "Paramètres de la base de données", "pool-size": "<PERSON>lle du pool", "require": "<PERSON><PERSON>", "prefer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disable": "Désactiver", "add-database": "Ajouter une base de données"}, "DomainSettings": {"domain-settings": "Paramètres de domaine", "configure-your-custom-domain": "Configurer vos domaines personnalisés et certificats SSL", "custom-domain": "Domaine <PERSON>", "add": "Ajouter", "ssl-certificate": "Certificat SSL", "auto-renew-ssl-certificates": "Renouveler automatiquement les certificats SSL", "auto-renew-before-expiry": "Renouveler automatiquement les certificats SSL avant leur expiration", "force-https": "Forcer le HTTPS", "redirect-all-http-traffic": "Rediriger tout le trafic HTTP vers HTTPS", "active-domain": "Domaines actifs", "remove": "<PERSON><PERSON><PERSON><PERSON>"}, "EnvironmentVariables": {"environment-variables": "Variables d'environnement", "configure-environment-variables": "Configurer les variables d'environnement pour vos déploiements", "all-enviroments": "Tous les environnements", "environment-variables2": "Variables d'environnement", "preview": "<PERSON><PERSON><PERSON><PERSON>", "development": "Développement", "create-new": "Créer nouveau", "key": "Clé", "value": "<PERSON><PERSON>", "save-variable": "Enregistrer la variable", "you-can-also-import": "Vous pouvez également importer des variables depuis un fichier .env :", "import-env-file": "Importer le fichier .env"}, "ProjectConfiguration": {"project-configuration": "Configuration du projet", "config-your-project-settings": "Configurer les paramètres de votre projet et les options de déploiement", "project-url": "URL du projet", "framework": "Framework", "repo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "branch": "Branche", "main": "main", "development": "développement", "staging": "staging"}}, "identity": {"unverified": {"title": "Vérification de l'identité", "description": "Vérifiez votre identité pour utiliser Biela.dev", "subtitle": "Processus de vérification sécurisé", "processServers": "Toutes les informations de la carte sont stockées en toute sécurité sur les serveurs de Stripe, et non sur les serveurs de Biela", "processCharge": "Votre carte ne sera pas débitée sans votre consentement explicite pour un abonnement", "processBenefits": "Biela.dev est complètement gratuit pour les comptes vérifiés jusqu'au 15 mai 2025", "verifyStripe": "Vérifier avec une carte de crédit ou de débit", "verifyStripeDescription": "Connectez votre méthode de paiement pour la vérification", "verifyNow": "Vérifier maintenant"}, "verified": {"title": "Identité vérifiée", "description": "Votre identité a été vérifiée avec succès", "paymentMethod": "Méthode de paiement", "cardEnding": "Carte se terminant par", "updatePayment": "Mettre à jour la méthode de paiement", "untilDate": "Jusqu'au 15 mai 2025", "freeAccess": "<PERSON><PERSON><PERSON> gratuit", "freeAccessDescription": "Profitez d'un accès complet à Biela.dev gratuitement", "secureStorage": "Stockage sécurisé", "secureStorageDescription": "Les informations de votre carte sont stockées en toute sécurité sur les serveurs de Stripe, et non sur ceux de Biela. Votre carte ne sera pas débitée sans votre consentement explicite pour un abonnement.", "subscriptionAvailable": "Les abonnements seront disponibles à partir du 15 mai 2025."}, "connectingToStripe": "Connexion à Stripe..."}, "tabs": {"billing": "Facturation", "profile": "Profil", "deployment": "Déploiement", "identity": "Identité"}}, "help": {"title": "Comment pouvons-nous vous aider ?", "searchPlaceholder": "Rechercher dans la documentation...", "categories": {"getting-started": {"title": "Premiers pas", "description": "Apprenez les bases de l'utilisation de Biela.dev", "articles": ["Guide de démarrage rapide", "Présentation de la plateforme", "Création de votre premier projet", "Comprendre le développement avec IA"]}, "ai-development": {"title": "Développement avec IA", "description": "Ma<PERSON><PERSON><PERSON>z le développement assisté par IA", "articles": ["Rédiger des instructions efficaces", "Bonnes pratiques pour la génération de code", "Conseils pour déboguer l'IA", "Fonctionnalités avancées de l'IA"]}, "project-management": {"title": "Gestion de projet", "description": "Organisez et gérez vos projets", "articles": ["Structure du projet", "Collaboration en équipe", "Contrôle de version", "Options de déploiement"]}}, "channels": {"docs": {"name": "Documentation", "description": "Guides complets et références API"}, "community": {"name": "Communauté", "description": "Connectez-vous avec d'autres développeurs"}, "github": {"name": "GitHub", "description": "Signalez des problèmes et contribuez"}}, "support": {"title": "Besoin d'aide supplémentaire ?", "description": "Notre équipe de support est disponible 24h/24 et 7j/7 pour vous aider avec toutes vos questions ou problèmes.", "button": "<PERSON>er le support"}}, "getStarted": {"title": "Découvrez comment fonctionne Biela.dev", "description": "Créez votre application en quelques minutes avec Biela.dev. Notre IA automatise l'ensemble du processus de développement, de la configuration au déploiement. Voici comment notre IA construit votre application sans effort !", "features": {"docs": {"title": "Documentation pour développeurs", "description": "Découvrez comment utiliser Biela.dev grâce à des guides faciles à suivre, des astuces et les meilleures pratiques. Parfait pour les débutants comme pour les développeurs expérimentés !", "cta": "Explorer la documentation pour développeurs"}, "support": {"title": "Retour d'information et support", "description": "Besoin d'aide ou envie de donner votre avis ? Contactez le support et aidez à améliorer Biela.dev !", "cta": "Envoyer un retour"}, "platform": {"title": "Fonctionnalités de la plateforme", "description": "Découvrez les outils puissants offerts par Biela.dev pour vous aider à créer des sites web et des applications sans effort. Laissez l'IA coder pour vous !", "cta": "Explorer les fonctionnalités"}}, "video": {"title": "Vidéo de démarrage rapide", "description": "Regardez comment Biela.dev construit pour vous : création d'applications et de sites web sans effort !", "cta": "Regarder le tutoriel"}, "guide": {"title": "Guide de démarrage rapide", "steps": {"setup": {"title": "Configurez instantanément votre projet", "description": "Préparez votre environnement de développement en quelques secondes"}, "generate": {"title": "Générez du code full-stack avec l'IA", "description": "Laissez l'IA écrire du code prêt pour la production pour vous"}, "features": {"title": "Génération instantanée de fonctionnalités", "description": "Ajoutez des fonctionnalités complexes avec des commandes simples"}, "editor": {"title": "Éditeur sans code et low-code", "description": "Modifiez votre application de manière visuelle ou via le code"}, "optimize": {"title": "Optimisez et testez en temps réel", "description": "Assurez-vous que votre application fonctionne parfaitement"}, "deploy": {"title": "Déployez en un clic", "description": "Mettez votre application en ligne instantanément grâce au déploiement automatisé"}}}, "faq": {"title": "Questions fréquentes", "questions": {"what": {"question": "Qu'est-ce que Biela.dev ?", "answer": "Biela.dev est une plateforme alimentée par l'IA qui vous aide à créer des sites web et des applications, même si vous ne savez pas coder. Elle automatise l'ensemble du processus de développement, de l'écriture du code au déploiement."}, "experience": {"question": "Ai-je besoin d'expérience en programmation pour utiliser Biela.dev ?", "answer": "Non ! Biela.dev est conçu pour les débutants comme pour les développeurs expérimentés. Vous pouvez construire avec l'aide de l'IA ou personnaliser le code généré selon vos besoins."}, "projects": {"question": "Quels types de projets puis-je créer ?", "answer": "Vous pouvez créer des sites web, des applications web, des applications mobiles, des plateformes SaaS, des boutiques en ligne, des tableaux de bord administratifs, et bien plus encore."}, "edit": {"question": "Puis-je modifier le code généré par Biela.dev ?", "answer": "Oui ! Biela.dev vous permet de personnaliser le code généré par l'IA ou d'utiliser l'éditeur sans code/low-code pour effectuer des modifications facilement."}, "deployment": {"question": "Comment fonctionne le déploiement ?", "answer": "En un seul clic, Biela.dev déploie votre projet, le mettant en ligne et prêt à être utilisé. Aucune configuration manuelle de serveur n'est requise !"}, "pricing": {"question": "Biela.dev est-il gratuit ?", "answer": "Biela.dev propose un plan gratuit avec des fonctionnalités de base. Pour bénéficier d'outils et de ressources plus avancé<PERSON>, vous pouvez passer à un plan premium."}, "integrations": {"question": "Pui<PERSON>-je intégrer des outils ou des bases de données tiers ?", "answer": "Oui ! Biela.dev prend en charge l'intégration avec des bases de données populaires (MongoDB, Firebase, Supabase) et des API tierces."}, "help": {"question": "Où puis-je obtenir de l'aide en cas de difficulté ?", "answer": "<PERSON><PERSON> pouvez consulter notre documentation pour développeurs, le guide de démarrage rapide, ou contacter le support via notre centre d'aide."}}}, "cta": {"title": "Prêt à commencer ?", "description": "Créez votre premier projet avec Biela.dev et découvrez le futur du développement.", "button": "Créer un nouveau projet"}}, "confirmDeleteProject": "Confirmer la suppression du projet", "confirmRemoveFromFolder": "Confirmer la suppression du dossier", "deleteProjectWarning": "Êtes-vous sûr de vouloir supprimer définitivement {{projectName}} ?", "removeFromFolderWarning": "Êtes-vous sûr de vouloir retirer {{projectName}} de {{folderName}} ?", "confirm": "Confirmer", "confirmDeleteFolder": "Confirmer la suppression du dossier", "deleteFolderWarning": "Êtes-vous sûr de vouloir supprimer {{folderName}} ? Cette action est irréversible.", "folderDeletedSuccessfully": "Dossier supprimé avec succès", "downloadChat": "Télécharger le chat", "inactiveTitle": "Cet onglet est inactif", "inactiveDescription": "Cliquez sur le bouton ci-dessous pour rendre cet onglet actif et continuer à utiliser l'application.", "inactiveButton": "Utiliser cet onglet", "suggestions": {"weatherDashboard": "<PERSON><PERSON>ez un tableau de bord météo", "ecommercePlatform": "Créez une plateforme de commerce électronique", "socialMediaApp": "Concevez une application de réseau social", "portfolioWebsite": "Générez un site web de portfolio", "taskManagementApp": "Créez une application de gestion des tâches", "fitnessTracker": "<PERSON><PERSON><PERSON> un tracker de fitness", "recipeSharingPlatform": "Concevez une plateforme de partage de recettes", "travelBookingSite": "Créez un site de réservation de voyages", "learningPlatform": "C<PERSON>ez une plateforme d'apprentissage", "musicStreamingApp": "Concevez une application de streaming musical", "realEstateListing": "<PERSON><PERSON>ez une annonce immobilière", "jobBoard": "C<PERSON>ez un tableau d'offres d'emploi"}, "pleaseWait": "Veuillez patienter...", "projectsInAll": "Projets dans Tous", "projectsInCurrentFolder": "Projets dans {{folderName}}", "createYourFirstProject": "Créez votre premier projet", "startCreateNewProjectDescription": "Commencez à construire quelque chose d'incroyable. Vos projets s'afficheront ici une fois créés.", "createProjectBtn": "Nouveau Projet", "publishedToContest": "Publié dans le concours", "publishToContest": "Publier dans le concours", "refreshSubmission": "Actualiser la soumission", "contestInformation": "Informations sur le concours", "selectFolder": "Choisir un dossier", "folder": "Dossier", "selectAFolder": "Sélectionnez un dossier", "thisProject": "ce projet", "projectDeletedSuccessfully": "Projet supprimé avec succès", "projectRemovedFromFolder": "Projet retiré du dossier", "unnamedProject": "Projet sans nom", "permanentDeletion": "Suppression permanente", "removeFromFolder": "<PERSON><PERSON><PERSON> de {{folderName}}", "verifiedAccount": "Compte vérifié", "hasSubmittedAMinimumOfOneProject": "A soumis au moins un projet", "haveAtLeastActiveReferrals": "Avoir au moins {{number}} parrainages actifs", "GoToAffiliateDashBoard": "Aller au tableau de bord des affiliés", "LikeOneProjectThatBelongsToAnotherUser": "A aimé un projet appartenant à un autre utilisateur", "GoToContestPage": "<PERSON>er à la page du concours", "ContestStatus": "Statut du concours", "ShowcaseYourBestWork": "Présentez votre meilleur travail", "submissions": "Soumissions", "qualifyConditions": "Conditions de qualification", "completed": "<PERSON><PERSON><PERSON><PERSON>", "collapseQualifyConditions": "Réduire les conditions de qualification", "expandQualifyConditions": "Développer les conditions de qualification", "basicParticipation": "Participation de base", "complete": "<PERSON><PERSON><PERSON><PERSON>", "incomplete": "Incomplet", "forTop3Places": "Pour les 3 premières places", "deleteSubmission": "Supprimer la soumission", "view": "Voir", "submitMoreProjectsToIncreaseYourChangesOfWining": "Soumettez plus de projets pour augmenter vos chances de gagner !", "removeFromContest": "Re<PERSON>rer du concours ?", "removeFromContestDescription": "Êtes-vous sûr de vou<PERSON> retirer <project>{{project}}</project> du concours ? Cette action ne peut pas être annulée.", "cardVerificationRequired": "Vérification de carte requise", "pleaseVerifyCard": "Veuillez vérifier votre carte pour continuer", "unlockFeaturesMessage": "Pour débloquer l'accès complet à toutes les fonctionnalités de la plateforme, nous vous demandons une vérification rapide de votre carte. Ce processus est entièrement sécurisé et garantit une expérience fluide sur Biela.dev. Aucuns frais ne seront prélevés sur votre carte pendant la vérification.", "freeVerificationNotice": "Avantages de la Vérification", "accessToAllFeatures": "Accès complet aux fonctionnalités de Biela.dev.", "enhancedFunctionality": "Performances améliorées de la plateforme.", "quickSecureVerification": "Processus de vérification sécurisé et crypté.", "noCharges": "*Aucun frais ni coût caché", "verificationOnly": " — vérification uniquement.", "verifyNow": "Vérifier maintenant", "DropFilesToUpload": "<PERSON>é<PERSON>z les fichiers à téléverser", "projectInfo": {"information": "Informations sur le projet", "type": "Type de projet", "complexity": "Complexité", "components": "Composants", "features": "Fonctionnalités", "confidenceScore": "Indice de confiance", "estimationUncertainty": "Incertitude de l'estimation", "keyTechnologies": "Technologies clés"}, "projectMetrics": {"teamComposition": "Composition de l'équipe", "hoursBreakdown": "Répartition des heures", "timeToMarket": "<PERSON><PERSON><PERSON> mise sur le marché", "maintenance": "Maintenance", "aiPowered": "Propulsé par l'IA", "developerLevel": "Niveau du développeur", "nextGenAI": "IA de nouvelle génération", "keyBenefits": "Avantages clés", "instantDevelopment": "Développement instantané", "noMaintenanceCosts": "Aucun coût de maintenance", "highConfidence": "Haute confiance", "productionReadyCode": "Code prêt pour la production", "immediate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uncertainty": "Incertitude", "minimal": "<PERSON><PERSON>"}, "loadingMessages": {"publish": ["Récupération des informations du projet...", "Création d'une capture d'écran...", "Génération du résumé et de la description..."], "refresh": ["Récupération des données de soumission actuelles...", "Mise à jour de la capture d'écran du projet...", "Actualisation des détails du projet..."]}, "errorMessages": {"publish": "Impossible de publier \"{{projectName}}\" dans le concours. Une erreur est survenue lors du traitement de votre demande.", "refresh": "Impossible d'actualiser la soumission \"{{projectName}}\". Le serveur n'a pas pu mettre à jour les informations de votre projet."}, "actions": {"refresh": {"title": "Actualisation de la soumission", "successMessage": "La soumission de votre projet a été mise à jour avec succès.", "buttonText": "Voir la soumission mise à jour"}, "publish": {"title": "Publication dans le concours", "successMessage": "Votre projet a été soumis au concours.", "buttonText": "Voir dans la page du concours"}}, "SupabaseConnect": "Supabase", "SupabaseDashboard": "Supabase", "RestoreApp": "<PERSON><PERSON><PERSON>", "SaveApp": "Enregistrer", "ForkChat": "<PERSON><PERSON><PERSON><PERSON>", "BielaTerminal": "Terminal Biela", "UnitTesting": "Tests unitaires", "InstallDependencies": "Installer les dépendances", "InstallDependenciesDescription": "Installe tous les paquets requis pour le projet en utilisant", "BuildProject": "Construire le projet", "BuildProjectDescription": "Compile et optimise le projet pour la production en utilisant", "StartDevelopment": "<PERSON><PERSON><PERSON><PERSON> le développement", "StartDevelopmentDescription": "<PERSON> le serveur de développement pour un aperçu en direct en utilisant", "NoProjectFound": "Aucun projet trouvé avec ce nom.", "NoProjectFoundDescription": " Veuillez vérifier les fautes de frappe et réessayer.", "ContextLimitReached": "Limite de contexte atteinte", "ClaudeContextDescription1": "Vous avez atteint la capacité de contexte pour ce projet avec Claude. Pas d'inquiétude — nous pouvons continuer avec un modèle Gemini disposant d'une fenêtre de contexte beaucoup plus large.", "ClaudeContextDescription2": "Les modèles Gemini offrent jusqu'à 5 fois plus de capacité de contexte, vous permettant de conserver tout votre code, historique de chat, et continuer le développement sans interruption.", "SelectModelToContinue": "Sélectionnez un modèle pour continuer :", "Performance": "Performance", "AlternativeLimitExplanation": "Il semble que l'IA ait atteint la limite de traitement pour ce projet. La majeure partie de l’espace est utilisée par les fichiers importés, pas par le chat lui-même.", "SuggestedSolutions": "Solutions suggérées :", "ReimportProject": "Réimporter comme nouveau projet", "ReimportProjectDescription": "<PERSON><PERSON> effacera l’historique du chat et libérera de l’espace de contexte, tout en conservant vos fichiers.", "BreakIntoProjects": "Diviser en plusieurs projets", "BreakIntoProjectsDescription": "Divisez votre travail en composants plus petits pouvant être développés séparément.", "ExportWork": "Exporter le travail terminé", "ExportWorkDescription": "Téléchargez et archivez les fichiers terminés pour libérer de l’espace de contexte.", "AlternativeContextNote": "Pour optimiser votre espace de contexte, pensez à supprimer les fichiers ou bibliothèques inutilisés et concentrez-vous sur les fichiers essentiels à cette phase de développement.", "ContinueWithSelectedModel": "Continuer avec le modèle sélectionné", "Close": "<PERSON><PERSON><PERSON>", "AIModel": "Modèle IA", "Active": "Actif", "Stats": "Statistiques", "Cost": "Coût", "ExtendedThinkingDisabledForModel": "Non disponible avec ce modèle", "ExtendedThinkingAlwaysOn": "Toujours actif avec ce modèle", "limitReached": "Votre limite a été atteinte !", "deleteProjectsSupabase": "Veuillez supprimer certains projets ou augmenter votre limite dans Supabase.", "goTo": "<PERSON><PERSON>", "clickProjectSettings": " cliquez sur le projet, Paramètres du projet, faites défiler vers le bas et cliquez", "delete": "<PERSON><PERSON><PERSON><PERSON>", "retrying": "Nouvelle tentative…", "retryConnection": "Réessayer la connexion", "RegisterPageTitle": "S'inscrire – biela.dev", "RegisterPageDescription": "<PERSON><PERSON><PERSON> votre compte sur biela.dev et débloquez toutes les fonctionnalités.", "SignUpHeading": "Inscription", "AlreadyLoggedInRedirectHome": "Vous êtes déjà connecté ! Redirection vers l'accueil...", "PasswordsMismatch": "Les mots de passe ne correspondent pas.", "FirstNameRequired": "Le prénom est requis", "LastNameRequired": "Le nom est requis", "UsernameRequired": "Le nom d'utilisateur est requis", "EmailRequired": "L'adresse e-mail est requise", "EmailInvalid": "Veuillez saisir une adresse e-mail valide", "TooManyRequests": "<PERSON><PERSON> de requ<PERSON>, ve<PERSON><PERSON><PERSON> réessayer plus tard", "SomethingWentWrongMessage": "Une erreur s'est produite, veuillez réessayer plus tard", "PasswordRequired": "Le mot de passe est requis", "ConfirmPasswordRequired": "Veuillez confirmer votre mot de passe", "AcceptTermsRequired": "<PERSON><PERSON> <PERSON> accepter les Conditions d'utilisation et la Politique de confidentialité", "CaptchaRequired": "Veuillez compléter le CAPTCHA", "RegistrationFailed": "L'inscription a échoué", "EmailConfirmationSent": "Un e-mail de confirmation a été envoyé ! Veuillez confirmer votre e-mail, puis vous connecter.", "RegistrationServerError": "Échec de l'inscription (le serveur a renvoyé false).", "SomethingWentWrong": "Une erreur s'est produite", "CheckEmailHeading": "Vérifiez votre e-mail pour confirmer votre inscription", "CheckEmailDescription": "Nous vous avons envoyé un e-mail contenant un lien de confirmation.", "GoToHomepage": "Aller à la page d'accueil", "ReferralCodeOptional": "Code de parrainage (optionnel)", "EnterReferralCode": "Entrez un code de parrainage si vous avez été invité par un utilisateur existant.", "PasswordPlaceholder": "Mot de passe", "ConfirmPasswordPlaceholder": "Confirmez le mot de passe", "CreateAccount": "<PERSON><PERSON><PERSON> un compte", "AlreadyHaveAccountPrompt": "Vous avez déjà un compte ?", "Login": "Connexion", "AcceptTermsPrefix": "J'accepte les", "TermsOfService": "Conditions d'utilisation", "AndSeparator": "et", "PrivacyPolicy": "Politique de confidentialité", "LoginPageTitle": "Connexion – biela.dev", "LoginPageDescription": "Accédez à votre compte ou connectez-vous à biela.dev pour profiter de toutes les fonctionnalités.", "LogInHeading": "Connexion", "EmailOrUsernamePlaceholder": "E-mail / Nom d'utilisateur", "ForgotPassword?": "Mot de passe oublié ?", "LoginToProfile": "Connexion à votre profil", "UserNotConfirmed": "Compte utilisateur non confirmé", "ConfirmEmailNotice": "<PERSON><PERSON> devez confirmer votre adresse e-mail pour activer votre compte.", "ResendConfirmationEmail": "Renvoyer l'e-mail de confirmation", "ResendConfirmationSuccess": "L'e-mail de vérification a été renvoyé ! Veuillez vérifier votre boîte de réception.", "ResendConfirmationError": "Échec de l'envoi de l'e-mail de confirmation.", "LoginSuccess": "Connexion réussie ! Redirection...", "LoginFailed": "Échec de la connexion", "LoginWithGoogle": "Connexion avec Google", "LoginWithGitHub": "Connexion avec GitHub", "SignUpWithGoogle": "S'inscrire avec Google", "SignUpWithGitHub": "S'inscrire avec GitHub", "Or": "OU", "NoAccountPrompt": "Vous n'avez pas de compte ?", "SignMeUp": "Inscrivez-moi", "ForgotPasswordPageTitle": "Mot de passe oublié – biela.dev", "ForgotPasswordPageDescription": "Réinitialisez le mot de passe de votre compte biela.dev et récupérez l'accès.", "BackToLogin": "Retour à la connexion", "ForgotPasswordHeading": "Mot de passe oublié", "ForgotPasswordDescription": "Saisissez votre adresse e-mail et nous vous enverrons un lien de vérification pour réinitialiser votre mot de passe.", "VerificationLinkSent": "Lien de vérification envoyé ! Veuillez vérifier votre e-mail.", "EnterYourEmailPlaceholder": "Entrez votre e-mail", "Sending": "Envoi en cours...", "SendVerificationCode": "Envoyer le code de vérification", "InvalidConfirmationLink": "Lien de confirmation invalide", "Back": "Retour", "ResetPassword": "Réinitialiser le mot de passe", "ResetPasswordDescription": "Créez un nouveau mot de passe pour votre compte", "NewPasswordPlaceholder": "Nouveau mot de passe", "ConfirmNewPasswordPlaceholder": "Confirmer le nouveau mot de passe", "ResetPasswordButton": "Réinitialiser le mot de passe", "PasswordRequirements": "Le mot de passe doit comporter au moins 8 caractères, inclure une majuscule, une minuscule, un chiffre et un caractère spécial.", "PasswordUpdatedSuccess": "Mot de passe mis à jour avec succès !", "affiliateDashboard": "Tableau de bord affilié", "userDashboard": "Tableau de bord utilisateur", "returnToAffiliateDashboard": "Retour au tableau de bord affilié", "returnToUserDashboard": "Retour au tableau de bord utilisateur", "myProfile": "Mon profil", "viewAndEditYourProfile": "Voir et éditer votre profil", "billing": "Facturation", "manageYourBillingInformation": "Gérer vos informations de facturation", "logout": "Se déconnecter", "logoutDescription": "Se déconnecter de votre compte", "SupabaseNotAvailable": "Supabase n'est pas disponible pour le moment, veuillez réessayer plus tard.", "projectActions": {"invalidSlug": "Slug de projet invalide.", "downloadSuccess": "Projet téléchargé avec succès !", "downloadError": "Échec du téléchargement du projet.", "exportSuccess": "Chat exporté ! Vérifiez votre dossier de téléchargements.", "exportError": "Échec de l'exportation du chat.", "duplicateSuccess": "Chat dupliqué avec succès !", "duplicateError": "Échec de la duplication du chat."}, "enter_new_phone_number": "Entrer un nouveau numéro", "enter_new_phone_number_below": "Veuillez entrer votre nouveau numéro ci-dessous :", "new_phone_placeholder": "Nouveau numéro de téléphone", "enter_otp_code": "Entrer le code OTP", "confirm_phone_message": "Pour utiliser votre compte, vous devez confirmer votre numéro. Entrez le code envoyé à ({{phone}}).", "wrong_phone": "Numéro incorrect ?", "resend_sms": "Ren<PERSON><PERSON> le <PERSON>", "submit": "So<PERSON><PERSON><PERSON>", "tokensAvailable": "jetons disponibles", "sectionTitle": "Gestion de Domaine", "addDomainButton": "A<PERSON>ter un Nom de Domaine", "connectCustomDomainTitle": "Connecter un Nom de Domaine Personnalisé", "disclaimer": "Avertissement :", "disclaimerText": "Pour vérifier correctement, vous devez configurer correctement toutes les règles DNS ci-dessus", "domainInputDescription": "Entrez le nom de domaine que vous souhaitez connecter à ce projet.", "domainLabel": "Nom de Domaine", "domainPlaceholder": "example.com", "cancelButton": "Annuler", "continueButton": "A<PERSON>ter un Nom de Domaine", "deployingText": "Déploiement...", "addingText": "Ajout...", "verifyButtonText": "Vérifier", "configureDnsTitle": "Configurer les Enregistrements DNS", "configureDnsDescription": "Ajoutez les enregistrements DNS suivants à votre domaine pour vérifier la propriété et le connecter à ce projet.", "tableHeaderType": "Type", "tableHeaderName": "Nom", "tableHeaderValue": "<PERSON><PERSON>", "note": "Remarque :", "noteText": "Les modifications DNS peuvent prendre jusqu'à 48 heures pour se propager. <PERSON><PERSON><PERSON><PERSON>, elles prennent souvent effet en quelques minutes à quelques heures.", "backButton": "Retour", "showDnsButton": "Afficher les Paramètres DNS", "hideDnsButton": "Masquer les Paramètres DNS", "removeButton": "<PERSON><PERSON><PERSON><PERSON>", "dnsSettingsTitle": "Paramètres DNS du Domaine", "removeDomainConfirmTitle": "Supp<PERSON>er le Nom de Domaine", "removeConfirmationText": "Êtes-vous sûr de vouloir supprimer le nom de domaine ", "importantCleanupTitle": "Nettoyage DNS Important", "cleanupDescription": "Après avoir supprimé ce nom de domaine de votre projet, n'oubliez pas de supprimer également les enregistrements DNS créés lors de la configuration. Cela permet de maintenir une configuration DNS propre et d'éviter les conflits futurs.", "confirmRemoveButton": "Supp<PERSON>er le Nom de Domaine", "customConfigTitle": "Configuration de Domaine Personnalisé", "customConfigDescription": "Connectez vos propres noms de domaine à ce projet. Votre projet restera toujours accessible via le domaine par défaut de Biela, mais les domaines personnalisés offrent une expérience de marque professionnelle à vos utilisateurs.", "defaultLabel": "<PERSON><PERSON>", "statusActive": "Actif", "statusPending": "En Attente", "lastVerifiedText": "Dernière vérification il y a un instant", "errorInvalidDomain": "Veuillez entrer un nom de domaine valide (par exemple, example.com)", "errorDuplicateDomain": "Ce nom de domaine est déjà connecté à votre projet", "errorAddFail": "Échec de l'ajout du nom de domaine.", "successAdd": "Nom de domaine ajouté avec succès ! Votre domaine a été ajouté à ce projet.", "benefitsTitle": "Avantages du Domaine", "benefitSecurityTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "benefitSecurityDesc": "Tous les domaines personnalisés sont automatiquement sécurisés avec des certificats SSL.", "benefitPerformanceTitle": "Performance Rapide", "benefitPerformanceDesc": "Le CDN mondial garantit un chargement rapide de votre projet pour les utilisateurs du monde entier.", "benefitBrandingTitle": "Image de Marque Professionnelle", "benefitBrandingDesc": "Utilisez votre propre domaine pour une expérience de marque cohérente.", "benefitAnalyticsTitle": "Intégration des Analyses", "benefitAnalyticsDesc": "Les domaines personnalisés fonctionnent parfaitement avec les plateformes d’analyse.", "meta": {"index": {"title": "biela.dev | Constructeur de Web & App avec IA – Construire avec des invites", "description": "Transformez vos idées en sites web ou applications en direct avec biela.dev. Utilisez des invites alimentées par l'IA pour créer des produits numériques personnalisés sans effort."}, "login": {"title": "Connexion à Votre Compte biela.dev", "description": "Accédez à votre tableau de bord biela.dev pour gérer et construire vos projets générés par l'IA."}, "register": {"title": "Inscrivez-vous sur biela.dev – Commencez à Construire avec l'IA", "description": "<PERSON><PERSON>ez votre compte biela.dev pour commencer à construire des sites web et des applications à l'aide d'invites alimentées par l'IA."}, "dashboard": {"title": "Votre Tableau de Bord de Projets – biela.dev", "description": "Gérez vos sites web et applications construits par l'IA, éditez vos projets en direct et suivez votre historique de construction – tout en un seul endroit."}, "profile": {"title": "Votre profil – biela.dev", "description": "Consultez et mettez à jour les détails de votre compte biela.dev, gérez vos préférences et personnalisez votre expérience de développement en IA."}, "billing": {"title": "Facturation – biela.dev", "description": "Ajoutez votre carte de crédit pour vérifier votre identité et débloquer l'accès complet aux fonctionnalités de biela.dev — aucun frais ne sera appliqué."}}, "transferProject": "Partager une copie", "transferSecurityNoteDescription": "Le destinataire recevra un accès complet à une copie de ce projet et à toutes ses ressources associées. Vous conserverez l'accès au projet original.", "transferProjectDescription": "Sai<PERSON><PERSON>z le nom d'utilisateur ou l'adresse e-mail de la personne à qui vous souhaitez transférer une copie de ce projet.", "transferProjectLabel": "Nom d'utilisateur ou e-mail", "transferProjectPlaceholder": "johns<PERSON> ou <EMAIL>", "transferButton": "<PERSON><PERSON><PERSON><PERSON>", "transferSecurityNote": "Note de sécurité :", "dontHavePermisionToTransfer": "Vous n'avez pas la permission de transférer ce projet", "transferProjectUserNotFound": "L'utilisateur {{ user }} n'a pas été trouvé !", "transferErrorOwnAccount": "Vous ne pouvez pas transférer un projet vers votre propre compte.", "transferError": "<PERSON><PERSON>ur lors du transfert du projet", "transferSuccess": "Transféré avec succès à {{ user }}", "enterValidEmailUsername": "Veuillez entrer un nom d'utilisateur ou un e-mail", "enterMinValidEmailUsername": "Veuillez entrer un nom d'utilisateur valide (au moins 3 caractères) ou une adresse e-mail", "youWillStillHaveAccess": "Vous aurez toujours accès au projet original", "newChangesWillNotAffect": "Les nouveaux changements n'affecteront pas le projet de l'autre utilisateur"}