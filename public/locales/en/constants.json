{"Claude37Description": "Most powerful AI for premium websites", "Claude37FeatureFirst": "High-quality code", "Claude37FeatureSecond": "Advanced functionality", "Claude37FeatureThird": "Superior design", "Claude37Performance": "Excellent", "Claude37Cost": "Premium", "Claude37ContextWindow": "Standard context window (200K tokens)", "GeminiProDescription": "Versatile AI with strong capabilities and wide context", "GeminiProFeatureFirst": "Reliable code output", "GeminiProFeatureSecond": "Handles complex inputs", "GeminiProFeatureThird": "Good design quality", "GeminiProPerformance": "Very good", "GeminiProCost": "Mid-premium", "GeminiProContextWindow": "Large context window (1M+ tokens)", "GeminiFlashDescription": "Faster, cost-effective option", "GeminiFlashFeatureFirst": "Quick generation", "GeminiFlashFeatureSecond": "Basic functionality", "GeminiFlashFeatureThird": "Simple designs", "GeminiFlashPerformance": "Good", "GeminiFlashCost": "Budget-friendly", "GeminiFlashContextWindow": "Large context window (1M+ tokens)"}