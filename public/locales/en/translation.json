{"whatWouldYouLikeToBuild": "Turn Your Idea into a Live Website or App in Minutes", "whatWouldYouLikeToBuildSubtitle": "If you can <1>imagine</1> it, you can <5>code</5> it.", "fromIdeaToDeployment": "Start for free. Code anything. Turn your skills into opportunity with every prompt.", "codePlaceholder": "If you can imagine it, BIELA can code it, what shall we do today?", "defaultPlaceholder": "How can I help you today? Let’s do something amazing together", "checkingFeatures": "Checking the features", "checklists": "Checklists", "runUnitTestsSuggestionTitle": "Suggestion", "runUnitTestsSuggestionMessage": "Would you like to Run Unit Tests for your project?", "runUnitTestsPrimaryButton": "Run Unit Tests", "runUnitTestsSecondaryButton": "<PERSON><PERSON><PERSON>", "createDatabaseTitle": "Database Creation", "createDatabaseMessage": "Would you like to create a database for your project?", "createDatabasePrimaryButton": "Create Database", "createDatabaseSecondaryButton": "<PERSON><PERSON><PERSON>", "extendedThinking": "Extended thinking", "extendedThinkingTooltip": "Enable AI to think more deeply before responding", "firstResponseOnly": "First response only", "always": "Always", "attachFile": "Attach file", "voiceInput": "Voice input", "selectLanguage": "Select language for voice input", "languageSelectionDisabled": "Language selection disabled during recording", "notAvailableInFirefox": "This feature is not available in Firefox, but it is available in Chrome and Safari", "enhancePrompt": "Enhance prompt", "cleanUpProject": "Clean up project", "showPrompt": "Show Prompt", "hidePrompt": "Hide Prompt", "sendButton": "Send", "abortButton": "Cancel", "inspirationTitle": "Need inspiration? Try one of these:", "cleanUpPrompt": "Clean up the project by ensuring no single file exceeds 300 lines of code. Refactor large files into smaller, modular components while maintaining full functionality. Identify and remove all unused files, code, components, and any redundant data that are no longer needed. Ensure that all components remain properly connected and functional, avoiding any disruptions to the existing system. Maintain code integrity by verifying that no changes introduce errors or break current features. The goal is to optimize the project for efficiency, maintainability, and clarity.", "checklistPrompt": "Look at my initial prompt, understand the goal point by point and build for me a checklist with a green check for everything what has been done and with a red check what has left to be done.", "personalPortfolioIdea": "Create a personal portfolio website with a dark theme", "recipeFinderIdea": "Build a recipe finder app that suggests meals based on ingredients", "weatherDashboardIdea": "Design a weather dashboard with animated backgrounds", "habitTrackerIdea": "Develop a habit tracker with progress visualization", "loading": "Loading", "error": "Error", "succes": "Succes!", "tryAgain": "Try Again", "dashboard": "Dashboard", "getStartedTitle": "Get Started", "getStartedSub": "Explore how Biela.dev works", "createProject": "Create New Project", "createProjectSub": "Start from scratch", "uploadProject": "Upload Project", "uploadProjectSub": "Import existing project", "importChat": "Import Chat", "importChatSub": "Import existing Chat", "createFolder": "Create a new folder", "createFolderSub": "Organize your projects", "editProjectName": "Edit project name", "editName": "Edit Name", "cancel": "Cancel", "changeFolder": "Change Folder", "save": "Save", "importing": "Importing...", "importFolder": "Import Folder", "giveTitle": "Give a title", "projects": "Projects", "searchProjects": "Search projects...", "becomeAffiliate": "Affiliate Dashboard", "exclusiveGrowth": "Exclusive Growth Perks", "lifetimeEarnings": "Lifetime Earnings", "highCommissions": "High Commissions", "earnCommission": "Earn 50% commission on your first sale", "joinAffiliateProgram": "Join Our Affiliate Program", "folders": "Folders", "organizeProjects": "Organize your projects by category", "createNewFolder": "Create a new folder", "enterFolderName": "Enter folder name", "editFolder": "Edit <PERSON>", "deleteFolder": "Delete Folder", "all": "All", "webProjects": "Web Projects", "mobilepps": "Mobile Apps", "developmentComparison": "Development Comparison", "traditionalVsAI": "Traditional vs AI", "traditional": "Traditional", "standardApproach": "Standard Approach", "developmentCost": "Development Cost", "developmentTime": "Development Time", "costSavings": "Cost Savings", "reducedCosts": "Reduced Costs", "timeSaved": "Time Saved", "fasterDelivery": "Faster Delivery", "bielaDevAI": "Biela.dev AI", "nextGenDevelopment": "Next-Gen Development", "developmentCosts": "Development Costs", "openInGitHub": "Open in GitHub", "downloadProject": "Download Project", "downloadChat": "Download Chat", "duplicateProject": "Duplicate Project", "openProject": "Open Project", "deleteProject": "Delete Project", "confirmDelete": "Delete this folder?", "invoicePreview": "Your invoice", "settings": {"title": "Settings", "deployment": {"AdvancedSettings": {"advanced-settings": "Advanced Settings", "configure-advanced-deployment-options": "Configure advanced deployment options", "server-configuration": "Server Configuration", "memory-limit": "Memory Limit", "region": "Region", "security-settings": "Security Settings", "enable-ddos-protection": "Enable DDoS Protection", "protect-against-distributed": "Protect against distributed denial-of-service attacks", "ip-whitelisting": "IP Whitelisting", "restrict-acces-to": "Restrict access to specific IP addresses", "deployment-options": "Deployment Options", "auto-deploy": "Auto Deploy", "automatically-deploy-when": "Automatically deploy when pushing to main branch", "preview-deployments": "Preview Deployments", "create-preview-deployments": "Create preview deployments for pull requests"}, "BuildSettings": {"build-and-deployment-settings": "Build & Deployment Settings", "build-command": "Build Command", "override": " Override", "output-directory": "Output Directory", "override2": "Override", "install-command": "Install Command", "development-command": "Development Command", "learn-more-title": "Learn more about", "learn-more-description": "Build and Development Settings"}, "DatabaseConfiguration": {"database-configuration": "Database Configuration", "configure-your-db-connections": "Configure your database connections and settings", "database-type": "Database Type", "connection-string": "Connection String", "your-db-credentials": " Your database credentials are encrypted and stored securely", "database-settings": "Database Settings", "pool-size": "Pool Size", "require": "Require", "prefer": "Prefer", "disable": "Disable", "add-database": "Add Database"}, "DomainSettings": {"domain-settings": "Domain Settings", "configure-your-custom-domain": "Domains are linked to your deployments. You can assign them to specific environments or set up a redirect to another domain. Additioanly, you can associate a domain with a particular Git branch by selecting the Preview environment.", "custom-domain": "Custom Domain", "add": "Add", "ssl-certificate": "SSL Certificate", "auto-renew-ssl-certificates": "Auto-renew SSL certificates", "auto-renew-before-expiry": "Automatically renew SSL certificates before expiry", "force-https": "Force HTTPS", "redirect-all-http-traffic": "Redirect all HTTP traffic to HTTPS", "active-domain": "Active Domains", "remove": "Remove"}, "EnvironmentVariables": {"environment-variables": "Environment Variables", "configure-environment-variables": "Configure environment variables for your deployments", "all-enviroments": "All Environments", "environment-variables2": "Environment Variables", "preview": "Preview", "development": "Development", "create-new": "Create New", "key": "Key", "value": "Value", "save-variable": "Save Variable", "you-can-also-import": "You can also import variables from a .env file:", "import-env-file": "Import .env File"}, "ProjectConfiguration": {"project-configuration": "Project Configuration", "config-your-project-settings": "Configure your project settings and deployment options", "project-url": "Project URL", "framework": "Framework", "repo": "Repository", "branch": "Branch", "main": "main", "development": "development", "staging": "staging"}}, "identity": {"unverified": {"title": "Identity Verification", "description": "Verify your identity to use Biela.dev", "subtitle": "Secure Verification Process", "processServers": "All card information is stored securely on Stripe's servers, not on Biela's servers", "processCharge": "Your card will not be charged without your explicit consent for a subscription", "processBenefits": "Biela.dev is completely free for verified accounts until May 15, 2025", "verifyStripe": "Verify with Credit or Debit Card", "verifyStripeDescription": "Connect your payment method for verification", "verifyNow": "Verify Now"}, "verified": {"title": "Identity Verified", "description": "Your identity has been successfully verified", "paymentMethod": "Payment Method", "cardEnding": "Card ending in", "updatePayment": "Update Payment Method", "untilDate": "Until May 15, 2025", "freeAccess": "Free Access", "freeAccessDescription": "Enjoy full access to Biela.dev at no cost", "secureStorage": "Secure Storage", "secureStorageDescription": "Your card information is stored securely on <PERSON><PERSON>'s servers, not on Biela's servers. Your card will not be charged without your explicit consent for a subscription.", "subscriptionAvailable": "Subscriptions will be available starting May 15, 2025."}, "connectingToStripe": "Connecting to Stripe..."}, "tabs": {"billing": "Billing", "profile": "Profile", "deployment": "Deployment", "identity": "Identity"}}, "help": {"title": "How can we help you?", "searchPlaceholder": "Search documentation...", "categories": {"getting-started": {"title": "Getting Started", "description": "Learn the basics of using Biela.dev", "articles": ["Quick Start Guide", "Platform Overview", "Creating Your First Project", "Understanding AI Development"]}, "ai-development": {"title": "AI Development", "description": "Master AI-powered development", "articles": ["Writing Effective Prompts", "Code Generation Best Practices", "AI Debugging Tips", "Advanced AI Features"]}, "project-management": {"title": "Project Management", "description": "Organize and manage your projects", "articles": ["Project Structure", "Team Collaboration", "Version Control", "Deployment Options"]}}, "channels": {"docs": {"name": "Documentation", "description": "Comprehensive guides and API references"}, "community": {"name": "Community", "description": "Connect with other developers"}, "github": {"name": "GitHub", "description": "Report issues and contribute"}}, "support": {"title": "Still Need Help?", "description": "Our support team is available 24/7 to help you with any questions or issues you may have.", "button": "Contact Support"}}, "getStarted": {"title": "Explore How Biela.dev Works", "description": "Build Your App in Minutes with Biela.dev. Our AI automates the entire development process, from setup to deployment. Here's how our AI builds your app effortlessly!", "features": {"docs": {"title": "Developer Documentation", "description": "Learn how to use Biela.dev with easy-to-follow guides, tips, and best practices. Perfect for beginners and experienced developers alike!", "cta": "Explore Developer Docs"}, "support": {"title": "Feedback and Support", "description": "Need help or have feedback? Contact support and help improve Biela.dev!", "cta": "Send Feedback"}, "platform": {"title": "Platform Features", "description": "Discover the powerful tools Biela.dev offers to help you create websites and apps effortlessly. Let AI do the coding for you!", "cta": "Explore Features"}}, "video": {"title": "Quick Start Video", "description": "Watch how Biela.dev builds for you—effortless app & web creation!", "cta": "Watch Tutorial"}, "guide": {"title": "Quick Start Guide", "steps": {"setup": {"title": "Instantly Set Up Your Project", "description": "Get your development environment ready in seconds"}, "generate": {"title": "Generate Full-Stack Code with AI", "description": "Let AI write production-ready code for you"}, "features": {"title": "Instant Feature Generation", "description": "Add complex features with simple prompts"}, "editor": {"title": "No-Code & Low-Code Editor", "description": "Modify your app visually or through code"}, "optimize": {"title": "Optimize & Test in Real-Time", "description": "Ensure your app performs perfectly"}, "deploy": {"title": "Deploy with One Click", "description": "Go live instantly with automated deployment"}}}, "faq": {"title": "Frequently Asked Questions", "questions": {"what": {"question": "What is Biela.dev?", "answer": "Biela.dev is an AI-powered platform that helps you create websites and apps—even if you don't know how to code. It automates the entire development process, from writing code to deployment."}, "experience": {"question": "Do I need coding experience to use Biela.dev?", "answer": "No! Biela.dev is designed for both beginners and experienced developers. You can build with AI assistance or customize the generated code as needed."}, "projects": {"question": "What types of projects can I create?", "answer": "You can build websites, web apps, mobile apps, SaaS platforms, e-commerce stores, admin dashboards, and more."}, "edit": {"question": "Can I edit the code generated by Biela.dev?", "answer": "Yes! Biela.dev allows you to customize the AI-generated code or use the no-code/low-code editor for easy modifications."}, "deployment": {"question": "How does deployment work?", "answer": "With one click, Biela.dev deploys your project, making it live and ready to use. No need for manual server setup!"}, "pricing": {"question": "Is Biela.dev free to use?", "answer": "Biela.dev offers a free plan with basic features. For more advanced tools and resources, you can upgrade to a premium plan."}, "integrations": {"question": "Can I integrate third-party tools or databases?", "answer": "Yes! Biela.dev supports integrations with popular databases (MongoDB, Firebase, Supabase) and third-party APIs."}, "help": {"question": "Where can I get help if I'm stuck?", "answer": "You can check out our Developer Documentation, Quick Start Guide, or reach out to <PERSON> through our help center."}}}, "cta": {"title": "Ready to Get Started?", "description": "Create your first project with Biela.dev and experience the future of development.", "button": "Create New Project"}}, "confirmDeleteProject": "Confirm Delete Project", "confirmRemoveFromFolder": "Confirm Remove from Folder", "deleteProjectWarning": "Are you sure you want to permanently delete {{projectName}}?", "removeFromFolderWarning": "Are you sure you want to remove {{projectName}} from {{folderName}}?", "confirm": "Confirm", "confirmDeleteFolder": "Confirm Delete Folder", "deleteFolderWarning": "Are you sure you want to delete {{folderName}}? This action cannot be undone.", "folderDeletedSuccessfully": "Folder deleted successfully", "inactiveTitle": "This tab is inactive", "inactiveDescription": "Click the button below to make this the active tab and continue using the app.", "inactiveButton": "Use this tab", "suggestions": {"weatherDashboard": "Create a weather dashboard", "ecommercePlatform": "Build an e-commerce platform", "socialMediaApp": "Design a social media app", "portfolioWebsite": "Generate a portfolio website", "taskManagementApp": "Create a task management app", "fitnessTracker": "Build a fitness tracker", "recipeSharingPlatform": "Design a recipe sharing platform", "travelBookingSite": "Create a travel booking site", "learningPlatform": "Build a learning platform", "musicStreamingApp": "Design a music streaming app", "realEstateListing": "Create a real estate listing", "jobBoard": "Build a job board"}, "pleaseWait": "Please wait...", "projectsInAll": "Projects in All", "projectsInCurrentFolder": "Projects in {{folderName}}", "createYourFirstProject": "Create Your First Project", "startCreateNewProjectDescription": "Start building something amazing. Your projects will be displayed here once you create them.", "createProjectBtn": "New Project", "publishedToContest": "Published to contest", "publishToContest": "Publish to contest", "refreshSubmission": "Refresh submission", "contestInformation": "Contest Information", "selectFolder": "Choose a Folder", "folder": "Folder", "selectAFolder": "Select a folder", "thisProject": "this project", "projectDeletedSuccessfully": "Project deleted successfully", "projectRemovedFromFolder": "Project removed from folder", "unnamedProject": "Unnamed Project", "permanentDeletion": "Permanent deletion", "removeFromFolder": "Remove from {{folderName}}", "verifiedAccount": "Verified account", "hasSubmittedAMinimumOfOneProject": "Has submitted a minimum of one project", "haveAtLeastActiveReferrals": "Have at least {{number}} active referrals", "GoToAffiliateDashBoard": "Go to affiliate dashboard", "LikeOneProjectThatBelongsToAnotherUser": "Liked one project that belongs to another user", "GoToContestPage": "Go to Contest Page", "ContestStatus": "Contest Status", "ShowcaseYourBestWork": "Showcase your best work", "submissions": "submissions", "qualifyConditions": "Qualifying Conditions", "completed": "completed", "collapseQualifyConditions": "Collapse qualifying conditions", "expandQualifyConditions": "Expand qualifying conditions", "basicParticipation": "Basic Participation", "complete": "Complete", "incomplete": "Incomplete", "forTop3Places": "For Top 3 Places", "deleteSubmission": "Delete submission", "view": "View", "submitMoreProjectsToIncreaseYourChangesOfWining": "Submit more projects to increase your chances of winning!", "removeFromContest": "Remove from Contest?", "removeFromContestDescription": "Are you sure you want to remove <project>{{project}}</project> from the contest? This action cannot be undone.", "cardVerificationRequired": "Card Verification Required", "pleaseVerifyCard": "Please verify your card to continue", "unlockFeaturesMessage": "To unlock full access to all platform features, we require a quick card verification. This process is completely secure and ensures a smooth experience on Biela.dev. No charges will be made to your card during verification.", "freeVerificationNotice": "Verification Benefits", "accessToAllFeatures": "Full access to Biela.dev features.", "enhancedFunctionality": "Enhanced platform performance.", "quickSecureVerification": "Secure and encrypted verification process.", "noCharges": "*No charges or hidden fees.", "verificationOnly": " - verification only.", "verifyNow": "Verify Now", "DropFilesToUpload": "Drop files to upload", "projectInfo": {"information": "Project Information", "type": "Project Type", "complexity": "Complexity", "components": "Components", "features": "Features", "confidenceScore": "Confidence Score", "estimationUncertainty": "Estimation Uncertainty", "keyTechnologies": "Key Technologies"}, "projectMetrics": {"teamComposition": "Team Composition", "hoursBreakdown": "Hours Breakdown", "timeToMarket": "Time to Market", "maintenance": "Maintenance", "aiPowered": "AI-Powered", "developerLevel": "Developer Level", "nextGenAI": "Next-Generation AI", "keyBenefits": "Key Benefits", "instantDevelopment": "Instant Development", "noMaintenanceCosts": "No Maintenance Costs", "highConfidence": "High Confidence", "productionReadyCode": "Production-Ready Code", "immediate": "Immediate", "uncertainty": "Uncertainty", "minimal": "Minimal"}, "loadingMessages": {"publish": ["Getting project information...", "Making a screenshot...", "Generating summary and description..."], "refresh": ["Retrieving current submission data...", "Updating project screenshot...", "Refreshing project details..."]}, "errorMessages": {"publish": "Unable to publish \"{{projectName}}\" to the contest. There was an issue processing your request.", "refresh": "Unable to refresh \"{{projectName}}\" submission. The server couldn't update your project information."}, "actions": {"refresh": {"title": "Refreshing Submission", "successMessage": "Your project submission has been successfully updated.", "buttonText": "View Updated Submission"}, "publish": {"title": "Publishing to Contest", "successMessage": "Your project has been submitted to the contest.", "buttonText": "View in Contest Page"}}, "SupabaseConnect": "Supabase Connect", "SupabaseDashboard": "Supabase Dashboard", "RestoreApp": "Rest<PERSON>", "SaveApp": "Save", "ForkChat": "Fork", "BielaTerminal": "Biela Terminal", "UnitTesting": "Unit Testing", "InstallDependencies": "Install Dependencies", "InstallDependenciesDescription": "Installs all required packages for the project using", "BuildProject": "Build Project", "BuildProjectDescription": "Compiles and optimizes the project for production using", "StartDevelopment": "Start Development", "StartDevelopmentDescription": " Launches the development server for live preview using", "NoProjectFound": "No project found with that name.", "NoProjectFoundDescription": " Please check for any typos and try again.", "ContextLimitReached": "Context Limit Reached", "ClaudeContextDescription1": "You've reached the context capacity for this project with <PERSON>. Don't worry - we can continue by switching to a Gemini model with a significantly larger context window.", "ClaudeContextDescription2": "Gemini models offer up to 5x more context capacity, allowing you to keep all your code, chat history, and continue building your project without interruption.", "SelectModelToContinue": "Select a model to continue:", "Performance": "Performance", "AlternativeLimitExplanation": "It looks like the AI has hit the processing limit for this project. Most of the space is being used by the imported files, not the chat itself.", "SuggestedSolutions": "Suggested solutions:", "ReimportProject": "Reimport as a new project", "ReimportProjectDescription": "This will clear the chat history and free up some context space, while preserving your files.", "BreakIntoProjects": "Break into multiple projects", "BreakIntoProjectsDescription": "Split your work into smaller components that can be developed separately.", "ExportWork": "Export completed work", "ExportWorkDescription": "Download and archive finished files to free up context space.", "AlternativeContextNote": "To get the most out of your available context, consider removing any unused files or libraries and focusing on the core files needed for the current development phase.", "ContinueWithSelectedModel": "Continue with selected model", "Close": "Close", "AIModel": "AI Model", "Active": "Active", "Stats": "Stats", "Cost": "Cost", "ExtendedThinkingDisabledForModel": "Not available with this model", "ExtendedThinkingAlwaysOn": "Always on with this model", "limitReached": "Your limit has been reached!", "deleteProjectsSupabase": "Please delete some projects or increase your limit in Supabase.", "goTo": "Go to", "clickProjectSettings": " click on the project, Project Settings, scroll to the bottom, and click", "delete": "Delete", "retrying": "Retrying…", "retryConnection": "Retry Connection", "RegisterPageTitle": "Register – biela.dev", "RegisterPageDescription": "Create your account on biela.dev and unlock all features.", "SignUpHeading": "Sign Up", "AlreadyLoggedInRedirectHome": "You're already logged in! Redirecting to home...", "PasswordsMismatch": "Passwords do not match.", "FirstNameRequired": "First name is required", "LastNameRequired": "Last name is required", "UsernameRequired": "Username is required", "EmailRequired": "Email is required", "EmailInvalid": "Please enter a valid email address", "TooManyRequests": "Too many requests, please try again later", "SomethingWentWrongMessage": "Something went wrong, please try again later", "PasswordRequired": "Password is required", "ConfirmPasswordRequired": "Please confirm your password", "AcceptTermsRequired": "You must accept the Terms of Service and Privacy Policy", "CaptchaRequired": "Please complete the CAPTCHA", "RegistrationFailed": "Registration failed", "EmailConfirmationSent": "Email confirmation was been sent! Please confirm your email and then log in.", "RegistrationServerError": "Registration failed (server returned false).", "SomethingWentWrong": "Something went wrong", "CheckEmailHeading": "Check your email to confirm your registration", "CheckEmailDescription": "We've sent you an email with a confirmation link.", "GoToHomepage": "Go to homepage", "ReferralCodeOptional": "Referral Code (Optional)", "EnterReferralCode": "Enter a referral code if you were invited by an existing user.", "PasswordPlaceholder": "Password", "ConfirmPasswordPlaceholder": "Confirm Password", "CreateAccount": "Create Account", "AlreadyHaveAccountPrompt": "Already have an account?", "Login": "<PERSON><PERSON>", "AcceptTermsPrefix": "I agree to the", "TermsOfService": "Terms of Service", "AndSeparator": "&", "PrivacyPolicy": "Privacy Policy", "LoginPageTitle": "Login – biela.dev", "LoginPageDescription": "Access your account or sign in to biela.dev to start using all features.", "LogInHeading": "Log In", "EmailOrUsernamePlaceholder": "Email/Username", "ForgotPassword?": "Forgot Password?", "LoginToProfile": "Log In To Your Profile", "UserNotConfirmed": "User account not confirmed", "ConfirmEmailNotice": "You need to confirm your email address to activate your account.", "ResendConfirmationEmail": "Resend Confirmation Email", "ResendConfirmationSuccess": "Verification email has been resent! Please check your inbox.", "ResendConfirmationError": "Failed to resend confirmation email.", "LoginSuccess": "Login successful! Redirecting...", "LoginFailed": "<PERSON><PERSON> failed", "LoginWithGoogle": "Login With Google", "LoginWithPaypal": "Login With Paypal", "LoginWithGitHub": "Login With GitHub", "SignUpWithGoogle": "Sign Up With Google", "SignUpWithGitHub": "Sign Up With GitHub", "Or": "OR", "NoAccountPrompt": "Don't have an account?", "SignMeUp": "Sign Me Up", "ForgotPasswordPageTitle": "Forgot Password – biela.dev", "ForgotPasswordPageDescription": "Reset your biela.dev account password and regain access.", "BackToLogin": "Back to Login", "ForgotPasswordHeading": "Forgot Password", "ForgotPasswordDescription": "Enter your email address and we'll send you a verification link to reset your password.", "VerificationLinkSent": "Verification link sent! Please check your email.", "EnterYourEmailPlaceholder": "Enter your email", "Sending": "Sending...", "SendVerificationCode": "Send Verification Code", "InvalidConfirmationLink": "Invalid confirmation link", "Back": "Back", "ResetPassword": "Reset Password", "ResetPasswordDescription": "Create a new password for your account", "NewPasswordPlaceholder": "New password", "ConfirmNewPasswordPlaceholder": "Confirm new password", "ResetPasswordButton": "Reset Password", "PasswordRequirements": "Password must be at least 8 characters long and include an uppercase letter, a lowercase letter, a number, and a special character.", "PasswordUpdatedSuccess": "Password updated successfully!", "affiliateDashboard": "Affiliate Dashboard", "userDashboard": "User Dashboard", "returnToAffiliateDashboard": "Return to affiliate dashboard", "returnToUserDashboard": "Return to user dashboard", "myProfile": "My Profile", "viewAndEditYourProfile": "View and edit your profile", "billing": "Billing", "manageYourBillingInformation": "Manage your billing information", "logout": "Logout", "logoutDescription": "Logout from your account", "SupabaseNotAvailable": "Supabase is not available right now, please try again later.", "projectActions": {"invalidSlug": "Invalid project slug.", "downloadSuccess": "Project downloaded successfully!", "downloadError": "Failed to download the project.", "exportSuccess": "Chat exported! Check your downloads folder.", "exportError": "Failed to export chat.", "duplicateSuccess": "Chat duplicated successfully!", "duplicateError": "Failed to duplicate chat."}, "enter_new_phone_number": "Enter New Phone Number", "enter_new_phone_number_below": "Enter your new phone number below:", "new_phone_placeholder": "New phone number", "enter_otp_code": "Enter OTP Code", "confirm_phone_message": "In order to be able to use your account, you need to confirm your phone number. Please enter the OTP code sent to your phone ({{phone}}).", "wrong_phone": "Wrong phone number?", "resend_sms": "Resend SMS", "submit": "Submit", "tokensAvailable": "tokens available", "sectionTitle": "Domain Management", "addDomainButton": "Add Domain Name", "connectCustomDomainTitle": "Connect a Custom Domain Name", "disclaimer": "Disclaimer:", "disclaimerText": "In order to succesfully verify you have to correctly set all the DNS rules above", "domainInputDescription": "Enter the domain name you want to connect to this project.", "domainLabel": "Domain Name", "domainPlaceholder": "example.com", "cancelButton": "Cancel", "continueButton": "Add Domain Name", "deployingText": "Deploying...", "addingText": "Adding...", "verifyButtonText": "Verify", "configureDnsTitle": "Configure DNS Records", "configureDnsDescription": "Add the following DNS records to your domain to verify ownership and connect it to this project.", "tableHeaderType": "Type", "tableHeaderName": "Name", "tableHeaderValue": "Value", "note": "Note:", "noteText": "DNS changes may take up to 48 hours to propagate. However, they often take effect within a few minutes to a few hours.", "backButton": "Back", "showDnsButton": "Show DNS Settings", "hideDnsButton": "Hide DNS Settings", "removeButton": "Remove", "dnsSettingsTitle": "Domain DNS Settings", "removeDomainConfirmTitle": "Remove Domain Name", "removeConfirmationText": "Are you sure you want to remove the domain name ", "importantCleanupTitle": "Important DNS Cleanup", "cleanupDescription": "After removing this domain name from your project, remember to also remove the DNS records you created during setup. This helps maintain a clean DNS configuration and prevents potential conflicts in the future.", "confirmRemoveButton": "Remove Domain Name", "customConfigTitle": "Custom Domain Configuration", "customConfigDescription": "Connect your own domain names to this project. Your project will always remain accessible via the default Biela domain, but custom domains provide a professional branded experience for your users.", "defaultLabel": "<PERSON><PERSON><PERSON>", "statusActive": "Active", "statusPending": "Pending", "lastVerifiedText": "Last verified just now", "errorInvalidDomain": "Please enter a valid domain name (e.g., example.com)", "errorDuplicateDomain": "This domain name is already connected to your project", "errorAddFail": "Failed to add domain name.", "successAdd": "Domain name added successfully! Your domain has been added to this project.", "benefitsTitle": "Domain Benefits", "benefitSecurityTitle": "Enhanced Security", "benefitSecurityDesc": "All custom domains are automatically secured with SSL certificates.", "benefitPerformanceTitle": "Fast Performance", "benefitPerformanceDesc": "Global CDN ensures your project loads quickly for users worldwide.", "benefitBrandingTitle": "Professional Branding", "benefitBrandingDesc": "Use your own domain for a consistent brand experience.", "benefitAnalyticsTitle": "Analytics Integration", "benefitAnalyticsDesc": "Custom domains work seamlessly with analytics platforms.", "meta": {"index": {"title": "biela.dev | AI-Powered Web & App Builder – Build with Prompts", "description": "Transform your ideas into live websites or apps with biela.dev. Use AI-driven prompts to build custom digital products effortlessly."}, "login": {"title": "Login to Your biela.dev Account2", "description": "Access your biela.dev dashboard to manage and build your AI-generated projects."}, "register": {"title": "Sign Up for biela.dev – Start Building with AI", "description": "Create your biela.dev account to start building websites and apps using AI-powered prompts."}, "dashboard": {"title": "Your Projects Dashboard – biela.dev", "description": "Manage your AI-built websites and apps, edit live projects, and track your build history—all in one place."}, "profile": {"title": "Your Profile – biela.dev", "description": "View and update your biela.dev account details, manage preferences, and personalize your AI development experience."}, "billing": {"title": "Billing – biela.dev", "description": "Add your credit card to verify your identity and unlock full access to biela.dev features—no charges will be made."}}, "transferProject": "Share a Copy", "transferSecurityNoteDescription": "The recipient will receive full access to a copy of this project and all its associated resources. ", "transferProjectDescription": "Enter the username or email of the person you want to transfer a copy of this project to.", "transferProjectLabel": "Username or Email", "transferProjectPlaceholder": "<NAME_EMAIL>", "transferButton": "Transfer", "transferSecurityNote": "Security note: ", "dontHavePermisionToTransfer": "You do not have permission to transfer this project", "transferProjectUserNotFound": "User {{ user }} was not found!", "transferErrorOwnAccount": "You cannot transfer a project to your own account.", "transferError": "Error transferring project", "transferSuccess": "Transferred successfully to {{ user }}", "enterValidEmailUsername": "Please enter a username or email", "enterMinValidEmailUsername": "Please enter a valid username (min 3 characters) or email address", "youWillStillHaveAccess": "You will still have access to the original project", "newChangesWillNotAffect": "New changes will not affect the other user's project"}