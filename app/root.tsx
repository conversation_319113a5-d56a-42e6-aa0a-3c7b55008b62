import type { LinksFunction } from '@remix-run/cloudflare';
import {
  isRouteErrorResponse,
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLocation,
  useRouteError,
} from '@remix-run/react';
import { UserProvider, useUser } from './ai/lib/context/userContext';
import { I18nextProvider, useTranslation } from 'react-i18next';
import i18n from '../i18n';
import tailwindReset from '@unocss/reset/tailwind-compat.css?url';
import { stripIndents } from './utils/stripIndent';
import { createHead } from 'remix-island';
import React, { Suspense, useCallback, useEffect, useState } from 'react';
import './components/styles/font.scss';
import reactToastifyStyles from 'react-toastify/dist/ReactToastify.css?url';
import globalStyles from './components/styles/index.scss?url';
import xtermStyles from '@xterm/xterm/css/xterm.css?url';
import { ToastContainer } from 'react-toastify';

import 'virtual:uno.css';
import SingleTabEnforcer from './ai/components/SingleTabEnforcer';
import { ChatOpenProvider } from './ai/lib/context/chatOpenContext';
import { backendApiFetch } from './ai/lib/backend-api';
import AccessDeinedPage from '~/routes/Access-Deined-Page';
import LoadFailurePage from '~/routes/Load-Failure-Page';
import PageNotFound from '~/routes/Not-Found-Page';
import { FaSpinner } from 'react-icons/fa';

interface User {
  isPhoneVerified: boolean;
  phoneNumber: string;
  googleId?: string;
  githubId?: string;
  email?: string;
}

export const links: LinksFunction = () => [
  { rel: 'icon', href: '/favicon.ico', type: 'image/x-icon' },
  {
    rel: 'icon',
    href: '/biela_favicon_light.svg',
    type: 'image/svg+xml',
    media: '(prefers-color-scheme: light)',
  },
  {
    rel: 'icon',
    href: '/biela_favicon_dark.svg',
    type: 'image/svg+xml',
    media: '(prefers-color-scheme: dark)',
  },
  { rel: 'stylesheet', href: reactToastifyStyles },
  { rel: 'stylesheet', href: tailwindReset },
  { rel: 'stylesheet', href: globalStyles },
  { rel: 'stylesheet', href: xtermStyles },
  {
    rel: 'preload',
    href: globalStyles, // Update this with the most critical CSS file
    as: 'style',
    onLoad: "this.onload=null;this.rel='stylesheet';",
  },
  {
    rel: 'preconnect',
    href: 'https://fonts.googleapis.com',
  },
  {
    rel: 'preconnect',
    href: 'https://fonts.gstatic.com',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'stylesheet',
    href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
  },
];

const inlineThemeCode = stripIndents`
  setTutorialKitTheme();

  function setTutorialKitTheme() {
    let theme = localStorage.getItem('biela_theme') ?? localStorage.getItem('data-theme');

    if (!theme) {
       theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'dark';
    }

    document.querySelector('html')?.setAttribute('data-theme', theme);
  }
`;

const gtmId = import.meta.env.VITE_GOOGLE_TAG_MANAGER_ID || 'GTM-5BXQMVKQ';

const gtmScript = stripIndents`
    (function(w,d,s,l,i){
      w[l]=w[l]||[];
      w[l].push({'gtm.start': new Date().getTime(), event:'gtm.js'});
      var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),
          dl=l!=='dataLayer'?'&l='+l:'';
      var gtmId = "${gtmId}";
      j.async=true;
      j.src='https://www.googletagmanager.com/gtm.js?id=' + gtmId + dl;
      f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer');
`;

export const Head = createHead(() => {
  return (
    <>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <Meta />
      <Links />
      <script dangerouslySetInnerHTML={{ __html: inlineThemeCode }} />
      <script dangerouslySetInnerHTML={{ __html: gtmScript }} />
      <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
    </>
  );
});

function AppContent({ setUserEmail }: { setUserEmail: (email?: string) => void }) {
  const { isLoggedIn, authenticateToken, getNewUserDataIfExpired } = useUser();
  // const [showPhoneInput, setShowPhoneInput] = useState(false);
  // const [phoneNumber, setPhoneNumber] = useState('');
  const location = useLocation();
  // const [showOtpDialog, setShowOtpDialog] = useState(false);
  // const [otpCode, setOtpCode] = useState('');
  const [user, setUser] = useState<User | null>(null);
  const { t } = useTranslation();

  const fetchProfileData = useCallback(async () => {
    try {
      const response = await backendApiFetch('/user/profile', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user profile');
      }

      const data: User = await response.json();
      setUser(data);
      setUserEmail(data?.email);
      if (!data?.isPhoneVerified && !data?.githubId && !data?.googleId) {
        if (!data?.phoneNumber) {
          // setShowPhoneInput(true);
        }
        // setPhoneNumber(data.phoneNumber || '');
        // setShowOtpDialog(true);
      }
    } catch (err) {
      console.error('Error fetching profile data:', err);
    }
  }, []);

  useEffect(() => {
    //@ts-ignore This function is loaded in the head script, therefore is not visible for Typescript, but it is called once the page loads.
    setTutorialKitTheme();
  }, []);

  useEffect(() => {
    const root = document.documentElement;
    const fs = parseFloat(getComputedStyle(root).fontSize);

    let headerHeight = '90px'; // default (medium ≤16px)
    if (fs > 16 && fs < 24)
      headerHeight = '100px'; // large
    else if (fs >= 24) headerHeight = '120px'; // x‑large+

    root.style.setProperty('--header-height', headerHeight);
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(location.search);
      const referral = params.get('referral');

      if (referral) {
        localStorage.setItem('referral', referral);
      }
    }
  }, [location.search]);

  useEffect(() => {
    if (isLoggedIn()) {
      fetchProfileData();
    }
  }, [isLoggedIn, fetchProfileData]);

  // const handleOTPSubmit = async (): Promise<boolean> => {
  //   if (!otpCode) {
  //     showToast.error('Please enter the OTP code', {
  //       autoClose: 5000,
  //       position: 'top-right',
  //       closeOnClick: false,
  //       pauseOnHover: false,
  //       draggable: false,
  //     });
  //     return false;
  //   }

  //   try {
  //     const response = await backendApiFetch(`/user-verification/${otpCode}`, {
  //       method: 'POST',
  //       headers: { 'Content-Type': 'application/json' },
  //     });

  //     const data = await response.json();

  //     if (response.ok) {
  //       showToast.success('OTP Verified Successfully!', {
  //         autoClose: 5000,
  //         position: 'top-right',
  //         closeOnClick: false,
  //         pauseOnHover: false,
  //         draggable: false,
  //       });
  //       return true;
  //     } else {
  //       showToast.error(data.message || 'Invalid OTP, please try again.', {
  //         autoClose: 5000,
  //         position: 'top-right',
  //         closeOnClick: false,
  //         pauseOnHover: false,
  //         draggable: false,
  //       });
  //       return false;
  //     }
  //   } catch (error) {
  //     console.error('Error verifying OTP:', error);
  //     showToast.error('Something went wrong. Please try again later.', {
  //       autoClose: 5000,
  //       position: 'top-right',
  //       closeOnClick: false,
  //       pauseOnHover: false,
  //       draggable: false,
  //     });
  //     return false;
  //   }
  // };

  // const handleOtpSubmit = async (e: React.FormEvent) => {
  //   e.preventDefault();
  //   const isSuccess = await handleOTPSubmit();
  //   if (isSuccess) setShowOtpDialog(false);
  // };

  // const verifyPhone = async (phone: string): Promise<boolean> => {
  //   try {
  //     const response = await backendApiFetch('/user-verification/phone', {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({ phoneNumber: phone }),
  //     });

  //     if (!response.ok) {
  //       throw new Error('Phone verification failed');
  //     }

  //     showToast.success('Phone verification sent successfully.', { autoClose: 5000, position: 'top-right' });

  //     return true;
  //   } catch (error) {
  //     console.error('Error occurred:', error);
  //     showToast.error('Failed to send phone verification', { autoClose: 5000, position: 'top-right' });

  //     return false;
  //   }
  // };

  // const updatePhoneAndResendOTP = async (newPhone: string) => {
  //   try {
  //     const response = await backendApiFetch('/user/profile', {
  //       method: 'PATCH',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({ phoneNumber: newPhone }),
  //     });

  //     if (!response.ok) {
  //       throw new Error('Failed to update phone number');
  //     }

  //     showToast.success('Phone number updated successfully!', { autoClose: 5000, position: 'top-right' });
  //     await verifyPhone(newPhone);
  //   } catch (error) {
  //     console.error('Error updating phone number:', error);
  //     showToast.error('Could not update phone number. Try again.', { autoClose: 5000, position: 'top-right' });
  //   }
  // };

  return (
    <>
      <Outlet />
      <ScrollRestoration />
      <Scripts />
      <ToastContainer />

      {/* OTP Dialog */}
      {/* {showOtpDialog && (
        <Dialog hasRoot={true}>
          <DialogTitle> {showPhoneInput ? t('enter_new_phone_number') : t('enter_otp_code')}</DialogTitle>

          <div className="p-6">
            {showPhoneInput ? (
              <>
                <p className="text-gray-400 mb-2"> {t('enter_new_phone_number_below')}</p>

                <PhoneInputField
                  label={''}
                  value={phoneNumber || ''}
                  onChange={(val) => setPhoneNumber(val)}
                  className="w-full"
                  placeholder={false}
                />
                <div className="flex justify-end gap-3 mt-6">
                  <DialogButton type="secondary" onClick={() => setShowPhoneInput(false)}>
                    {t('cancel')}
                  </DialogButton>
                  <DialogButton
                    type="primary"
                    onClick={() => {
                      updatePhoneAndResendOTP(phoneNumber);
                      setShowPhoneInput(false);
                    }}
                  >
                    {t('submit')}
                  </DialogButton>
                </div>
              </>
            ) : (
              <>
                <p className="text-gray-400"> {t('confirm_phone_message', { phone: phoneNumber })} </p>
                <input
                  type="text"
                  value={otpCode}
                  onChange={(e) => setOtpCode(e.target.value)}
                  className="w-full mt-4 px-4 py-3 border border-gray-700 rounded-lg bg-gray-900 text-white"
                />
                <div className="flex justify-between items-center gap-3 mt-6 flex-wrap">
                  <p
                    onClick={() => setShowPhoneInput(true)}
                    className="text-sm text-red-400 underline hover:text-red-300 transition"
                  >
                    {t('wrong_phone')}
                  </p>

                  <DialogButton type="secondary" onClick={() => verifyPhone(phoneNumber)}>
                    {t('resend_sms')}
                  </DialogButton>
                  <DialogButton type="primary" onClick={handleOtpSubmit}>
                    {t('submit')}
                  </DialogButton>
                </div>
              </>
            )}
          </div>
        </Dialog>
      )} */}
    </>
  );
}

export function HydrateFallback() {
  return <div className="hidden" />;
}

export default function App() {
  const [userEmail, setUserEmail] = useState<string>();

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Head />
      </head>
      <body>
        <ChatOpenProvider>
          <UserProvider>
            <I18nextProvider i18n={i18n}>
              <Suspense fallback={<HydrateFallback />}>
                <AppContent setUserEmail={setUserEmail} />
              </Suspense>
            </I18nextProvider>
          </UserProvider>
          <SingleTabEnforcer userEmail={userEmail} />
        </ChatOpenProvider>
        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}

export function ErrorBoundary() {
  const error = useRouteError();
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (isRouteErrorResponse(error)) {
    return (
      <html lang="en">
        <head>
          <meta charSet="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <Meta />
          <Links />
        </head>
        <body>
          {isRouteErrorResponse(error) ? (
            <>
              {error.status === 404 && <PageNotFound />}
              {error.status === 403 && <AccessDeinedPage />}
              {error.status === 500 && <LoadFailurePage />}
              {![404, 403, 500].includes(error.status) && <LoadFailurePage />}
            </>
          ) : (
            <LoadFailurePage />
          )}
          <ScrollRestoration />
          <Scripts />
          <LiveReload />
        </body>
      </html>
    );
  }
}
