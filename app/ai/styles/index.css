@import "tailwindcss";

@theme {
  /* Colors */
  --color-navy-50: #1A1D2E;
  --color-navy-100: #161926;
  --color-navy-200: #13151F;
  --color-navy-300: #0F1119;
  --color-navy-400: #0C0D14;
  --color-navy-500: #0A0B14;
  --color-navy-600: #080912;
  --color-navy-700: #06070F;
  --color-navy-800: #04050A;
  --color-navy-900: #020305;
  --color-accent: #22C55E;
  --color-accent-purple: #7C3AED;
  --color-accent-blue: #3B82F6;
  --color-accent-chatOptions: #4ADE801A;
  --color-inactive: #E5E7EB;

  /* Opacity */
  --opacity-3: 0.03;

  /* Fonts */
  --font-sans: 'Inter', sans-serif;
  --font-manrope: 'Manrope', sans-serif;

  /* Animations */
  --animation-float: floating 6s ease-in-out infinite;
  --animation-pulse-slow: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animation-gradient: gradient 8s linear infinite;
  --animation-glow: glow 2s ease-in-out infinite;
}
@layer base {
  html {
    scroll-behavior: smooth;
  }
  /* todo bg-navy didn`t exist before, make sure to use correct color; */
  body {
    @apply bg-navy-50 text-white antialiased overflow-x-hidden;
    background: linear-gradient(to bottom right, #0B0E14, #13151F);
    font-family: 'Manrope', sans-serif;
  }
}


.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.blur-margins:after {
  content: "";
  width: 50px;
  height: 42px;
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(to right, rgba(10, 23, 48, 0) 0%, rgba(10, 23, 48, 1) 100%);
  z-index: 60;
}
.blur-margins:before {
  content: "";
  width: 50px;
  height: 42px;
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(to right, rgba(10, 23, 48, 1) 0%, rgba(10, 23, 48, 0) 100%);
  z-index: 60;
}


@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 60s linear infinite; /* Changed from 30s to 60s for slower scrolling */
}

.animate-scroll:hover {
  animation-play-state: paused;
}
