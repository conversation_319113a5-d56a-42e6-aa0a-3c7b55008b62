import React, { useEffect, useState } from 'react';
import { ClientOnly } from 'remix-utils/client-only';
import { useNavigate } from '@remix-run/react';
import { TokensSession, TokensStore } from '~/ai/lib/stores/tokens/tokensStore';
import { FaRobot } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const TokenRemaining = ({ isStreaming, isHeader }: { isStreaming: boolean; isHeader?: boolean }) => {
  const [remainingTokens, setRemainingTokens] = useState<number | null>(null);
  const [freePromptsRemaining, setFreePromptsRemaining] = useState<number | null>(null);
  const navigate = useNavigate();
  const { t } = useTranslation('translation');

  const tokensStoreCallback = (data: TokensSession | null) => {
    if (data) {
      setRemainingTokens(data.remainingTokens);
      setFreePromptsRemaining(data.freeTokensRemaining);
    }
  };

  const tokensStore = TokensStore.getInstance(tokensStoreCallback);

  async function refreshTokens() {
    const data = await tokensStore.refreshTokensData();

    tokensStoreCallback(data || null);
  }

  useEffect(() => {
    const wasInit = remainingTokens !== null && freePromptsRemaining !== null;

    if (!isStreaming || !wasInit) {
      refreshTokens();
    }
  }, [isStreaming]);

  const displayTokens =
    remainingTokens === null || remainingTokens === undefined || remainingTokens <= 0 ? 0 : remainingTokens;

  if (remainingTokens === null || freePromptsRemaining === null) {
    return null;
  }

  return (
    <ClientOnly>
      {() => (
        <div className="">
          {isHeader ? (
            <div className="hidden md:flex items-center gap-2 bg-[#11182780] px-4 py-2 rounded-full">
              <FaRobot className="text-green-400 text-[16px]" />
              <span className={'text-white font-manrope font-extralight flex gap-1'}>
                {new Intl.NumberFormat('en', { notation: 'compact', compactDisplay: 'short' }).format(displayTokens)}

                <span className={'sm:block hidden'}>{t('tokensAvailable', 'tokens available')}</span>
              </span>
            </div>
          ) : freePromptsRemaining > 0 ? (
            <div style={{ color: '#FFD700' }}>
              {freePromptsRemaining} free prompt{freePromptsRemaining > 1 ? 's' : ''} remaining
            </div>
          ) : displayTokens > 0 ? (
            <>
              <div style={{ display: 'flex', gap: '8px' }}>
                <img src="/icons/coin.svg" alt="Coins" />
                Tokens remaining
              </div>
              <span style={{ color: '#4ADE80' }}>{new Intl.NumberFormat('de-DE').format(displayTokens)}</span>
            </>
          ) : (
            <div
              style={{ color: '#FF6347', cursor: 'pointer', textDecoration: 'underline' }}
              onClick={() => navigate('/register')}
            >
              Sign Up & Get More Tokens
            </div>
          )}
        </div>
      )}
    </ClientOnly>
  );
};

export default TokenRemaining;
