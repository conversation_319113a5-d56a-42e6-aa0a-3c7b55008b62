// BaseChat.tsx
/*
 * @ts-nocheck
 * Preventing TS checks with files presented in the video for a better presentation.
 */
import type { JSONValue, Message } from 'ai';
import React, { lazy, type RefCallback, Suspense, useEffect, useRef, useState } from 'react';
import ClientOnly from '~/components/common/ClientOnly';
import { classNames } from '~/utils/classNames';
import { Messages } from './Messages.client';
import { SendButton } from './SendButton.client';
import * as Tooltip from '@radix-ui/react-tooltip';
import styles from '../styles/BaseChat.module.scss';
import FilePreview from './FilePreview';
import { ScreenshotStateManager } from './ScreenshotStateManager';
import type { ActionAlert } from '~/types/actions';
import '../../components/styles/ainmated-spinner.scss';
import { ChatCodeSwitch } from '~/ai/components/ChatCodeSwitch';
import WithTooltip from './Tooltip';

import { createScopedLogger } from '~/utils/logger';
import ChatSuggestion from '~/ai/components/ChatSuggestion';
import CheckList from '~/assets/icons/checkList';
import { chatStore, setCheckingList, setCleaningProject, setShowPrompt } from '~/ai/lib/stores/chat';
import { useStore } from '@nanostores/react';
import { useParams } from 'react-router-dom';
import { backendApiFetch } from '~/ai/lib/backend-api';
import RoadmapContent from '~/ai/components/RoadmapContent';
import { motion } from 'framer-motion';
// Import the new TabsSection component (adjust the path as needed)
import TabsSection from '~/components/common/TabsSection';
import ActionButtons from '~/components/common/ActionButtons';
import AIModelSelector from '~/components/common/AIModelSelector';
import { HistorySwitch } from '~/ai/components/HistorySwitch';
import { RoadmapSwitch } from '~/ai/components/RoadmapSwitch';
import HistoryContent from '~/ai/components/HistoryContent';
import { ChevronDownIcon } from 'lucide-react';
import { useUser } from '~/ai/lib/context/userContext';
import { Trans, useTranslation } from 'react-i18next';
import CardVerificationModal from '~/backOffice/components/settings/IdentitySettings/CardVerificationModal';
import { useChatOpen } from '../lib/context/chatOpenContext';
import ProjectIdeasCarousel from './ProjectIdeasCarousel';
import CardSuccessModal from '~/backOffice/components/settings/IdentitySettings/CardSuccessModal';
import { StatusPopup } from '~/routes/profile';
import JoinUsLiveYoutube from '~/backOffice/components/settings/IdentitySettings/JoinUsLiveYoutube';
import { TokensSession, TokensStore } from '~/ai/lib/stores/tokens/tokensStore';
import { workbenchStore } from '../lib/stores/workbench';

const Workbench = lazy(()=> import('~/ai/components/workbench/Workbench.client'))
const logger = createScopedLogger('BaseChat');

interface BaseChatProps {
  textareaRef?: React.RefObject<HTMLTextAreaElement> | undefined;
  messageRef?: RefCallback<HTMLDivElement> | undefined;
  scrollRef?: RefCallback<HTMLDivElement> | undefined;
  showChat?: boolean;
  chatStarted?: boolean;
  isStreaming?: boolean;
  messages?: Message[];
  description?: string;
  enhancingPrompt?: boolean;
  promptEnhanced?: boolean;
  input?: string;
  handleStop?: () => void;
  sendMessage?: (event: React.UIEvent, messageInput?: string, messageOptions?: JSONValue) => void;
  handleInputChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  enhancePrompt?: () => void;
  importChat?: (description: string, messages: Message[]) => Promise<void>;
  exportChat?: () => void;
  uploadedFiles?: File[];
  setUploadedFiles?: (files: File[]) => void;
  imageDataList?: string[];
  setImageDataList?: (dataList: string[]) => void;
  actionAlert?: ActionAlert;
  clearAlert?: () => void;
}

export const BaseChat = React.forwardRef<HTMLDivElement, BaseChatProps>(
  (
    {
      textareaRef,
      messageRef,
      scrollRef,
      showChat = true,
      chatStarted = false,
      isStreaming = false,
      input = '',
      enhancingPrompt,
      handleInputChange,
      enhancePrompt,
      sendMessage,
      handleStop,
      uploadedFiles = [],
      setUploadedFiles,
      imageDataList = [],
      setImageDataList,
      messages,
      actionAlert,
      clearAlert,
    },
    ref,
  ) => {
    const { t } = useTranslation('translation');

    const [isListening, setIsListening] = useState(false);
    const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
    const [transcript, setTranscript] = useState('');
    const { showPrompt, checkingList } = useStore(chatStore);
    const { chatId } = useParams();
    const [rotation, setRotation] = useState(0);
    const [isStatusPopupOpen, setIsStatusPopupOpen] = useState(false);

    useEffect(() => {
      if (actionAlert?.type === 'SERVICE_UNAVAILABLE') {
        setIsStatusPopupOpen(true);
      } else {
        setIsStatusPopupOpen(false);
      }
    }, [actionAlert, isStreaming]);

    useEffect(() => {
      setInputValue(input);
    }, [input]);
    const { setIsChatOpen } = useChatOpen();
    // Add state for the active tab (defaulting to 'chat')
    const [activeTab, setActiveTab] = useState('chat');
    const [inputValue, setInputValue] = useState('');
    const [isDragActive, setIsDragActive] = useState(false);
    const { getUser } = useUser();
    const user = getUser();

    const handleIdeaClick = (idea: string) => {
      setInputValue(idea);
      setHasContent(idea.trim().length > 0);
    };

    const [hasContent, setHasContent] = useState(false);

    useEffect(() => {
      setHasContent(inputValue.trim().length > 0);
    }, [inputValue]);

    const fetchUserData = async (userId: any) => {
      try {
        const response = await backendApiFetch(`/user/${userId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        if (response.ok) {
          return await response.json();
        }
        return null;
      } catch (error) {
        console.error('Error fetching user data:', error);
        return null;
      }
    };

    useEffect(() => {
      let active = true;

      // Pornim animația de la starea curentă a rotației
      let angle = rotation;

      function updateRotation() {
        if (!active) {
          return;
        }

        // Incrementăm unghiul și îl normalizăm la [0, 360)
        angle = (angle + 1) % 360;
        setRotation(angle);

        /*
         * Dacă streaming-ul este activ, continuăm animația.
         * Dacă nu, mai continuăm doar până când unghiul revine la 0.
         */
        if (isStreaming || angle !== 0) {
          setTimeout(() => {
            requestAnimationFrame(() => {
              if (active) {
                updateRotation();
              }
            });
          }, 5000 / 360);
        } else {
          // Când animația s-a terminat (angle a revenit la 0), oprim animația.
          setRotation(0);
        }
      }

      /*
       * Pornim animația dacă:
       * - streaming-ul e activ, sau
       * - streaming-ul a devenit false, dar animația nu s-a terminat încă (rotation !== 0)
       */
      if (isStreaming || rotation !== 0) {
        updateRotation();
      }

      // Cleanup: oprim orice actualizare ulterioară când componenta se demontează sau când 'isStreaming' se schimbă

      return () => {
        active = false;
      };
    }, [isStreaming]);

    useEffect(() => {
      if (chatId) {
        localStorage.setItem('chatId', chatId);
      } else {
        setIsChatOpen(false);
      }
    }, [chatId]);

    const abort = async () => {
      if (isStreaming) {
        try {
          await backendApiFetch('/ai/abort', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
          });

          handleStop?.();
        } catch (error) {
          logger.error('Error during abort:', error);
          handleStop?.();
        }
      }
    };

    /**
     * Keep an eye on transcript changes for debugging
     */
    useEffect(() => {
      logger.debug('Transcript from speech:', transcript);
    }, [transcript]);

    useEffect(() => {
      localStorage.removeItem('projectUrl');
      localStorage.removeItem('annonKey');

      // Setup speech recognition
      if (typeof window !== 'undefined' && ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window)) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;

        recognition.onresult = (event) => {
          const transcript = Array.from(event.results)
            .map((result) => result[0])
            .map((result) => result.transcript)
            .join('');

          setTranscript(transcript);
          setInputValue(transcript);

          if (handleInputChange) {
            const syntheticEvent = {
              target: { value: transcript },
            } as React.ChangeEvent<HTMLTextAreaElement>;
            handleInputChange(syntheticEvent);
          }
        };

        recognition.onerror = (event) => {
          console.error('Speech recognition error:', event.error);
          setIsListening(false);
        };

        setRecognition(recognition);
      }

      // Clear the input
      if (handleInputChange) {
        const syntheticEvent = {
          target: { value: '' },
        } as React.ChangeEvent<HTMLTextAreaElement>;
        handleInputChange(syntheticEvent);
      }
    }, []);

    const startListening = (language: string) => {
      if (recognition) {
        recognition.lang = language;
        recognition.start();
        setIsListening(true);
      }
    };

    const stopListening = () => {
      if (recognition) {
        recognition.stop();
        setIsListening(false);
      }
    };

    function getFirstUserMessageOnHomePage(): string | null {
      if (typeof window === "undefined") return null;

      if (window.location.pathname !== "/") return null;

      const match = document.cookie.match(
        /(?:^|; )first-user-message=([^;]*)/,
      );

      return match ? decodeURIComponent(match[1]) : null;
    }

    useEffect(() => {
      const message = getFirstUserMessageOnHomePage();
      if (message) {
        setInputValue(message)
      }
    }, []);

    const handleSendMessage = (event: React.UIEvent, messageInput?: string) => {
      if (sendMessage) {
        setIsChatOpen(true);
        sendMessage(event, messageInput);
        setInputValue('');
        // If speech recognition is active, abort it
        if (recognition) {
          recognition.abort();
          setTranscript('');
          setIsListening(false);

          // Clear the input
          if (handleInputChange) {
            const syntheticEvent = {
              target: { value: '' },
            } as React.ChangeEvent<HTMLTextAreaElement>;
            handleInputChange(syntheticEvent);
          }
        }
      }
    };

    const pushFile = (file: File, dataUrl: string) => {
      setUploadedFiles?.((prev) => [...prev, file]);
      setImageDataList?.((prev) => [...prev, dataUrl]);
    };

    const fileToDataURL = (file: File) =>
      new Promise<string>((res, rej) => {
        const r = new FileReader();
        r.onload = () => res(r.result as string);
        r.onerror = rej;
        r.readAsDataURL(file);
      });

    const processAndAddFile = async (file: File) => {
      const MAX_FILE_SIZE = 1 * 1024 * 1024; // 1 MB
      let finalFile = file;

      if (file.type.startsWith('image/') && file.size > MAX_FILE_SIZE) {
        try {
          finalFile = await optimizeImage(file);
        } catch (err) {
          console.error('Image optimisation failed:', err);
        }
      }

      const dataUrl = await fileToDataURL(finalFile);
      pushFile(finalFile, dataUrl);
    };

    const handleFileUpload = () => {
      const picker = document.createElement('input');
      picker.type = 'file';
      picker.accept = '*/*';
      picker.multiple = true;

      picker.onchange = (e) => {
        const files = Array.from((e.target as HTMLInputElement).files || []);
        files.forEach(processAndAddFile); // ← the helper does all work
      };
      picker.click();
    };

    const optimizeImage = (file: File): Promise<File> => {
      const MAX_FILE_SIZE = 1 * 1024 * 1024; // 1MB

      return new Promise((resolve, reject) => {
        const img = new Image();
        const reader = new FileReader();

        reader.onload = (e) => {
          img.src = e.target?.result as string;
        };

        reader.onerror = reject;
        reader.readAsDataURL(file);

        img.onload = () => {
          const MAX_WIDTH = 1024;
          const MAX_HEIGHT = 1024;

          let width = img.width;
          let height = img.height;

          if (width > height) {
            if (width > MAX_WIDTH) {
              height = (height * MAX_WIDTH) / width;
              width = MAX_WIDTH;
            }
          } else {
            if (height > MAX_HEIGHT) {
              width = (width * MAX_HEIGHT) / height;
              height = MAX_HEIGHT;
            }
          }

          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (ctx) {
            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);

            const tryOptimize = (quality: number) => {
              canvas.toBlob(
                (blob) => {
                  if (blob) {
                    const optimizedFile = new File([blob], file.name, {
                      type: file.type,
                    });
                    if (optimizedFile.size <= MAX_FILE_SIZE) {
                      resolve(optimizedFile);
                    } else {
                      if (quality > 0.1) {
                        tryOptimize(quality - 0.05);
                      } else {
                        resolve(optimizedFile);
                      }
                    }
                  } else {
                    reject(new Error('Blob creation failed'));
                  }
                },
                'image/jpeg',
                quality,
              );
            };

            tryOptimize(0.7);
          }
        };
      });
    };

    const handlePaste = async (e: React.ClipboardEvent) => {
      const items = e.clipboardData?.items;

      if (!items) {
        return;
      }

      for (const item of items) {
        if (item.type.startsWith('image/')) {
          e.preventDefault();

          const file = item.getAsFile();

          if (file) {
            const reader = new FileReader();

            reader.onload = async (e) => {
              const base64Image = e.target?.result as string;

              // Validate & re-encode image
              const img = new Image();

              img.onload = () => {
                let width = img.width;
                let height = img.height;
                const aspectRatio = img.width / img.height;
                const isVerticalImage = aspectRatio < 0.5 && img.height > 4000;

                const MAX_WIDTH = 1920;
                const MAX_HEIGHT = isVerticalImage ? 7500 : 1080;

                if (width > MAX_WIDTH || height > MAX_HEIGHT) {
                  if (isVerticalImage) {
                    const ratio = MAX_HEIGHT / height;
                    height = MAX_HEIGHT;
                    width = width * ratio;
                  } else {
                    const ratio = Math.min(MAX_WIDTH / width, MAX_HEIGHT / height);
                    width = width * ratio;
                    height = height * ratio;
                  }
                }

                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;

                const ctx = canvas.getContext('2d');

                if (ctx) {
                  ctx.imageSmoothingEnabled = true;
                  ctx.imageSmoothingQuality = 'high';

                  ctx.drawImage(img, 0, 0, width, height);

                  let quality = 1;
                  const originalSize = file.size;

                  if (originalSize > 8000000) {
                    quality = 0.8;
                  } else if (originalSize > 4000000) {
                    quality = 0.9;
                  }

                  const processedImage = canvas.toDataURL('image/jpeg', quality);
                  pushFile(file, processedImage);
                }
              };

              img.onerror = (error) => {
                console.error('Error processing image:', error);
              };
              img.src = base64Image;
            };
            reader.readAsDataURL(file);
          }

          break;
        }
      }
    };

    const handleCleanUp = () => {
      if (isStreaming) {
        return;
      }

      setCleaningProject(true);

      const cleanUpPrompt =
        'Clean up the project by ensuring no single file exceeds 300 lines of code. Refactor large files into smaller, modular components while maintaining full functionality. Identify and remove all unused files, code, components, and any redundant data that are no longer needed. Ensure that all components remain properly connected and functional, avoiding any disruptions to the existing system. Maintain code integrity by verifying that no changes introduce errors or break current features. The goal is to optimize the project for efficiency, maintainability, and clarity.';
      sendMessage?.({} as React.UIEvent, cleanUpPrompt, {
        annotations: ['hidden'],
      });
    };

    const handleChecklist = () => {
      if (isStreaming) {
        return;
      }

      setCheckingList(true);

      const checklistPrompt =
        'Look at my initial prompt, understand the goal point by point and build for me a checklist with a green check for everything what has been done and with a red check what has left to be done.';
      sendMessage?.({} as React.UIEvent, checklistPrompt, {
        annotations: ['hidden'],
      });
    };

    const { mode } = useStore(chatStore);

    const placeholderText = mode === 'code' ? t('codePlaceholder') : t('defaultPlaceholder');

    function reducedMessage(content: string): string {
      const lines = content.split('\n');
      return lines.slice(0, 8).join('\n');
    }

    const isPreview = actionAlert?.source === 'preview';
    const isMigration = actionAlert?.source === 'migration';
    const fixMessage = `*Fix this ${isPreview ? 'preview' : isMigration ? 'migration' : 'terminal'} error* \n\`\`\`${isPreview ? 'js' : isMigration ? 'js' : 'sh'}\n${reducedMessage(actionAlert?.content ?? '')}\n\`\`\`\n`;

    useEffect(() => {
      if (
        (actionAlert?.type === 'error' && isStreaming) ||
        actionAlert?.type === 'preview' ||
        actionAlert?.type === 'migration'
      ) {
        sendMessage?.({} as React.UIEvent, fixMessage);
        clearAlert?.();
      }
    }, [actionAlert, isStreaming, fixMessage, sendMessage, clearAlert]);

    useEffect(() => {
      if (isStreaming) {
        workbenchStore.currentView.set('code');
      }
    }, [isStreaming]);

      // Convert image URL to File
    const urlToFile = async (url: string, filename: string, mimeType: string): Promise<File> => {
      const res = await fetch(url);
      const blob = await res.blob();
      return new File([blob], filename, { type: mimeType });
    };

    // Simulate drop event with a single File
    const simulateImageDrop = async (
      textarea: HTMLTextAreaElement | null | undefined, file: File, 
      image: { url: string; title:string; id: number; type: string }) => {
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(file);

      const dropEvent = new DragEvent('drop', {
        bubbles: true,
        cancelable: true,
        dataTransfer,
      });

      textarea.dispatchEvent(dropEvent);
      setInputValue(prev => prev + `${image.title} image's url is: ${image.url} \n`);
    };

    // Loop through and simulate all image drops
    const simulateMultipleImageDrops = async (
      images: { url: string; title:string; id: number; type: string }[]
    ) => {
      setInputValue(prev => prev + '\n\n\n\n\n\n\n\n')
      for (const image of images) {
        const file = await urlToFile(image.url, `image-${image.id}.jpeg`, image.type);
        await simulateImageDrop(
          textareaRef?.current, file, 
          image);
      }
    };

    const [isLoadingCardData, setIsLoadingCardData] = useState<boolean>(true);
    const [isLoadingCardDataError, setIsLoadingCardDataError] = useState<boolean>(false);
    const [cardVerified, setCardVerified] = useState<boolean>(false);
    useEffect(() => {
      setIsLoadingCardData(true);
      setIsLoadingCardDataError(false);

      const checkCardVerified = async () => {
        try {
          const user = getUser();

          if (user?.id) {
            const res: { isCardVerified: boolean } = await fetchUserData(user.id);
            setCardVerified(res.isCardVerified);
          }
        } catch (err) {
          console.error('Failed to fetch card data:', err);
          setIsLoadingCardDataError(true);
        } finally {
          setIsLoadingCardData(false);
        }
      };

      void checkCardVerified();
    }, []);

    const [showCard, setShowCard] = useState(true);

    const historyScrollRef = useRef<HTMLDivElement>(null);

    const [showVerificationModal, setShowVerificationModal] = useState(false);
    useEffect(() => {
      if (window?.location?.search === '?verified') {
        setShowVerificationModal(true);
      }
    }, []);

    useEffect(() => {
      if (showVerificationModal) {
        const newUrl = window.location.href.split('?')[0];
        window.history.replaceState({}, document.title, newUrl);
      }
    }, [showVerificationModal]);
    const [remainingTokens, setRemainingTokens] = useState<number | null>(null);
    const [freePromptsRemaining, setFreePromptsRemaining] = useState<number | null>(null);
    const tokensStoreCallback = (data: TokensSession | null) => {
      if (data) {
        setRemainingTokens(data.remainingTokens);
        setFreePromptsRemaining(data.freeTokensRemaining);
      }
    };

    const tokensStore = TokensStore.getInstance(tokensStoreCallback);

    async function refreshTokens() {
      const data = await tokensStore.refreshTokensData();

      tokensStoreCallback(data || null);
    }

    useEffect(() => {
      const wasInit = remainingTokens !== null && freePromptsRemaining !== null;

      if (!isStreaming || !wasInit) {
        refreshTokens();
      }
    }, [isStreaming]);

    useEffect(() => {
      const hideSpecificMessage = () => {
        const paragraphs = document.querySelectorAll('p');
        paragraphs.forEach((p) => {
          if (p.textContent === 'Create a database for my project') {
            const parentDiv = p.closest('.special-bg.user-message') as HTMLElement;

            if (parentDiv) {
              parentDiv.style.display = 'none';
            }
          }
        });
      };
      hideSpecificMessage();

      const observer = new MutationObserver(() => {
        hideSpecificMessage();
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      return () => {
        observer.disconnect();
      };
    }, []);

    const baseChat = (
      <>
        <div className={!chatStarted ? 'max-w-5xl mx-auto' : 'h-full overflow-hidden'}>
          {user && !cardVerified && !isLoadingCardData && showCard && !isLoadingCardDataError && remainingTokens <= 0 && (
            <CardVerificationModal
              onClose={() => {
                setShowCard(false);
              }}
            />
          )}
          <JoinUsLiveYoutube />
          {showVerificationModal && <CardSuccessModal onClose={() => setShowVerificationModal(false)} />}
          <StatusPopup
            isOpen={isStatusPopupOpen}
            type={'error'}
            message={
              t('SupabaseNotAvailable', 'Supabase is not available right now, please try again later.')
            }
            onClose={() => {
              clearAlert?.();
              setIsStatusPopupOpen(false);
            }}
          />
          <div
            ref={ref}
            className={classNames(
              styles.BaseChat,
              `relative flex h-full w-full overflow-hidden
          ${chatStarted ? 'chat-style-apply ' : 'first-prompt'}`,
            )}
            data-chat-visible={showChat}
          >
            <div
              ref={scrollRef}
              className={`flex flex-col pt-28 lg:flex-row overflow-y-auto w-full h-full ${!chatStarted ? 'lg:flex-row' : 'max-h-[calc(100vh-0)] new-bg-chat'}`}
            >
              <div
                style={{ zIndex: '1', margin: '0' }}
                className={classNames(
                  styles.Chat,
                  chatStarted ? '' : styles.chatbg,
                  'overflow-y-hidden flex flex-col flex-grow lg:min-w-[var(--chat-min-width)] h-full justify-between entire-chat',
                )}
              >
                {!chatStarted && (
                  <div style={{ zIndex: '1' }} id="intro" className=" mx-auto text-center px-4 lg:px-0">
                    <h1
                      className={classNames(
                        'text-3xl max-sm:text-2xl md:text-[30px] font-light mb-[24px] mt-8 mx-auto rexton-light text-white',
                      )}
                    >
                      {t('whatWouldYouLikeToBuild', 'Turn Your Idea into a Live Website or App in Minutes')}
                    </h1>
                    <motion.p
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="text-3xl md:text-2xl max-sm:text-lg font-light mb-6 rexton-light text-white"
                    >
                      <Trans
                        i18nKey="whatWouldYouLikeToBuildSubtitle"
                        ns="translation"
                        defaultValue="If you can <1>imagine</1> it, you can <5>code</5> it."
                        components={{
                          1: <span className="text-blue-400" />,
                          5: <span className="text-purple-400" />,
                        }}
                      />
                    </motion.p>
                    <p className="text-white/70 text-lg max-w-4xl mx-auto px-4 tracking-[0.4px] font-extralight">
                      {t(
                        'fromIdeaToDeployment',
                        'Start for free. Code anything. Turn your skills into opportunity with every prompt.',
                      )}
                    </p>
                  </div>
                )}
                <div
                  className={classNames('', {
                    'pl-[1rem] z-1 show-prompt  h-full flex flex-col bg-[#0A0F1C]': chatStarted,
                  })}
                >
                  <ClientOnly>
                    {() =>
                      chatStarted ? (
                        <>
                          <TabsSection activeTab={activeTab} setActiveTab={setActiveTab} />

                          {/* CHAT */}
                          {activeTab === 'chat' && (
                            <>
                              <div className="bg-[#0A0F1C] z-1 chat-tab-section max-[800px]:pr-[55px]">
                                <div className="bg-[#0A0F1C]">
                                  <div className="min-[800px]:max-w-[var(--chat-min-width)] with-border-section border-r-0 flex justify-between border border-white/5 items-center top-header-prompt text-sm px-4 py-2 shadow-xs max-sm:flex-wrap max-sm:gap-2 ">
                                    <div className="flex gap-[6px] items-center">
                                      <WithTooltip
                                        className={'text-[13px] font-normal tracking-[0.4px] !bg-[#1F2937]'}
                                        tooltip={checkingList ? t('checkingFeatures') : t('checklists')}
                                      >
                                        <button
                                          className="flex items-center gap-1.5 px-3 py-1.5 text-white/70 hover:bg-white/5 rounded-md transition-colors text-xs"
                                          style={{
                                            backgroundColor: 'transparent',
                                            display: chatStarted ? 'flex' : 'none',
                                          }}
                                          onClick={handleChecklist}
                                        >
                                          <CheckList checked={checkingList} />
                                          <span className="text-[13px] font-normal tracking-[0.4px]">
                                            {checkingList ? t('checkingFeatures') : t('checklists')}
                                          </span>
                                        </button>
                                      </WithTooltip>
                                      {chatStarted && (
                                        <WithTooltip tooltip={`${showPrompt ? t('hidePrompt', 'Hide Prompt') : t('showPrompt', 'Show Prompt')}`} className={'!bg-[#1F2937]'}>
                                          <button
                                            className={`p-1.5 bg-white/0 hover:bg-white/5  rounded-md transition-transform duration-300 ${showPrompt ? 'rotate-180' : 'rotate-0'}`}
                                            onClick={() => setShowPrompt(!showPrompt)}
                                          >
                                            <ChevronDownIcon className="w-4 h-4 text-white/50" />
                                          </button>
                                        </WithTooltip>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Chat messages */}
                              <Messages
                                ref={messageRef}
                                className="py-4 flex flex-col w-full flex-1 border-l border-l-white/5 max-w-chat mx-auto z-1 gap-messages base-response"
                                isStreaming={isStreaming}
                                messages={messages}
                                isTyping={true}
                              />
                            </>
                          )}

                          {/* HISTORY */}
                          {activeTab === 'history' && (
                            <div className={'h-full'}>
                              <div className="min-[800px]:max-w-[var(--chat-min-width)] px-4 py-2 max-[800px]:mr-[55px]">
                                {/* <HistorySwitch /> */}
                              </div>
                              {/*<h2 className="text-xl font-bold text-white">History</h2>*/}
                              <div
                                ref={historyScrollRef}
                                className="flex-1 overflow-y-auto h-full space-y-2 max-[800px]:mr-[55px]">
                                <HistoryContent messages={messages.filter((msg) => msg.role === 'assistant')} scrollContainerRef={historyScrollRef} />
                              </div>
                            </div>
                          )}

                          {/* ROADMAP */}
                          {activeTab === 'roadmap' && (
                            <div className="roadmap-tab h-full z-1 bg-[#0A0F1C] max-[800px]:pr-[55px]">
                              <div className="min-[800px]:max-w-[var(--chat-min-width)] px-4 py-2 border border-white/5 max-[800px]:mr-[55px]">
                                <RoadmapSwitch />
                              </div>
                              <RoadmapContent />
                            </div>
                          )}
                        </>
                      ) : null
                    }
                  </ClientOnly>

                  <div className="flex justify-center z-40 ">
                    <ClientOnly>
                      {() => (
                        <div
                          style={{ zIndex: '1', '--rotaion-variable': `${rotation}deg` }}
                          className={classNames(
                            `${(actionAlert?.type === 'preview' || actionAlert?.type === 'migration' || actionAlert?.type === 'error') && 'chat-on-error'} flex flex-col place-items-center align-items-center justify-center rounded-lg relative w-full max-w-[100vw] mx-auto z-prompt base-chat`,
                            {
                              'sticky bottom-2': chatStarted,
                              'chat-mode': mode === 'chat',
                            },
                          )}
                        >
                          <div className={classNames(styles.containerUnitTesting, 'w-full')}>
                            {/*{((actionAlert?.type === 'error' && isStreaming) ||*/}
                            {/*  actionAlert?.type === 'preview' ||*/}
                            {/*  actionAlert?.type === 'migration') && (*/}
                            {/*  <ChatAlert*/}
                            {/*    alert={actionAlert}*/}
                            {/*    clearAlert={() => clearAlert?.()}*/}
                            {/*    postMessage={(message) => {*/}
                            {/*      sendMessage?.({} as React.UIEvent, message);*/}
                            {/*      clearAlert?.();*/}
                            {/*    }}*/}
                            {/*  />*/}
                            {/*)}*/}
                            {actionAlert?.type === 'unitTesting' && !isStreaming && (
                              <ChatSuggestion
                                title={t('runUnitTestsSuggestionTitle', 'Suggestion')}
                                message={t(
                                  'runUnitTestsSuggestionMessage',
                                  'Would you like to Run Unit Tests for your project?',
                                )}
                                primaryButtonText={t('runUnitTestsPrimaryButton', 'Run Unit Tests')}
                                secondaryButtonText={t('runUnitTestsSecondaryButton', 'Dismiss')}
                                clearAlert={() => clearAlert?.()}
                                postMessage={() => {
                                  sendMessage?.({} as React.UIEvent, 'Run Unit Tests for my Project');
                                  clearAlert?.();
                                }}
                              />
                            )}
                            {actionAlert?.type === 'database' && !isStreaming && (
                              <ChatSuggestion
                                title={t('createDatabaseTitle', 'Database Creation')}
                                message={t(
                                  'createDatabaseMessage',
                                  'Would you like to create a database for your project?',
                                )}
                                primaryButtonText={t('createDatabasePrimaryButton', 'Create Database')}
                                secondaryButtonText={t('createDatabaseSecondaryButton', 'Dismiss')}
                                clearAlert={() => clearAlert?.()}
                                postMessage={() => {
                                  sendMessage?.({} as React.UIEvent, 'Create a database for my project');
                                  clearAlert?.();
                                }}
                              />
                            )}
                          </div>
                          <div className="w-100% h-full rotate-full relative group">
                            <FilePreview
                              files={uploadedFiles}
                              imageDataList={imageDataList}
                              onRemove={(index) => {
                                setUploadedFiles?.(uploadedFiles.filter((_, i) => i !== index));
                                setImageDataList?.(imageDataList.filter((_, i) => i !== index));
                              }}
                            />
                            <ClientOnly>
                              {() => (
                                <ScreenshotStateManager
                                  setUploadedFiles={setUploadedFiles}
                                  setImageDataList={setImageDataList}
                                  uploadedFiles={uploadedFiles}
                                  imageDataList={imageDataList}
                                />
                              )}
                            </ClientOnly>

                            <div
                              className={classNames(
                                { 'p-1 py-4 md:p-8 show-prompt': !chatStarted },
                                `relative transition-all flex flex-col  md:block ${!showPrompt ? '' : 'hide-prompt'}`,
                              )}
                            >
                              <div className={classNames({ 'chat-default flex': !chatStarted })}>
                                {(!messages || messages.length === 0) && (
                                  <>
                                    <ChatCodeSwitch
                                      activeTab={activeTab}
                                      changeTabToChat={() => {
                                        setActiveTab('chat');
                                      }}
                                    />
                                    <AIModelSelector />
                                  </>
                                )}
                              </div>
                              <div className="show-prompt border w-full border-white/5 flex flex-col gap-4 py-4 px-0.5 md:py-4 md:px-4">
                                <div
                                  className={classNames(
                                    'container-textarea border border-white/5 hover:border-white/10 transition-all hover:border-biela-elements-focus duration-200 text-white transition-colors rounded-lg bg-transparent',
                                    {
                                      'border border-white/5': !isDragActive, // show borders only when NOT dragging
                                      dragging: isDragActive, // add “dragging” when active
                                    },
                                  )}
                                >
                                  <div
                                    className={classNames(styles.dropZone, { [styles.dragActive]: isDragActive })}
                                    /* …handlers… */
                                    style={{ position: 'relative' }}
                                    onDragEnter={() => setIsDragActive(true)}
                                    onDragOver={(e) => {
                                      e.preventDefault();
                                      // e.currentTarget.style.background = '#1488fc';
                                      e.currentTarget.style.borderRadius = '9px';
                                      setIsDragActive(true);
                                    }}
                                    // onDragLeave={() => setIsDragActive(false)}

                                    onDragLeave={(e) => {
                                      setIsDragActive(false);
                                      e.currentTarget.style.background = 'none';
                                    }}
                                    onDrop={(e) => {
                                      e.preventDefault();
                                      e.currentTarget.style.background = 'none';
                                      e.stopPropagation();
                                      setIsDragActive(false); // <<< this was missing
                                    }}
                                  >
                                    {isDragActive && (
                                      <div
                                        className="box absolute inset-0 flex items-center justify-center
                                                 rounded-lg pointer-events-none"
                                      >
                                        <span className="text-white text-sm font-medium">Drop files to upload</span>
                                      </div>
                                    )}
                                    <textarea
                                      rows={3}
                                      ref={textareaRef}
                                      className={classNames(
                                        { '': !chatStarted },
                                        'w-full !h-[80px] !max-h-[80px] p-4 !overflow-auto outline-none resize-none text-biela-elements-textPrimary placeholder-biela-elements-textTertiary bg-transparent text-sm chat-textarea-font',
                                        'transition-all duration-200',
                                        'hover:border-biela-elements-focus',
                                      )}
                                      value={inputValue}
                                      onDragEnter={(e) => {
                                        e.preventDefault();
                                      }}
                                      onDragOver={(e) => {
                                        e.preventDefault();
                                      }}
                                      onDragLeave={(e) => {
                                        e.preventDefault();
                                      }}
                                      onDrop={(e) => {
                                        e.preventDefault();
                                        const files = Array.from(e.dataTransfer.files);
                                        files.forEach((file) => {
                                          if (file.type.startsWith('image/')) {
                                            const reader = new FileReader();
                                            reader.onload = async (e) => {
                                              const base64Image = e.target?.result as string;
                                              const img = new Image();
                                              img.onload = () => {
                                                const canvas = document.createElement('canvas');
                                                canvas.width = img.width;
                                                canvas.height = img.height;
                                                const ctx = canvas.getContext('2d');
                                                if (ctx) {
                                                  ctx.drawImage(img, 0, 0);
                                                  const processedImage = canvas.toDataURL(file.type, 1.0);
                                                  pushFile(file, processedImage);
                                                }
                                              };
                                              img.onerror = (error) => {
                                                console.error('Error processing image:', error);
                                              };
                                              img.src = base64Image;
                                            };
                                            reader.readAsDataURL(file);
                                          }
                                        });
                                      }}
                                      onKeyDown={(event) => {
                                        if (event.key === 'Enter') {
                                          if (event.shiftKey) return;
                                          event.preventDefault();
                                          if (isStreaming) {
                                            handleStop?.();
                                            return;
                                          }
                                          if (event.nativeEvent.isComposing) return;
                                          handleSendMessage?.(event);
                                        }
                                      }}
                                      onKeyDownCapture={(event) => {
                                        if (event.key === 'Enter' && isStreaming) {
                                          event.preventDefault();
                                          event.stopPropagation();
                                          return;
                                        }
                                      }}
                                      onChange={(event) => {
                                        setInputValue(event.target.value);
                                        handleInputChange?.(event);
                                      }}
                                      onPaste={handlePaste}
                                      placeholder={isDragActive ? '' : placeholderText}
                                      translate="no"
                                    />
                                  </div>
                                </div>

                                <div className="flex justify-between items-center text-sm prompt-actions-button shadow-xs h-[52px]">
                                  <ActionButtons
                                    onFileClick={handleFileUpload}
                                    isListening={isListening}
                                    onStartVoice={startListening}
                                    onStopVoice={stopListening}
                                    enhancePrompt={enhancePrompt}
                                    input={input}
                                    enhancingPrompt={enhancingPrompt}
                                    handleCleanUp={handleCleanUp}
                                    chatStarted={chatStarted}
                                  />
                                  <ClientOnly>
                                    {() => (
                                      <SendButton
                                        // show={!!input.length || isStreaming || !!uploadedFiles.length}
                                        show={true}
                                        isStreaming={isStreaming}
                                        hasContent={hasContent}
                                        onClick={(event) => {
                                          if (isStreaming) {
                                            abort();
                                            return;
                                          }
                                          if (inputValue.length || uploadedFiles.length) {
                                            handleSendMessage?.(event, inputValue);
                                          }
                                        }}
                                        type={!isStreaming ? t('sendButton') : t('abortButton')}
                                      />
                                    )}
                                  </ClientOnly>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </ClientOnly>
                  </div>
                </div>
                {/*{!chatStarted && <CopyRighting />}*/}
              </div>
              <Suspense>
                 {chatStarted && <Workbench sendMessage={sendMessage} chatStarted={chatStarted} isStreaming={isStreaming} mode={mode} uploadContentStudioImages={simulateMultipleImageDrops}/>}
              </Suspense>
            </div>
          </div>
        </div>
        {!chatStarted && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="mb-12 px-4 container mx-auto px-6 z-10"
          >
            <div className="text-center mb-6">
              <h2 className="text-white/90 text-xl font-light rexton-light">
                {t('inspirationTitle', 'Need inspiration? Try one of these:')}
              </h2>
            </div>

            <ProjectIdeasCarousel setPrompt={handleIdeaClick} />
          </motion.div>
        )}
      </>
    );

    return <Tooltip.Provider delayDuration={200}>{baseChat}</Tooltip.Provider>;
  },
);
