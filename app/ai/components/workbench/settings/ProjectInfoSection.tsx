import React, { FormEvent, useCallback, useEffect, useState } from 'react';
import { ArrowPathIcon, CodeBracketIcon, PencilSquareIcon } from '@heroicons/react/24/outline';
import ProjectActions from './ProjectActions';
import Modal from '../../Modal';
import { useUser } from '~/ai/lib/context/userContext';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import { chatId, db, updateChatDescription } from '~/ai/lib/persistence';
import WithTooltip from '~/ai/components/Tooltip';
import { FaCheck, FaSpinner, FaTimes } from 'react-icons/fa';
import { FaCodeFork } from 'react-icons/fa6';
import { useTranslation } from 'react-i18next';
import { ArrowDownTrayIcon } from '@heroicons/react/16/solid';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid';
import type { DropdownItems } from '~/components/common/Dropdown';
import type { ProjectCommits } from '~/ai/lib/stores/projects/code';
import {
  fetchProjects,
  getProject,
  Project,
  updateProjectName as apiUpdateProjectName,
} from '~/api/projectsApi';

interface Collaborator {
  email: string;
  role: string;
  status: 'pending' | 'accepted';
}

interface ProjectData {
  [key: string]: any;
}

function generateProjectSlug(chatId: string | undefined): string | null {
  try {
    const userStore = UserStore.getInstance();
    const user = userStore.getUser();

    if (!user || !user.id) {
      console.warn('User is not logged in or user ID is missing');
      return null;
    }

    return `${chatId}-${user.id}`;
  } catch (error) {
    console.error('Error generating project slug:', error);
    return null;
  }
}

interface ProjectInfoSectionProps {
  hasCommited: boolean;
  isForkingApp: boolean;
  handleForkClick: (event: React.UIEvent) => void;
  commitToRestore?: DropdownItems;
  commits: ProjectCommits[] | null;
  handleRestoreAppClick: (event: React.UIEvent) => void;
  isStreaming?: boolean;
  showSaveTooltip: boolean;
  setShowSaveTooltip: (value: boolean) => void;
  handleSaveClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
  isSavingApp: boolean;
}

function ProjectInfoSection({hasCommited,isForkingApp,handleForkClick,commitToRestore,commits,handleRestoreAppClick,isStreaming,showSaveTooltip,setShowSaveTooltip,handleSaveClick,isSavingApp}:ProjectInfoSectionProps) {
  const { t } = useTranslation();
  const [username, setUsername] = useState<string | undefined>();
  const [projectName, setProjectName] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editingName, setEditingName] = useState<string>(projectName);
  const [newCollaborator, setNewCollaborator] = useState<string>('');
  const [showInviteModal, setShowInviteModal] = useState<boolean>(false);
  const [collaboratorToRemove, setCollaboratorToRemove] = useState<Collaborator | null>(null);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);

  const [editingProjectId, setEditingProjectId] = useState<string | null>(null);
  const [editedProjectName, setEditedProjectName] = useState<string>('');
  const [savingProjectName, setSavingProjectName] = useState(false);
  const [originalProjectName, setOriginalProjectName] = useState<string>('');

  const [projectData, setProjectData] = useState<Project | null>(null);
  const [isLoadingProject, setIsLoadingProject] = useState<boolean>(true);

  const { getUser } = useUser();

  const fetchProjectWithEstimations = useCallback(async () => {
    const currentChatId = chatId.get();
    if (!currentChatId) {
      console.error('No chatId available');
      setIsLoadingProject(false);
      return;
    }
    setIsLoadingProject(true);
    try {
      const projectSlug = generateProjectSlug(currentChatId);
      if (!projectSlug) {
        console.error('Project slug generation failed.');
        setIsLoadingProject(false);
        return;
      }

      const fetchedProject = await getProject(projectSlug);
      if (!fetchedProject || !fetchedProject.projectName) {
        console.error('Project not found or initial fetch failed.');
        // Attempt to fetch via project list if direct get fails or for estimations
        const projectsList = await fetchProjects(
          undefined,
          0,
          10,
          'updatedAt',
          'DESC',
          fetchedProject?.projectName || projectSlug.split('-')[0],
        );
        const projectFromList = projectsList.items.find(
          (item) => item.projectSlug === projectSlug || item.projectName === fetchedProject?.projectName,
        );

        if (projectFromList) {
          setProjectData(projectFromList);
        } else {
          console.error(`Project with slug ${projectSlug} not found in list.`);
          setProjectData(null);
        }
        setIsLoadingProject(false);
        return;
      }

      setProjectData(fetchedProject);

      if (!fetchedProject.estimations) {
        const projectsListData = await fetchProjects(undefined, 0, 10, 'updatedAt', 'DESC', fetchedProject.projectName);
        const projectWithEstimations = projectsListData.items.find(
          (item) => item.projectName === fetchedProject.projectName && item.projectSlug === projectSlug,
        );
        if (projectWithEstimations) {
          setProjectData(projectWithEstimations);
        } else {
          setProjectData(fetchedProject);
        }
      }
    } catch (error) {
      console.error('Error fetching project with estimations:', error);
      setProjectData(null);
    } finally {
      setIsLoadingProject(false);
    }
  }, []);

  useEffect(() => {
    const user = getUser();
    if (user && user?.username) {
      setUsername(user.username);
    }
  }, [getUser]);

  useEffect(() => {
    fetchProjectWithEstimations();
  }, [fetchProjectWithEstimations, chatId.get()]);

  const handleRename = () => {
    if (!projectData) return;
    setEditingProjectId(projectData._id.toString());
    setEditedProjectName(projectData.projectName || projectData.projectSlug || '');
    setOriginalProjectName(projectData.projectName || projectData.projectSlug || '');
  };

  const handleInvite = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (newCollaborator) {
      setShowInviteModal(true);
    }
  };

  const confirmInvite = () => {
    setCollaborators([
      ...collaborators,
      {
        email: newCollaborator,
        role: 'Collaborator',
        status: 'pending',
      },
    ]);
    setNewCollaborator('');
    setShowInviteModal(false);
  };

  const handleRemoveCollaborator = (collaborator: Collaborator) => {
    setCollaboratorToRemove(collaborator);
  };

  const confirmRemoveCollaborator = () => {
    if (collaboratorToRemove) {
      setCollaborators(collaborators.filter((c) => c.email !== collaboratorToRemove.email));
      setCollaboratorToRemove(null);
    }
  };

  const handleSaveProjectName = async () => {
    if (!projectData || !projectData.projectSlug) {
      console.error('Project data or slug is missing.');
      return;
    }
    if (editedProjectName.trim() === '' || editedProjectName === originalProjectName) {
      setEditingProjectId(null);
      return;
    }

    setSavingProjectName(true);

    try {
      await apiUpdateProjectName(projectData.projectSlug, editedProjectName);

      if (db) {
        const currentChatId = projectData.projectSlug.split('-')[0];
        if (currentChatId) {
          await updateChatDescription(db, currentChatId, editedProjectName);
        } else {
          console.warn('Could not extract chatId from projectSlug for DB update');
        }
      }

      // ✅ Actualizează doar local, evită 3 requesturi în plus
      setProjectData((prev) => (prev ? { ...prev, projectName: editedProjectName } : prev));

      setEditingProjectId(null);
    } catch (error) {
      console.error('Error updating project name:', error);
    } finally {
      setSavingProjectName(false);
    }
  };


  const handleCancelEditProjectName = () => {
    setEditingProjectId(null);
    setEditedProjectName('');
  };

  if (isLoadingProject) {
    return (
      <div className="flex items-center justify-center h-40">
        <FaSpinner className="animate-spin text-3xl text-green-400" />
      </div>
    );
  }

  if (!projectData) {
    return <div className="p-6 space-y-6 text-white/70">Project information could not be loaded.</div>;
  }

  return (
    <div className="space-y-6">
      <div className="bg-[#1A1F2E]/50 rounded-xl border border-white/5 overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-white/5">
          <div className="flex items-center gap-3">
            <CodeBracketIcon className="w-5 h-5 text-[#4ADE80]" />
            <h2 className="text-lg font-medium text-white">Project Info</h2>
          </div>
          <ProjectActions />
        </div>

        <div className="p-6 space-y-6">
          <div className="flex items-center justify-between flex-wrap gap-2">
            <div>
              <div className="flex items-center gap-3 mb-2 min-h-[36px]">
                {editingProjectId === projectData._id.toString() ? (
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={editedProjectName}
                      onChange={(e) => setEditedProjectName(e.target.value)}
                      className="font-manrope w-96 max-sm:w-64 bg-black/20 rounded-lg px-3 py-1 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 font-light text-lg lg:text-xl"
                      autoFocus
                      onKeyDown={(e) => e.key === 'Enter' && handleSaveProjectName()}
                    />
                    <button
                      onClick={handleSaveProjectName}
                      className={`p-1.5 lg:p-2 bg-green-500/40 hover:bg-green-500/70 rounded-lg transition-colors group ${
                        savingProjectName ||
                        editedProjectName.trim() === '' ||
                        editedProjectName === originalProjectName
                          ? 'opacity-50 cursor-not-allowed'
                          : ''
                      }`}
                      disabled={
                        savingProjectName ||
                        editedProjectName.trim() === '' ||
                        editedProjectName === originalProjectName
                      }
                    >
                      {savingProjectName ? (
                        <FaSpinner className="animate-spin text-gray-400" />
                      ) : (
                        <FaCheck className="text-gray-400 group-hover:text-white" />
                      )}
                    </button>
                    <button
                      onClick={handleCancelEditProjectName}
                      className="p-1.5 lg:p-2 bg-red-500/40 hover:bg-red-500/70 rounded-lg transition-colors group"
                    >
                      <FaTimes className="text-gray-400 group-hover:text-white" />
                    </button>
                  </div>
                ) : (
                  <h3 className="text-lg lg:text-xl font-light ellipsis-text-name">
                    {projectData.projectName || projectData.projectSlug}
                  </h3>
                )}
              </div>
              <p className="text-sm text-white/50">General information about your project.</p>
            </div>

            <div className="flex gap-1 flex-wrap">
              {editingProjectId !== projectData._id.toString() && (
                <button
                  onClick={handleRename}
                  className="flex items-center gap-2 px-4 py-2 bg-white/5 rounded-lg text-sm text-white/70 hover:bg-white/10 transition-colors"
                >
                  <PencilSquareIcon className="w-4 h-4" />
                  Rename
                </button>
              )}
              <WithTooltip
                tooltip={
                  hasCommited ? (
                    <div className={'flex flex-col'}>
                      <span>Lastly saved at:</span>
                      <span>{commitToRestore?.label ?? new Date(commits?.[0]?.date as Date)?.toLocaleString()}</span>
                    </div>
                  ) : (
                    <span>There's no saved app version.</span>
                  )
                }
                position={'bottom'}
              >
                <button
                  onClick={handleRestoreAppClick}
                  disabled={!hasCommited || isStreaming}
                  className={`cursor-pointer flex items-center gap-2 px-3 py-1.5 rounded-md text-[12px] xl:text-[14px] font-light tracking-[0.4px] transition-colors text-white/70 bg-white/0 hover:bg-white/10 ${!hasCommited || isStreaming ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <ArrowPathIcon className="w-4 h-4 font-normal" />
                  <span>{t('RestoreApp', 'Restore')}</span>
                </button>
              </WithTooltip>
              <WithTooltip
                tooltip="Save Successful!"
                position="bottom"
                open={showSaveTooltip}
                onOpenChange={(open) => setShowSaveTooltip(open)}
              >
                <button
                  onClick={handleSaveClick}
                  disabled={isStreaming || isSavingApp}
                  className={`cursor-pointer flex items-center gap-2 px-3 py-1.5 rounded-md text-[12px] xl:text-[14px] font-light tracking-[0.4px] transition-colors text-white/70 bg-white/0 hover:bg-white/10 ${isStreaming || isSavingApp ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {isSavingApp ? (
                    <FaSpinner className="w-4 h-4 animate-spin" />
                  ) : (
                    <ArrowDownTrayIcon className="w-4 h-4" />
                  )}
                  {isSavingApp ? t('Saving', 'Saving') : t('SaveApp', 'Save')}
                </button>
              </WithTooltip>
              <button
                onClick={handleForkClick}
                disabled={isStreaming || isForkingApp}
                className={`cursor-pointer flex items-center gap-2 px-3 py-1.5 rounded-md text-[12px] xl:text-[14px] font-light tracking-[0.4px] transition-colors text-white/70 bg-white/0 hover:bg-white/10 ${isStreaming || isForkingApp ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isForkingApp ? (
                  <FaSpinner className="w-4 h-4 animate-spin" />
                ) : (
                  <FaCodeFork className="w-4 h-4" style={{ fontWeight: 300 }} />
                )}
                <span>{isForkingApp ? t('Forking', 'Forking...') : t('ForkChat', 'Fork')}</span>
              </button>
            </div>
          </div>
          <p className="text-sm text-white/50">General information about your project.</p>
          <div className="grid gap-4" style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))' }}>
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-white/50 mb-1">Owner</div>
              <div className="text-white">{username || '-'}</div>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-white/50 mb-1">Tech stack</div>
              <div className="text-white">{projectData?.estimations?.keyTechnologies?.join(', ') || '-'}</div>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-white/50 mb-1">Feature count</div>
              <div className="text-white">{projectData?.estimations?.featureCount || '-'}</div>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-white/50 mb-1">Unique component count</div>
              <div className="text-white">{projectData?.estimations?.uniqueComponentCount || '-'}</div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        title="INVITE COLLABORATOR"
        message={`You are about to invite ${newCollaborator} to collaborate on this project.`}
        actionLabel="Send Invite"
        onAction={confirmInvite}
        variant="duplicate"
      />

      <Modal
        isOpen={!!collaboratorToRemove}
        onClose={() => setCollaboratorToRemove(null)}
        title="REMOVE COLLABORATOR"
        message={`You are about to remove ${collaboratorToRemove?.email} from this project.`}
        actionLabel="Remove"
        onAction={confirmRemoveCollaborator}
        variant="delete"
      />
    </div>
  );
}

export default ProjectInfoSection;
