import React, { useState } from 'react';
import { useLoaderData } from 'react-router-dom';
import { ArrowDownTrayIcon, ChatBubbleLeftIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import ActionButton from '../../ActionButton';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import { chatId, useChatHistory } from '~/ai/lib/persistence';
import { useTranslation } from 'react-i18next';
import { Copy } from 'lucide-react';
import { FaPaperPlane } from 'react-icons/fa';
import { backendApiFetch } from '~/ai/lib/backend-api';
import TransferModal from '~/backOffice/components/project/TransferProjectModal';

interface LoaderDataType {
  id?: string;
}

function ProjectActions() {
  const { downloadProject, exportChat, duplicateCurrentChat } = useChatHistory();
  const [downloading, setDownloading] = React.useState(false);
  const [transferError, setTransferError] = useState<string | null>(null);
  const [showTransferProjectModal, setShowTransferProjectModal] = useState<string | boolean>(false);
  const [loadingTransferProjects, setLoadingTransferProjects] = useState<boolean>(false);
  const { t } = useTranslation();
  const generateProjectSlug = (chatId: string | undefined): string | null => {
    try {
      const user = UserStore.getInstance().getUser();
      if (!user || !user.id) {
        console.warn('User is not logged in or user ID is missing');
        return null;
      }
      if (!chatId) {
        console.warn('Chat ID is missing');
        return null;
      }
      return `${chatId}-${user.id}`;
    } catch (error) {
      console.error('Error generating project slug:', error);
      return null;
    }
  };

  const handleDownload = async () => {
    const slug = generateProjectSlug(chatId.get());
    if (!slug) {
      toast.error(t('projectActions.invalidSlug'));
      return;
    }

    setDownloading(true);
    try {
      await downloadProject(slug);
      toast.success(t('projectActions.downloadSuccess'));
    } catch (error) {
      console.error('Error downloading project:', error);
      toast.error(t('projectActions.downloadError'));
    } finally {
      setDownloading(false);
    }
  };

  const handleExport = async () => {
    const slug = generateProjectSlug(chatId.get());
    if (!slug) {
      toast.error(t('projectActions.invalidSlug'));
      return;
    }

    try {
      await exportChat(slug);
      toast.success(t('projectActions.exportSuccess'));
    } catch (error) {
      console.error('Error exporting chat:', error);
      toast.error(t('projectActions.exportError'));
    }
  };

  const handleDuplicate = async () => {
    const slug = generateProjectSlug(chatId.get());
    if (!slug) {
      toast.error(t('projectActions.invalidSlug'));
      return;
    }

    try {
      await duplicateCurrentChat(slug);
      toast.success(t('projectActions.duplicateSuccess'));
    } catch (error) {
      console.error('Error duplicating chat:', error);
      toast.error(t('projectActions.duplicateError'));
    }
  };

  const handleCloseTransfer = () => {
    setShowTransferProjectModal(false);
    setTransferError(null);
  };
  const handleTransferProject = async (transferTo: string) => {
    setLoadingTransferProjects(true);
    const slug = generateProjectSlug(chatId.get());
    if (!slug) {
      setTransferError(t('projectActions.invalidSlug'));
      setLoadingTransferProjects(false);
      return;
    }
    try {
      const response = await backendApiFetch(`/user-projects/${slug}/transfer/${transferTo}`, {
        method: 'POST', headers: { 'Content-Type': 'application/json' } }
      );
      const data = await response.json();

      if (response.ok) {
        setTransferError(null);
        toast.success(
          t('transferSuccess', { user: transferTo, defaultValue: `Transferred successfully to ${transferTo}` }),
        );
      } else {
        throw new Error(data.message || 'transfer failed');
      }
    } catch (error: any) {
      const msgText = error.message || '';
      let message = t('transferError', 'Error transferring project');

      if (msgText.includes('permission')) {
        message = t('dontHavePermisionToTransfer', 'You do not have permission to transfer this project');
      } else if (msgText.includes('not found')) {
        message = t('transferProjectUserNotFound', {
          user: transferTo,
          defaultValue: `User ${transferTo} was not found!`,
        });
      } else if (msgText.includes('own account')) {
        message = t('transferErrorOwnAccount', 'You cannot transfer a project to your own account.');
      }
      setTransferError(message);
    } finally {
      setLoadingTransferProjects(false);
    }
  };

  return (
    <div className="flex items-center px-2.5 py-0.5 bg-[#232e41] border border-[#293447] rounded-lg border-radius-10">
      <ActionButton
        id={'download-project-files'}
        icon={ArrowDownTrayIcon}
        label="Download project files"
        onClick={handleDownload}
        variant="download"
        downloading={downloading}
      />
      <ActionButton
        id={'export-chat'}
        icon={ChatBubbleLeftIcon}
        label="Export project chat"
        onClick={handleExport} />
      <ActionButton
        id={'duplicate-project'}
        icon={Copy}
        label={t('duplicateProject', 'Duplicate project')}
        onClick={handleDuplicate} />
      <ActionButton
        id={'transfer-project'}
        icon={FaPaperPlane}
        openPosition={'bottom-end'}
        label={t('transferProject', 'Share a project copy with another biela user')}
        onClick={() => {
          setShowTransferProjectModal(true);
        }}
      />
      <TransferModal
        isOpen={showTransferProjectModal}
        isLoading={loadingTransferProjects}
        errorMessage={transferError}
        onClose={handleCloseTransfer}
        onTransfer={handleTransferProject}
      />
    </div>
  );
}

export default ProjectActions;
