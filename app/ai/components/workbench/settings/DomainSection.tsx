import React, { useEffect, useState } from 'react';
import {
  AcademicCapIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  GlobeAltIcon,
  PlusIcon,
  TrashIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { addDomain, deleteDomain, getDomains } from '~/api/cloudApi';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import { deploy } from '~/ai/lib/stores/cloud/cloud';
import { fetchProjects, getProject } from '~/api/projectsApi';
import { motion } from 'framer-motion';
import { chatId } from '~/ai/lib/persistence';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { useTranslation } from 'react-i18next';

interface Domain {
  id: string;
  name: string;
  isDefault: boolean;
  verified: boolean;
  type: string;
  verifications: [];
}
interface DomainsResponse {
  domains: Domain[];
}
interface ProjectData {
  [key: string]: any;
}

const generateProjectSlug = (chatId: string | undefined): string | null => {
  try {
    const userStore = UserStore.getInstance();
    const user = userStore.getUser();

    if (!user || !user.id) {
      console.warn('User is not logged in or user ID is missing');
      return null;
    }

    if (!chatId) {
      console.warn('Chat ID is missing');
      return null;
    }

    return `${chatId}-${user.id}`;
  } catch (error) {
    console.error('Error generating project slug:', error);
    return null;
  }
};

export const verifyDomainStatus = async (projectSlug: string, domainName: string): Promise<Domain | null> => {
  try {
    const response = await backendApiFetch(`/cloud/app/${projectSlug}/dns?verify=true`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Failed to verify domain status for ${domainName}:`, response.statusText);
      return null;
    }

    const data = (await response.json()) as Domain;
    return data;
  } catch (error) {
    console.error(`Error verifying domain status for ${domainName}:`, error);
    return null;
  }
};

function DomainSection() {
  const [domains, setDomains] = useState<Domain[]>([]);

  const [projectData, setProjectData] = useState<ProjectData | null>(null);
  const [isAddingDomain, setIsAddingDomain] = useState(false);
  const [newDomain, setNewDomain] = useState('');
  const [verificationStep, setVerificationStep] = useState(0);
  const [verificationStatus, setVerificationStatus] = useState<'verifying' | 'success' | 'error' | null>(null);
  const [verificationError, setVerificationError] = useState('');
  const [domainToRemove, setDomainToRemove] = useState<Domain | null>(null);
  const [isDeploying, setIsDeploying] = useState<boolean>(false);
  const [expandedDNSDomainId, setExpandedDNSDomainId] = useState<string | null>(null);
  const [expandedRemoveDomainId, setExpandedRemoveDomainId] = useState<string | null>(null);
  const { t } = useTranslation("translation");
  const [loading, setLoading] = useState(false);

  const handleRefresh = async (domainToRefresh: Domain) => {
    setLoading(true);
    try {
      const projectSlug = generateProjectSlug(chatId.get());
      if (!projectSlug) {
        console.error('Project slug generation failed.');
        setLoading(false);
        return;
      }
      const updatedDomain = await verifyDomainStatus(projectSlug, domainToRefresh.name);
      console.log(updatedDomain,'updatedDomain');
      if (!updatedDomain) {
        console.error(`Failed to verify domain: ${domainToRefresh.name}`);
        setLoading(false);
        return;
      }

      setDomains(updatedDomain.domains);

      console.log(`Domain ${domainToRefresh.name} refreshed successfully.`);
    } catch (error) {
      console.error('Failed to refresh domain status:', error);
    } finally {
      setLoading(false);
    }
  };
  console.log(domains,'domains');
  const fetchDomains = async () => {
    const projectSlug = generateProjectSlug(chatId.get());

    if (projectSlug) {
      try {
        const fetchedDomains: DomainsResponse = await getDomains(projectSlug, true);
        setDomains(fetchedDomains.domains);
      } catch (error) {
        console.error('Failed to fetch domains:', error);
      }
    }
  };

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout>;
    if (verificationStatus === 'success') {
      timer = setTimeout(() => setVerificationStatus(null), 3000);
    }
    return () => clearTimeout(timer);
  }, [verificationStatus]);

  useEffect(() => {
    fetchDomains();
  }, [chatId]);

  const handleAddDomain = () => {
    setIsAddingDomain(true);
    setVerificationStep(0);
    setNewDomain('');
    setVerificationStatus(null);
    setVerificationError('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleContinueToVerification();
    }
  };

  const handleCancelAddDomain = () => {
    setIsAddingDomain(false);
    setVerificationStep(0);
    setNewDomain('');
    setVerificationStatus(null);
  };

  const handleDomainInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewDomain(e.target.value);
  };

  const handleContinueToVerification = async () => {
    const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i;

    if (!domainRegex.test(newDomain)) {
      setVerificationError(t('errorInvalidDomain', 'Please enter a valid domain name (e.g., example.com)'));
      return;
    }

    if (domains.some((domain) => domain?.name.toLowerCase() === newDomain.toLowerCase())) {
      setVerificationError(t('errorDuplicateDomain', 'This domain name is already connected to your project'));
      return;
    }

    setVerificationError('');
    await handleVerifyDomain();
    setVerificationStep(1);
  };

  useEffect(() => {
    const fetchProjectWithEstimations = async () => {
      if (!chatId.get()) {
        console.error('No chatId available');
        return;
      }

      try {
        const projectSlug = generateProjectSlug(chatId.get());

        if (!projectSlug) {
          console.error('Project slug generation failed.');
          return;
        }

        const project = await getProject(projectSlug);

        if (!project || !project.projectName) {
          console.error('Project not found.');
          return;
        }

        const projectData = await fetchProjects(undefined, 0, 10, 'updatedAt', 'DESC', project.projectName);

        const projectWithEstimations = projectData.items.find((item) => item.projectName === project.projectName);

        if (projectWithEstimations) {
          setProjectData(projectWithEstimations);
          console.log(projectData);
        } else {
          console.error(`Project with name ${project.projectName} not found.`);
        }
      } catch (error) {
        console.error('Error fetching project with estimations:', error);
      }
    };

    fetchProjectWithEstimations();
  }, [chatId]);

  const handleVerifyDomain = async () => {
    setVerificationStatus('verifying');

    try {
      const projectSlug = generateProjectSlug(chatId.get());

      if (!projectSlug) {
        setVerificationStatus('error');
        setVerificationError('Project slug generation failed.');

        return;
      }

      if (projectData && projectData.isDeployed === false) {
        setIsDeploying(true);
        await deploy();
      }

      setIsDeploying(false);
      await addDomain(projectSlug, { name: newDomain });

      setVerificationStatus('success');

      fetchDomains();
    } catch (error) {
      setVerificationStatus('error');
      setVerificationError(t('errorAddFail', '`Failed to add domain name.`'));
    }
  };

  const handleFinalVerifyDomain = () => {
    setTimeout(() => {
      setIsAddingDomain(false);
      setVerificationStep(0);
      setVerificationStatus(null);
    }, 2000);
  };

  const handleRemoveDomainClick = (domain: Domain) => {
    setDomainToRemove(domain);
    setExpandedRemoveDomainId((prev) => (prev === domain.id ? null : domain.id));
  };

  const handleCancelRemoveDomain = () => {
    setExpandedRemoveDomainId(null);
  };

  const handleConfirmRemoveDomain = async () => {
    if (domainToRemove) {
      const projectSlug = generateProjectSlug(chatId.get());

      if (projectSlug) {
        try {
          await deleteDomain(projectSlug, domainToRemove.name);
          setDomains((prevDomains) => prevDomains.filter((domain) => domain?.id !== domainToRemove.id)); // Actualizează lista de domenii
          setDomainToRemove(null);
          setExpandedRemoveDomainId(null);
        } catch (error) {
          console.error('Failed to remove domain name:', error);
        }
      }
    }
  };

  const isSubdomain = (domain: string) => {
    if (!domain) {
      return false;
    }

    return domain.split('.').length > 2;
  };

  const handleClickDNS = (domain: Domain) => {
    setExpandedDNSDomainId((prev) => (prev === domain.id ? null : domain.id));
  };

  const matchedDomain = domains.find((domain) => domain.name === newDomain);

  return (
    <div className="bg-[#1A1F2E]/50 rounded-xl border border-white/5 overflow-hidden">
      <div className="flex items-center justify-between p-4 border-b border-white/5">
        <div className="flex items-center gap-3">
          <AcademicCapIcon className="w-5 h-5 text-[#A78BFA]" />
          <h2 className="text-lg font-medium text-white">
            {t('sectionTitle', 'Domain Management')}
          </h2>
        </div>

        {!isAddingDomain && (
          <button
            onClick={handleAddDomain}
            className="flex items-center gap-2 px-3 py-1.5 bg-[#A78BFA]/10 text-[#A78BFA] rounded-lg hover:bg-[#A78BFA]/20 transition-colors"
          >
            <PlusIcon className="w-4 h-4" />
            <span>
              {t('addDomainButton', 'Add Domain Name')}
            </span>
          </button>
        )}
      </div>

      <div className="p-6">
        {isAddingDomain ? (
          <div className="bg-white/5 rounded-lg border border-white/10 overflow-hidden">
            <div className="p-4 border-b border-white/10">
              <h3 className="text-white font-medium">
                {t('connectCustomDomainTitle', 'Connect a Custom Domain Name')}
              </h3>
            </div>

            <div className="mx-6 mt-6 bg-green-500/10 border border-green-500/20 rounded-lg p-4 text-sm text-white/80 ">
              <p>
                <span className="text-green-400 font-medium">
                  {t('disclaimer', 'Disclaimer:')}
                </span>
                {t('disclaimerText', 'In order to succesfully verify you have to correctly set all the DNS rules above')}
              </p>
            </div>
            <div className="p-6">
              {verificationStep === 0 && (
                <div className="space-y-4">
                  <p className="text-white/70 text-sm">
                    {t('domainInputDescription', 'Enter the domain name you want to connect to this project.')}
                  </p>
                  <div>
                    <label className="block text-white/70 text-sm mb-2">
                      {t('domainLabel', 'Domain Name')}
                    </label>
                    <input
                      type="text"
                      value={newDomain}
                      onChange={handleDomainInputChange}
                      onKeyDown={handleKeyDown}
                      placeholder="example.com"
                      className={clsx(
                        'w-full bg-white/5 border rounded-lg px-3 py-2 text-white focus:outline-none',
                        verificationError ? 'border-red-500' : 'border-white/10 focus:border-[#A78BFA]/30',
                      )}
                    />
                    {verificationError && <p className="text-red-500 text-xs mt-1">{verificationError}</p>}
                  </div>

                  <div className="flex items-center justify-end gap-3 mt-6">
                    <button
                      onClick={handleCancelAddDomain}
                      className="px-4 py-2 bg-white/5 text-white/80 rounded-lg hover:bg-white/10 transition-colors"
                    >
                      {t('cancelButton', 'Cancel')}
                    </button>
                    <button
                      onClick={handleContinueToVerification}
                      disabled={!newDomain.trim()}
                      className={clsx(
                        'px-4 py-2 rounded-lg transition-colors',
                        newDomain.trim()
                          ? 'bg-[#A78BFA] text-black hover:bg-[#A78BFA]/90'
                          : 'bg-white/5 text-white/40 cursor-not-allowed',
                      )}
                    >
                      {isDeploying ? (
                        <motion.div
                          className="flex items-center gap-2"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <ArrowPathIcon className="w-4 h-4 animate-spin" />
                          <span className="font-medium">
                            {t('deployingText', 'Deploying...')}
                          </span>
                        </motion.div>
                      ) : verificationStatus === 'verifying' ? (
                        <motion.div
                          className="flex items-center gap-2"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: [0, 1, 0.7, 1] }}
                          transition={{ duration: 0.6, repeat: Infinity }}
                        >
                          <ArrowPathIcon className="w-4 h-4 animate-spin" />
                          <span className="font-medium">
                            {t('addingText', 'Adding...')}
                          </span>
                        </motion.div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <CheckCircleIcon className="w-4 h-4" />
                          <span className="font-medium">
                            {t('continueButton', 'Add Domain Name')}
                          </span>
                        </div>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {verificationStep === 1 && (
                <div className="space-y-6">
                  <div>
                    <h4 className="text-white font-medium mb-2">
                      {t('configureDnsTitle', 'Configure DNS Records')}
                    </h4>
                    <p className="text-white/70 text-sm mb-4">
                      {t('configureDnsDescription', 'Add the following DNS records to your domain to verify ownership and connect it to this project.')}
                    </p>

                    <div className="bg-[#0A0F1C] rounded-lg p-4 border border-white/10 mb-4">
                      <div className="space-y-4">
                        {/* Header */}
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-white/70 text-xs w-16">
                              {t('tableHeaderType', 'Type')}
                            </span>
                            <span className="text-white/70 text-xs w-40">
                              {t('tableHeaderName', 'Name')}
                            </span>
                            <span className="text-white/70 text-xs flex-1">
                              {t('tableHeaderValue', 'Value')}
                            </span>
                            <span className="text-white/70 text-xs w-16">TTL</span>
                          </div>

                          {/* A Record */}
                          {!isSubdomain(newDomain) && (
                            <div className="flex items-center justify-between bg-white/5 p-2 rounded">
                              <span className="text-white font-mono text-sm w-16">A</span>
                              <span className="text-white font-mono text-sm w-40">@</span>
                              <span className="text-white font-mono text-sm flex-1">76.76.21.21</span>
                              <span className="text-white font-mono text-sm w-16">3600</span>
                            </div>
                          )}
                        </div>

                        {/* CNAME Record */}
                        <div>
                          <div className="flex items-center justify-between bg-white/5 p-2 rounded">
                            <span className="text-white font-mono text-sm w-16">CNAME</span>
                            <span className="text-white font-mono text-sm w-40">
                              {isSubdomain(newDomain) ? newDomain.split('.')[0] : 'www'}
                            </span>
                            <span className="text-white font-mono text-sm flex-1">cname.vercel-dns.com</span>
                            <span className="text-white font-mono text-sm w-16">3600</span>
                          </div>
                        </div>

                        {/* TXT Record - only if exists */}
                        {matchedDomain?.verifications?.some((v) => v.type === 'TXT') && (
                          <div>
                            <div className="flex items-center justify-between bg-white/5 p-2 rounded">
                              <span className="text-white font-mono text-sm w-16">TXT</span>
                              <span className="text-white font-mono text-sm w-40">
                                {matchedDomain.verifications.find((v) => v.type === 'TXT')?.domain}
                              </span>
                              <span className="text-white font-mono text-sm flex-1">
                                {matchedDomain.verifications.find((v) => v.type === 'TXT')?.value}
                              </span>
                              <span className="text-white font-mono text-sm w-16">3600</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="bg-[#A78BFA]/10 border border-[#A78BFA]/20 rounded-lg p-4 text-sm text-white/80">
                      <p>
                        <span className="text-[#A78BFA] font-medium">
                          {t('note', 'Note:')}
                        </span>
                        {t('noteText', 'DNS changes may take up to 48 hours to propagate. However, they often take effect within a few minutes to a few hours.')}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center justify-end gap-3">
                    <button
                      onClick={() => setVerificationStep(0)}
                      className="px-4 py-2 bg-white/5 text-white/80 rounded-lg hover:bg-white/10 transition-colors"
                    >
                      {t('backButton', 'Back')}
                    </button>
                    <button
                      onClick={handleFinalVerifyDomain}
                      className={clsx(
                        'px-4 py-2 rounded-lg transition-colors flex items-center gap-2',
                        verificationStatus === 'verifying' || isDeploying
                          ? 'bg-[#A78BFA]/70 text-black cursor-not-allowed'
                          : 'bg-[#A78BFA] text-black hover:bg-[#A78BFA]/90',
                      )}
                      disabled={verificationStatus === 'verifying' || isDeploying}
                    >
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="w-4 h-4" />
                        <span className="font-medium">
                          {t('verifyButtonText', 'Verify')}
                        </span>
                      </div>
                    </button>
                  </div>

                  {verificationStatus === 'error' && (
                    <div className="mt-4 bg-red-500/10 border border-red-500/20 rounded-lg p-4 text-sm text-red-400">
                      <div className="flex items-start gap-2">
                        <XCircleIcon className="w-5 h-5 flex-shrink-0 mt-0.5" />
                        <p>{verificationError}</p>
                      </div>
                    </div>
                  )}

                  {verificationStatus === 'success' && (
                    <div
                      className="absolute bottom-7 right-0 mt-4 bg-[#4ADE80]/10 border border-[#4ADE80]/20 rounded-lg p-4 text-sm text-[#4ADE80]">
{' '}
                      <div className="flex items-start gap-2">
                        <CheckCircleIcon className="w-5 h-5 flex-shrink-0 mt-0.5" />
                        <p>
                          {t('successAdd', 'Domain name added successfully! Your domain has been added to this project.')}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="bg-[#A78BFA]/5 border border-[#A78BFA]/10 rounded-lg p-4 mb-6">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-[#A78BFA]/10 rounded-lg">
                  <GlobeAltIcon className="w-5 h-5 text-[#A78BFA]" />
                </div>
                <div>
                  <h3 className="text-white font-medium mb-1">
                    {t('customConfigTitle', 'Custom Domain Configuration')}
                  </h3>
                  <p className="text-white/70 text-sm">
                    {t('customConfigDescription', 'Connect your own domain names to this project. Your project will always remain accessible via the default Biela domain, but custom domains provide a professional branded experience for your users.')}
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {domains?.map((domain) => (
                <div
                  key={domain?.id}
                  className={clsx(
                    'bg-white/5 rounded-lg p-4 border',
                    domain?.isDefault ? 'border-[#4ADE80]/20' : 'border-white/10',
                  )}
                >
                  <div className="flex items-center justify-between flex-col xl:flex-row">
                    <div className="flex items-center gap-3 mb-4 w-full xl:w-auto xl:mb-0">
                      <div
                        className={clsx(
                          'w-10 h-10 rounded-lg flex items-center justify-center',
                          domain?.type === 'system' ? 'bg-[#A78BFA]/10' : 'bg-[#A78BFA]/10',
                        )}
                      >
                        <GlobeAltIcon
                          className={clsx('w-5 h-5', domain?.type === 'system' ? 'text-[#A78BFA]' : 'text-[#A78BFA]')}
                        />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-white font-medium">{domain?.name}</h3>
                          {domain?.isDefault && (
                            <span className="px-2 py-0.5 bg-[#4ADE80]/10 text-[#4ADE80] text-xs rounded-full">
                              {t('defaultLabel', 'Default')}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <span
                            className={clsx(
                              'w-2 h-2 rounded-full',
                              domain?.verified === true ? 'bg-[#4ADE80]' : 'bg-yellow-500',
                            )}
                          ></span>
                          <span className="text-white/50">
                            {t(domain?.verified === true ? 'statusActive' : 'statusActive')} • {t('lastVerifiedText', 'Last verified just now')}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col self-end w-full sm:w-auto sm:flex-row md:items-center gap-2">
                      <button
                        onClick={() => handleRefresh(domain)}
                        disabled={loading}
                        className="flex items-center justify-center w-full sm:w-auto gap-1 px-3 py-1.5 bg-[#A78BFA]/10 text-[#A78BFA] rounded-lg hover:bg-[#A78BFA]/20 transition-colors text-sm"
                      >
                        {loading ? (
                          <span>Refreshing...</span>
                        ) : (
                          <>
                            <ArrowPathIcon className="w-4 h-4" />
                            <span>Refresh</span>
                          </>
                        )}
                      </button>

                      <button
                        onClick={() => handleClickDNS(domain)}
                        className="flex items-center justify-center w-full sm:w-auto gap-1 px-3 py-1.5 bg-[#A78BFA]/10 text-[#A78BFA] rounded-lg hover:bg-[#A78BFA]/20 transition-colors text-sm"
                      >
                        {expandedDNSDomainId === domain.id
                          ? t('hideDnsButton', 'Hide DNS Settings')
                          : t('showDnsButton', 'Show DNS Settings')}
                      </button>
                      {domain?.type !== 'system' && (
                        <button
                          onClick={() => handleRemoveDomainClick(domain)}
                          className="flex items-center justify-center w-full sm:w-auto gap-1 px-3 py-1.5 bg-red-500/10 text-red-400 rounded-lg hover:bg-red-500/20 transition-colors text-sm"
                        >
                          <TrashIcon className="w-4 h-4" />
                          <span>
                            {t('removeButton', 'Remove')}
                          </span>
                        </button>
                      )}
                    </div>
                  </div>

                  {/* DNS Settings */}
                  {expandedDNSDomainId === domain.id && (
                    <div className="mt-4 bg-[#0A0F1C] rounded-lg p-4 border border-white/10 mb-4">
                      <h4 className="text-white text-base font-semibold mb-4">
                        {t('dnsSettingsTitle', 'Domain DNS Settings')}
                      </h4>
                      <div className="space-y-4">
                        {/* Header */}
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-white/70 text-xs w-16">
                            {t('tableHeaderType', 'Type')}
                          </span>
                          <span className="text-white/70 text-xs w-40">Name
                            {t('tableHeaderName', 'Name')}
                          </span>
                          <span className="text-white/70 text-xs flex-1">
                            {t('tableHeaderValue', 'Value')}
                          </span>
                          <span className="text-white/70 text-xs w-16">TTL</span>
                        </div>

                        {/* A Record */}
                        {!isSubdomain(domain?.name) && (
                          <div className="flex items-center justify-between bg-white/5 p-2 rounded">
                            <span className="text-white font-mono text-sm w-16">A</span>
                            <span className="text-white font-mono text-sm w-40">@</span>
                            <span className="text-white font-mono text-sm flex-1">76.76.21.21</span>
                            <span className="text-white font-mono text-sm w-16">3600</span>
                          </div>
                        )}

                        {/* CNAME Record */}
                        <div className="flex items-center justify-between bg-white/5 p-2 rounded">
                          <span className="text-white font-mono text-sm w-16">CNAME</span>
                          <span className="text-white font-mono text-sm w-40">
                            {isSubdomain(domain?.name) ? domain?.name.split('.')[0] : 'www'}
                          </span>
                          <span className="text-white font-mono text-sm flex-1">cname.vercel-dns.com</span>
                          <span className="text-white font-mono text-sm w-16">3600</span>
                        </div>

                        {/* TXT Record – only show if available */}
                        {domain?.verifications?.some((v) => v.type === 'TXT') && (
                          <div className="flex items-center justify-between bg-white/5 p-2 rounded">
                            <span className="text-white font-mono text-sm w-16">TXT</span>
                            <span className="text-white font-mono text-sm w-40">
                              {domain.verifications.find((v) => v.type === 'TXT')?.domain}
                            </span>
                            <span className="text-white font-mono text-sm flex-1">
                              {domain.verifications.find((v) => v.type === 'TXT')?.value}
                            </span>
                            <span className="text-white font-mono text-sm w-16">3600</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Remove Domain Confirmation */}
                  {expandedRemoveDomainId === domain.id && (
                    <div className="mt-4 bg-[#1A1F2E] rounded-lg p-4 border border-red-400/20 mb-4">
                      <h4 className="text-red-400 text-base font-semibold mb-4 flex items-center gap-2">
                        <ExclamationTriangleIcon className="w-5 h-5 text-amber-500" />
                        {t('removeDomainConfirmTitle', 'Remove Domain Name')}
                      </h4>
                      <p className="text-white/80 mb-4">
                        {t('removeConfirmationText', 'Are you sure you want to remove the domain name{\' \'}')}
                        <span className="font-semibold text-white">{domain.name}</span>?
                      </p>
                      <div className="bg-amber-500/10 border border-amber-500/20 rounded-lg p-4 mb-4">
                        <div className="flex items-start gap-2">
                          <ExclamationTriangleIcon className="w-5 h-5 text-amber-500 flex-shrink-0 mt-0.5" />
                          <div>
                            <p className="text-amber-500 font-medium mb-1">
                              {t('importantCleanupTitle', 'Important DNS Cleanup')}
                            </p>
                            <p className="text-white/70 text-sm">
                              {t('cleanupDescription', 'After removing this domain name from your project, remember to also remove the DNS records you created during setup. This helps maintain a clean DNS configuration and prevents potential conflicts in the future.')}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center flex-col xl:flex-row justify-end gap-3">
                        <button
                          onClick={handleCancelRemoveDomain}
                          className="px-4 py-2 w-full lx:w-auto bg-white/5 text-white/80 rounded-lg hover:bg-white/10 transition-colors"
                        >
                          {t('abortButton', 'Cancel')}
                        </button>
                        <button
                          onClick={handleConfirmRemoveDomain}
                          className="px-4 py-2 w-full lx:w-auto rounded-lg bg-red-500 text-white hover:bg-red-600 transition-colors font-semibold"
                        >
                          {t('removeDomainConfirmTitle','Remove Domain Name')}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="bg-white/5 rounded-lg p-4 border border-white/10">
              <h3 className="text-white font-medium mb-3">
                {t('benefitsTitle','Domain Benefits')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-[#A78BFA]/10 rounded-lg">
                    <svg className="w-5 h-5 text-[#A78BFA]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white text-sm font-medium mb-1">
                      {t('benefitSecurityTitle','Enhanced Security')}
                    </h4>
                    <p className="text-white/50 text-xs">
                      {t('benefitSecurityDesc','All custom domains are automatically secured with SSL certificates.')}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="p-2 bg-[#A78BFA]/10 rounded-lg">
                    <svg className="w-5 h-5 text-[#A78BFA]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white text-sm font-medium mb-1">
                      {t('benefitPerformanceTitle','Fast Performance')}
                    </h4>
                    <p className="text-white/50 text-xs">
                      {t('benefitPerformanceDesc','Global CDN ensures your project loads quickly for users worldwide.')}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="p-2 bg-[#A78BFA]/10 rounded-lg">
                    <svg className="w-5 h-5 text-[#A78BFA]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white text-sm font-medium mb-1">
                      {t('benefitBrandingTitle', 'Professional Branding')}
                    </h4>
                    <p className="text-white/50 text-xs">
                      {t('benefitBrandingDesc', ' Use your own domain for a consistent brand experience.')}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="p-2 bg-[#A78BFA]/10 rounded-lg">
                    <svg className="w-5 h-5 text-[#A78BFA]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white text-sm font-medium mb-1">
                      {t('benefitAnalyticsTitle', 'Analytics Integration')}
                    </h4>
                    <p className="text-white/50 text-xs">
                      {t('benefitAnalyticsDesc', 'Custom domains work seamlessly with analytics platforms.')}
                      </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default DomainSection;
