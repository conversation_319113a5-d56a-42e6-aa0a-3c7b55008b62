import { useStore } from '@nanostores/react';
import React, { memo, useEffect, useRef, useState } from 'react';
import { type ImperativePanelHandle, Panel } from 'react-resizable-panels';
import { Terminal } from './Terminal';

import { PlayIcon, X } from 'lucide-react';
import { createScopedLogger } from '~/utils/logger';
import { themeStore } from '~/ai/lib/stores/theme';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { shortcutEventEmitter } from '~/ai/lib/hooks';
import { classNames } from '~/utils/classNames';
import { IconButton } from '~/components/common/IconButton';
import { ArrowDownTrayIcon } from '@heroicons/react/16/solid';
import { FaFlask, FaTerminal } from 'react-icons/fa';
import { RocketLaunchIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { Tooltip } from '~/backOffice/components/dashboard/Tooltip';

const logger = createScopedLogger('Terminal');

const MAX_TERMINALS = 5;
export const DEFAULT_TERMINAL_SIZE = 18;

export const TerminalTabs = memo(() => {
  const theme = useStore(themeStore);
  const showTerminal = useStore(workbenchStore.showTerminal);
  const { t } = useTranslation('translation');
  const startBtnRef = useRef<HTMLButtonElement>(null);
  const [showStartTip, setShowStartTip] = useState(false);

  const terminalPanelRef = useRef<ImperativePanelHandle>(null);
  const terminalToggledByShortcut = useRef(false);

  const installBtnRef = useRef<HTMLButtonElement>(null);
  const [showInstallTip, setShowInstallTip] = useState(false);

  const buildBtnRef = useRef<HTMLButtonElement>(null);
  const [showBuildTip, setShowBuildTip] = useState(false);

  // Initialize with one terminal
  const [terminals, setTerminals] = useState([
    {
      id: Date.now().toString(36) + Math.random().toString(36).substring(2),
    },
    {
      id: Date.now().toString(36) + Math.random().toString(36).substring(2),
    },
  ]);
  const [activeTerminal, setActiveTerminal] = useState(() => terminals[0].id);
  const [terminalCount, setTerminalCount] = useState(1);

  const addTerminal = () => {
    if (terminalCount < MAX_TERMINALS) {
      const newTerminal = {
        id: Date.now().toString(36) + Math.random().toString(36).substring(2),
      };
      setTerminals((prev) => [...prev, newTerminal]);
      setTerminalCount((prev) => prev + 1);
      setActiveTerminal(newTerminal.id);
    }
  };

  const deleteTerminal = (terminalId: string, event: React.MouseEvent) => {
    // Stop the click from propagating to the terminal button
    event.stopPropagation();

    const terminalsCopy = terminals.filter((terminal) => terminal.id !== terminalId);

    // If we're deleting the active terminal or if there will be no active terminal
    if (terminalId === activeTerminal || !terminalsCopy.find((t) => t.id === activeTerminal)) {
      // Find the index of the terminal being deleted
      const deletedIndex = terminals.findIndex((t) => t.id === terminalId);

      /*
       * If we're deleting the last terminal, activate the previous one
       * Otherwise activate the next terminal
       */
      const newActiveIndex =
        deletedIndex === terminals.length - 1
          ? Math.max(0, deletedIndex - 1)
          : Math.min(deletedIndex, terminalsCopy.length - 1);

      const newActiveId = terminalsCopy[newActiveIndex]?.id;

      // Set the new active terminal before updating the terminals list
      setActiveTerminal(newActiveId);
    }

    setTerminals(terminalsCopy);
    setTerminalCount((prev) => prev - 1);
  };

  useEffect(() => {
    const { current: terminal } = terminalPanelRef;

    if (!terminal) {
      return;
    }

    const isCollapsed = terminal.isCollapsed();

    if (!showTerminal && !isCollapsed) {
      terminal.collapse();
    } else if (showTerminal && isCollapsed) {
      terminal.resize(DEFAULT_TERMINAL_SIZE);
    }

    terminalToggledByShortcut.current = false;
  }, [showTerminal]);

  useEffect(() => {
    const unsubscribeFromEventEmitter = shortcutEventEmitter.on('toggleTerminal', () => {
      terminalToggledByShortcut.current = true;
    });

    const unsubscribeFromThemeStore = themeStore.subscribe(() => {});

    return () => {
      unsubscribeFromEventEmitter();
      unsubscribeFromThemeStore();
    };
  }, []);

  return (
    <Panel
      ref={terminalPanelRef}
      defaultSize={showTerminal ? DEFAULT_TERMINAL_SIZE : 0}
      minSize={10}
      className="overflow-visible"
      collapsible
      onExpand={() => {
        if (!terminalToggledByShortcut.current) {
          workbenchStore.toggleTerminal(true);
        }
      }}
      onCollapse={() => {
        if (!terminalToggledByShortcut.current) {
          workbenchStore.toggleTerminal(false);
        }
      }}
    >
      <div className="h-full overflow-visible">
        <div className="z-10 bg-biela-elements-terminals-background h-full flex flex-col terminal-pills">
          <div className="flex items-center bg-biela-elements-bg-depth-5 border-y border-biela-elements-borderColor min-h-[34px] overflow-x-auto overflow-y-hidden">
            {terminals.map((terminal, index) => {
              const isActive = activeTerminal === terminal.id;
              return (
                <React.Fragment key={index}>
                  {index == 0 || index == 1 ? (
                    <button
                      key={terminal.id}
                      className={classNames(
                        'flex items-center gap-2 px-4 py-2 border-r border-white/5 !text-white/70 cursor-pointer !text-[15px] !font-normal !tracking-[0.4px]',
                        {
                          'bg-biela-elements-terminals-buttonBackground ': isActive,
                          'bg-transparent hover:bg-white/5': !isActive,
                        },
                      )}
                      onClick={() => {
                        setActiveTerminal(terminal.id);

                        if (index === 1) {
                          workbenchStore.actionAlert.set({
                            type: 'unitTesting',
                            title: 'Suggestion',
                            description: 'Would you like to implement Unit Tests for your project???',
                            content: 'Content',
                            source: 'preview',
                          });
                        }
                      }}
                    >
                      {index == 0 ? (
                        <>
                          <FaTerminal className={'w-4 h-4'} />
                          <span className="text-[12px] xl:text-[14px] font-manrope  whitespace-nowrap">
                            {t('BielaTerminal', 'Biela Terminal')}
                          </span>
                        </>
                      ) : (
                        <>
                          <FaFlask className={'w-4 h-4'} />
                          <span className="text-[12px] xl:text-[14px] font-manrope  whitespace-nowrap">
                            {t('UnitTesting', 'Unit Testing')}
                          </span>
                        </>
                      )}
                    </button>
                  ) : (
                    <React.Fragment>
                      <button
                        key={terminal.id}
                        className={classNames(
                          'flex items-center gap-2 px-4 py-2 border-r border-white/5 !text-white/70 cursor-pointer !text-[15px] !font-normal !tracking-[0.4px]',
                          {
                            'bg-biela-elements-terminals-buttonBackground ': isActive,
                            'bg-transparent hover:bg-white/5': !isActive,
                          },
                        )}
                        onClick={() => setActiveTerminal(terminal.id)}
                      >
                        <img src="/icons/terminal.svg" alt="Toggle Device Mode" />
                        Terminal {terminalCount > 1 && index - 1}
                        <X
                          color="#FF5656"
                          className=" cursor-pointer"
                          size={16}
                          onClick={(e) => deleteTerminal(terminal.id, e)}
                        />
                      </button>
                    </React.Fragment>
                  )}
                </React.Fragment>
              );
            })}
            {terminalCount < MAX_TERMINALS && (
              <IconButton className="min-w-6" icon="plus" size="sm" onClick={addTerminal} />
            )}
            <div className="flex gap-2 ms-auto">
              <div className={'relative group'}>
                <button
                  ref={installBtnRef}
                  onMouseEnter={() => setShowInstallTip(true)}
                  onMouseLeave={() => setShowInstallTip(false)}
                  onClick={() => workbenchStore.installDependenciesBielaTerminalServer()}
                  className={
                    'flex items-center gap-2 px-3 py-2 bg-white/0 hover:bg-white/5 transition-colors !text-white/70 cursor-pointer !text-[15px] !font-normal !tracking-[0.4px]'
                  }
                >
                  <ArrowDownTrayIcon className="min-w-4 min-h-4 max-w-4 max-h-4" />
                  <span className="text-[12px] xl:text-[14px] font-manrope font-light whitespace-nowrap">
                    {t('InstallDependencies', 'Install Dependencies')}
                  </span>
                </button>
                <div className="relative">
                  <Tooltip anchor={installBtnRef} open={showInstallTip} widthClass="w-64">
                    <div className="text-sm text-white/90 mb-2">
                      {t('InstallDependencies', 'Install Dependencies')}
                    </div>
                    <div className="text-xs text-white/50 mb-3">
                      {t('InstallDependenciesDescription', 'Installs all required packages for the project using')}&nbsp; <code>npm install</code>.
                    </div>
                    <div className="flex items-center gap-2 bg-white/5 rounded px-2 py-1 font-mono text-xs">
                      <span className="text-white/30">❯</span>
                      <span className="text-white/70">npm install</span>
                    </div>
                  </Tooltip>
                </div>
              </div>
              <div className={'relative group'}>
                <button
                  ref={buildBtnRef}
                  onMouseEnter={() => setShowBuildTip(true)}
                  onMouseLeave={() => setShowBuildTip(false)}
                  onClick={() => workbenchStore.buildProjectBielaTerminalServer()}
                  className={
                    'flex items-center gap-2 px-3 py-2 bg-white/0 hover:bg-white/5 transition-colors !text-white/70 cursor-pointer !text-[15px] !font-normal !tracking-[0.4px]'
                  }
                >
                  <RocketLaunchIcon className="min-w-4 min-h-4 max-w-4 max-h-4" />
                  <span className="text-[12px] xl:text-[14px] font-manrope font-light whitespace-nowrap">
                    {t('BuildProject', 'Build Project')}
                  </span>
                </button>

                <div className="relative">
                  <Tooltip anchor={buildBtnRef} open={showBuildTip} widthClass="w-64">
                    <div className="text-sm text-white/90 mb-2">{t('BuildProject', 'Build Project')}</div>
                    <div className="text-xs text-white/50 mb-3">
                      {t('BuildProjectDescription', 'Compiles and optimizes the project for production using')} &nbsp;<code>npm run build</code>.
                    </div>
                    <div className="flex items-center gap-2 bg-white/5 rounded px-2 py-1 font-mono text-xs">
                      <span className="text-white/30">❯</span>
                      <span className="text-white/70">npm run build</span>
                    </div>
                  </Tooltip>
                </div>
              </div>
              <div className={'relative group'}>
                <button
                  ref={startBtnRef}
                  onMouseEnter={() => setShowStartTip(true)}
                  onMouseLeave={() => setShowStartTip(false)}
                  onClick={() => workbenchStore.startProjectBielaTerminalServer()}
                  className={
                    'flex items-center gap-2 px-3 py-2 bg-white/0 hover:bg-white/5 transition-colors !text-white/70 cursor-pointer !text-[15px] !font-normal !tracking-[0.4px]'
                  }
                >
                  <PlayIcon className="min-w-4 min-h-4 max-w-4 max-h-4" />
                  <span className="text-[12px] xl:text-[14px] font-manrope font-light whitespace-nowrap">
                    {t('StartDevelopment', 'Start Development')}
                  </span>
                </button>

                <div className="relative">
                  <Tooltip anchor={startBtnRef} open={showStartTip} widthClass="w-64">
                    <div className="text-sm text-white/90 mb-2">{t('StartDevelopment', 'Start Development')}</div>
                    <div className="text-xs text-white/50 mb-3">
                      {t('StartDevelopmentDescription', 'Launches the development server for live preview using')} &nbsp;<code>npm run dev</code>.
                    </div>
                    <div className="flex items-center gap-2 bg-white/5 rounded px-2 py-1 font-mono text-xs">
                      <span className="text-white/30">❯</span>
                      <span className="text-white/70">npm run dev</span>
                    </div>
                  </Tooltip>
                </div>
              </div>
              {/*<button onClick={() => workbenchStore.stopBielaTerminalServer()} title="Stop Server" className='bg-transparent border-none'>*/}
              {/*  <Pause size={16} color="#ffffff" />*/}
              {/*</button>*/}
              {/*<button onClick={() => workbenchStore.restartBielaTerminalServer()} title="Restart Server" className='bg-transparent border-none'>*/}
              {/*  <RefreshCcw size={16} color="#ffffff" />*/}
              {/*</button>*/}
            </div>
          </div>
          {terminals.map((terminal, index) => {
            const isActive = activeTerminal === terminal.id;

            logger.debug(`Starting terminal [${terminal.id}]`);

            if (index === 0) {
              return (
                <Terminal
                  key={terminal.id}
                  id={`terminal_${terminal.id}`}
                  className={classNames('h-full overflow-hidden', {
                    hidden: !isActive
                  })}
                  onTerminalReady={(terminal) => {
                    workbenchStore.attachbielaTerminal(terminal);
                  }}
                  onTerminalResize={(cols, rows) => workbenchStore.onTerminalResize(cols, rows)}
                  theme={theme}
                />
              );
            } else if (index === 1) {
              return (
                <Terminal
                  key={terminal.id}
                  id={`terminal_${terminal.id}`}
                  className={classNames('h-full overflow-hidden', {
                    hidden: !isActive,
                  })}
                  onTerminalReady={(terminal) => workbenchStore.attachUnitTestingTerminal(terminal)}
                  onTerminalResize={(cols, rows) => workbenchStore.onTerminalResize(cols, rows)}
                  theme={theme}
                />
              );
            } else {
              return (
                <Terminal
                  key={terminal.id}
                  id={`terminal_${terminal.id}`}
                  className={classNames('h-full overflow-hidden', {
                    hidden: !isActive,
                  })}
                  onTerminalReady={(terminal) => workbenchStore.attachTerminal(terminal)}
                  onTerminalResize={(cols, rows) => workbenchStore.onTerminalResize(cols, rows)}
                  theme={theme}
                />
              );
            }
          })}
        </div>
      </div>
    </Panel>
  );
});
