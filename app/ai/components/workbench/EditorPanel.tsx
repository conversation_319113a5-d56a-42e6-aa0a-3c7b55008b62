import { useStore } from '@nanostores/react';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { ImperativePanelHandle } from 'react-resizable-panels';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';

import {
  CodeMirrorEditor,
  type EditorSettings,
  type OnChangeCallback as OnEditorChange,
  type OnScrollCallback as OnEditorScroll
} from '../../components/CodeMirrorEditor';
import { PanelHeader } from '~/ai/components/PanelHeader';
import { PanelHeaderButton } from '~/ai/components/PanelHeaderButton';
import { WORK_DIR } from '~/utils/constants';
import { renderLogger } from '~/utils/logger';
import { isMobile } from '~/utils/mobile';
import { FileBreadcrumb } from './FileBreadcrumb';
import { FileTree } from './FileTree';
import { DEFAULT_TERMINAL_SIZE, TerminalTabs } from './terminal/TerminalTabs';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { toast } from 'react-toastify';
import { chatStore, setIsModifiedCode } from '~/ai/lib/stores/chat';
import { FolderIcon } from '@heroicons/react/24/outline';
import { FaHistory, FaRegSave } from 'react-icons/fa';

const DEFAULT_EDITOR_SIZE = 100 - DEFAULT_TERMINAL_SIZE;
const editorSettings: EditorSettings = { tabSize: 2 };

export const EditorPanel = memo(() => {
  renderLogger.trace('EditorPanel');

  const fileTreePanelRef = useRef<ImperativePanelHandle>(null);
  const editorPanelRef   = useRef<ImperativePanelHandle>(null);

  const showTerminal    = useStore(workbenchStore.showTerminal);
  const unsavedFiles    = useStore(workbenchStore.unsavedFiles);
  const editorDocument  = useStore(workbenchStore.currentDocument);
  const selectedFile    = useStore(workbenchStore.selectedFile);
  const { isStreaming } = chatStore.get();

  const lastSelectedFileRef = useRef<string | undefined>();
  const [editable, setEditable] = useState(!isStreaming && !!editorDocument);

  const resetHorizontal = useCallback(() => {
    fileTreePanelRef.current?.resize(20);
    editorPanelRef.current?.resize(80);
  }, []);

  const activeFileSegments = useMemo(() => {
    return editorDocument?.filePath.split('/') ?? undefined;
  }, [editorDocument]);

  const onEditorChange = useCallback<OnEditorChange>(
    update => {
      if (isStreaming) return;
      workbenchStore.setCurrentDocumentContent(update.content);
    },
    [isStreaming],
  );

  const onEditorScroll = useCallback<OnEditorScroll>(
    position => {
      workbenchStore.setCurrentDocumentScrollPosition(position);
    },
    [],
  );

  const onFileSelect = useCallback((filePath?: string) => {
    workbenchStore.setSelectedFile(filePath, true);
  }, []);

  const onFileSave = useCallback(() => {
    if (isStreaming) return;
    setIsModifiedCode(true);
    workbenchStore.saveCurrentDocument().catch(() => {
      toast.error('Failed to update file content');
    });
  }, [isStreaming]);

  const onFileReset = useCallback(() => {
    if (isStreaming) return;
    workbenchStore.resetCurrentDocument();
  }, [isStreaming]);

  const activeFileUnsaved = useMemo(
    () => editorDocument != null && unsavedFiles?.has(editorDocument.filePath),
    [editorDocument, unsavedFiles],
  );

  useEffect(() => {
    if (isStreaming) {
      lastSelectedFileRef.current = selectedFile;
    }
  }, [isStreaming, selectedFile]);

  useEffect(() => {
    setEditable(!isStreaming && !!editorDocument);
  }, [isStreaming, editorDocument]);

  const handleFileSelect = useCallback(
    (filePath?: string) => {

      if (!isStreaming) {
        document.querySelectorAll('.cm-scroller').forEach(el => {
          (el as HTMLElement).scrollTop = 0;
        });
      }

      onFileSelect(filePath);
    },
    [isStreaming, onFileSelect],
  );

  return (
    <PanelGroup direction="vertical">
      <Panel defaultSize={showTerminal ? DEFAULT_EDITOR_SIZE : 100} minSize={20}>
        <PanelGroup direction="horizontal">
          <Panel
            ref={fileTreePanelRef}
            defaultSize={20}
            minSize={10}
            collapsible
          >
            <div className="flex flex-col border-r border-biela-elements-borderColor h-full editor-fonts">
              <PanelHeader className="flex items-center !gap-2 bg-transparent !border-0">
                <FolderIcon className="w-4 h-4" />
                <span className="text-sm text-white/90 font-normal tracking-[0.4px]">
                  Files
                </span>
              </PanelHeader>
              <FileTree
                className="h-full"
                hideRoot
                rootFolder={WORK_DIR}
                selectedFile={selectedFile}
                onFileSelect={handleFileSelect}
              />
            </div>
          </Panel>

          <PanelResizeHandle
            className="cursor-pointer"
            onDoubleClick={resetHorizontal}
          />

          <Panel
            ref={editorPanelRef}
            className="flex flex-col p-4"
            defaultSize={80}
            minSize={20}
          >
            <PanelHeader className="overflow-x-auto rounded-t-lg flex items-center justify-between px-4 py-2 border-b border-white/10">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              {activeFileSegments?.length && (
                <div className="flex items-center flex-1 text-sm h-[28px]">
                  <FileBreadcrumb pathSegments={activeFileSegments} onFileSelect={onFileSelect} />
                  {activeFileUnsaved && (
                    <div className="flex gap-1 ml-auto -mr-1.5">
                      <PanelHeaderButton onClick={onFileSave}>
                        <FaRegSave size={15} />
                        Save
                      </PanelHeaderButton>
                      <PanelHeaderButton onClick={onFileReset}>
                        <FaHistory size={15}/>
                        Reset
                      </PanelHeaderButton>
                    </div>
                  )}
                </div>
              )}
            </PanelHeader>

            <div className="h-full flex-1 overflow-hidden rounded-b-lg">
              <CodeMirrorEditor
                editable={editable}
                settings={editorSettings}
                doc={editorDocument}
                autoFocusOnDocumentChange={!isMobile()}
                onScroll={onEditorScroll}
                onChange={onEditorChange}
                onSave={onFileSave}
              />
            </div>
          </Panel>
        </PanelGroup>
      </Panel>

      <PanelResizeHandle />

      <TerminalTabs />
    </PanelGroup>
  );
});
