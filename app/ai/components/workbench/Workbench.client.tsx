import { useStore } from '@nanostores/react';
import { type HTMLMotionProps, motion, type Variants } from 'framer-motion';
import { atom, computed } from 'nanostores';
import React, { memo, ReactElement, UIEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'react-toastify';
import { workbenchStore, type WorkbenchViewType } from '~/ai/lib/stores/workbench';
import { classNames } from '~/utils/classNames';
import { cubicEasingFn } from '~/utils/easings';
import { renderLogger } from '~/utils/logger';
import { EditorPanel } from './EditorPanel';
import { Preview } from './Preview';
import useViewport from '~/ai/lib/hooks';
import { chatId, db, useChatHistory } from '~/ai/lib/persistence/useChatHistory.client';
import { getMessages, setMessages } from '~/ai/lib/persistence/db';
import { deploy, getAppState } from '~/ai/lib/stores/cloud/cloud';
import { useNavigate } from '@remix-run/react';
import { useUser } from '~/ai/lib/context/userContext';
import ConfirmationDialog, {
  DialogConfirmationType,
  emptyConfirmationDialogHandler
} from '~/ai/components/ConfirmationDialog';
import { chatStore, setIsModifiedCode, setShowChat } from '~/ai/lib/stores/chat';
import {
  downloadProjectZipBuffer,
  getProjectCommits,
  ProjectCommits,
  setProjectCommits,
  uploadProject
} from '~/ai/lib/stores/projects/code';
import SupabaseConnectClient from '~/components/supabase/SupabaseConnect.client';
import type { JSONValue, Message } from 'ai';
import Dropdown, { DropdownItems } from '~/components/common/Dropdown';
import { ComputerDesktopIcon, DevicePhoneMobileIcon, DeviceTabletIcon } from '@heroicons/react/16/solid';
import { CodeBracketIcon, WrenchScrewdriverIcon } from '@heroicons/react/24/outline';
import { FaArrowsAltH, FaEye } from 'react-icons/fa';
import SettingsView from './SettingsView';
import { useTranslation } from 'react-i18next';

interface WorkspaceProps {
  chatStarted?: boolean;
  isStreaming?: boolean;
  sendMessage?: (event: React.UIEvent, messageInput?: string, messageOptions?: JSONValue) => void;
  messages?: Message[];
}

const viewTransition = { ease: cubicEasingFn };

const workbenchVariants = {
  closed: {
    width: 0,
    transition: {
      duration: 0.2,
      ease: cubicEasingFn,
    },
  },
  open: {
    width: 'var(--workbench-width)',
    position: 'absolute',
    transition: {
      duration: 0.2,
      ease: cubicEasingFn,
    },
  },
} satisfies Variants;

const DEVICES = [
  { label: 'Desktop', value: 100, id: 'desktop', Icon: <ComputerDesktopIcon className="w-4 h-4" /> },
  { label: 'Mobile', value: 37.5, id: 'mobile', Icon: <DevicePhoneMobileIcon className="w-4 h-4" /> },
  { label: 'Tablet', value: 76.8, id: 'tablet', Icon: <DeviceTabletIcon className="w-4 h-4" /> },
];

// @TO_BE_REMOVED after Webcontainer update
export const saveAppStore = atom<() => Promise<void>>(async () => {
  console.warn('Save app function not initialized yet');
});

export const Workbench = memo(({ chatStarted, isStreaming,sendMessage }: WorkspaceProps) => {
  renderLogger.trace('Workbench');
  const navigate = useNavigate();

  const hasPreview = useStore(computed(workbenchStore.previews, (previews) => !!previews.length));
  const showWorkbench = useStore(workbenchStore.showWorkbench);
  const selectedView = useStore(workbenchStore.currentView);
  const { showChat } = useStore(chatStore);

  const [isDeploying, setIsDeploying] = useState(false);
  const [appState, setAppState] = useState<any>(undefined);
  const [deviceMode, setDeviceMode] = useState<(typeof DEVICES)[number]>(DEVICES[0]);
  const [dialogContent, setDialogContent] = useState<{
    type: DialogConfirmationType;
    item: { description: string };
  } | null>(null);
  const [isSavingApp, setIsSavingApp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [isForkingApp, setIsForkingApp] = useState(false);
  const [commits, setCommits] = useState<ProjectCommits[] | null>(null);
  const projectSlug = workbenchStore.getSlug();
  const hasCommited = useMemo(() => !!commits?.length, [commits?.length]);
  const [commitToRestore, setCommitToRestore] = useState<DropdownItems | undefined>();
  const { duplicateCurrentChat } = useChatHistory();
  const [showSaveTooltip, setShowSaveTooltip] = useState(false);
  const isSmallViewport = useViewport(1024);

  const { isLoggedIn } = useUser();

  const setSelectedView = useCallback(
    (view: WorkbenchViewType) => {
      workbenchStore.currentView.set(view);
    },
    [isStreaming],
  );

  // @TO_BE_REMOVED after Webcontainer update
  useEffect(() => {
    saveAppStore.set(handleSaveApp);

    return () => {
      saveAppStore.set(async () => {
        console.warn('Save app function not available - Workbench unmounted');
      });
    };
  }, [commits, isStreaming, isSavingApp, projectSlug]);

  useEffect(() => {
    if (!hasPreview) {
      return;
    }

    setSelectedView('preview');
  }, [hasPreview, setSelectedView]);

  const fetchAppState = useCallback(async (cached?: boolean) => {
    const state = await getAppState(cached);

    setAppState(state);
  }, []);

  const fetchProjectCommits = useCallback(async () => {
    if (!isLoggedIn()) {
      return;
    }

    const commits = await getProjectCommits(projectSlug);

    if (!commits?.length) {
      return;
    }

    setCommits(commits);
    setCommitToRestore({
      value: commits[0].sha as string,
      label: new Date(commits[0].date as Date).toLocaleString(),
    });
  }, [projectSlug, isLoggedIn()]);

  useEffect(() => {
    if(chatStarted) {
      (async () => {
        await fetchAppState();
        await fetchProjectCommits();
      })();
    }
  }, [isStreaming]);

  useEffect(() => {
    if (isDeploying) {
      return;
    }
    if(chatStarted) {
      (async () => {
        await fetchAppState(true);
      })();
    }

  }, [isDeploying]);

  const handleFork = async () => {
    if (isForkingApp || isStreaming) {
      return;
    }

    setIsForkingApp(true);

    try {
      if (!db || !chatId.get()) {
        toast.error('Chat persistence is not available');
        return;
      }

      await duplicateCurrentChat(projectSlug);
    } catch (error) {
      toast.error('Failed to fork chat: ' + (error as Error).message);
    } finally {
      setIsForkingApp(false);
    }
  };

  const handleSaveApp = async () => {
    if (isSavingApp || isStreaming) {
      return;
    }

    setIsSavingApp(true);

    try {
      const uploadedCommits = await uploadProject();

      if (!uploadedCommits?.length) {
        return;
      }

      const chat = await getMessages(db!, chatId.value!);

      if (commits?.length) {
        for (const comm of uploadedCommits) {
          const existing = commits.find(({ sha }) => comm.sha === sha);

          //Keep the same message history for the previous commits
          if (existing) {
            comm.messages = existing.messages;

            continue;
          }

          //Assign the current messages for the new commits
          comm.messages = chat.messages;
        }
      } else {
        //It means it's the first commit
        uploadedCommits[0].messages = chat.messages;
      }

      setProjectCommits(projectSlug, uploadedCommits);
      setCommits(uploadedCommits);
      setCommitToRestore({
        label: new Date(uploadedCommits[0].date).toLocaleString(),
        value: uploadedCommits[0].sha,
      });
    } catch (error) {
      console.error('Failed saving app:', error);
    } finally {
      setIsSavingApp(false);
    }
  };

  const handleSaveClick = async (e) => {
    e.preventDefault();
    await handleSaveApp();
    setShowSaveTooltip(true);
    setTimeout(() => setShowSaveTooltip(false), 2000);
  };

  const handleForkClick = (event: UIEvent) => {
    event.preventDefault();
    setDialogContent({ type: DialogConfirmationType.FORK, item: { description: 'this chat' } });
  };

  const handleRestoreAppClick = async (event: UIEvent) => {
    event.preventDefault();

    if (!isLoggedIn()) {
      goToSignIn();

      return;
    }

    setDialogContent({ type: DialogConfirmationType.RESTORE, item: { description: 'a previous app version' } });
  };



  const handleRestoreApp = async () => {
    if (isRestoring || isStreaming || !commitToRestore) {
      return;
    }

    setIsRestoring(true);

    try {
      const zipArrayBuffer = await downloadProjectZipBuffer(commitToRestore.value as string);
      await workbenchStore.loadProjectFromZipBuffer(zipArrayBuffer as ArrayBuffer);

      const restoredCommit = commits?.find(({ sha }) => sha === commitToRestore.value);

      if (restoredCommit?.messages) {
        const chat = await getMessages(db!, chatId.value!);

        await setMessages(db!, chat.id, restoredCommit.messages, chat.urlId, chat.description, chat.timestamp);
      }

      workbenchStore.restartBielaTerminalServer().then();
    } catch (error) {
      console.error('Failed saving app:', error);
    } finally {
      setIsRestoring(false);
      //TODO: find other solution to handle loading chat message history
      //This should be a only a temporary solution.
      window.location.reload();
    }
  };

  const closeDialog = () => {
    setDialogContent(null);
  };

  async function handleDeploy() {
    if (isDeploying) {
      return;
    }

    setIsDeploying(true); // Start deployment
    // console.log('handleDeploy...', chatId.get());

    try {
      const response = await deploy(); // Call your deploy function

      setAppState(response);
    } catch (error) {
      console.error('Deployment failed:', error);
    } finally {
      setIsDeploying(false); // Reset deployment state
      setIsModifiedCode(false);
    }
  }

  function openApp() {
    if (appState?.cloudApp?.url) {
      window.open(`https://${appState.cloudApp.url}`);
    }
  }

  function goToSignIn() {
    navigate('/login');
  }

  // We'll store the initial mouse x coordinate and the starting variable value.
  const startXRef = useRef(0);
  const initialValueRef = useRef(519);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const handleMouseDown = (e: MouseEvent) => {
    e.preventDefault(); // Prevents text selection, etc.
    setIsDragging(true);
    startXRef.current = e.clientX;
    const computedStyle = getComputedStyle(document.documentElement);
    initialValueRef.current = parseFloat(computedStyle.getPropertyValue('--chat-min-width')) || 519;

    // Attach global mousemove and mouseup event listeners
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e: MouseEvent) => {
    e.preventDefault();
    const dx = e.clientX - startXRef.current;
    const newValue = initialValueRef.current + dx;
    // Ensure a minimum width of 400px
    if (newValue > 400) {
      document.documentElement.style.setProperty('--chat-min-width', `${newValue}px`);
    } else {
      document.documentElement.style.setProperty('--chat-min-width', `400px`);
    }
  };

  const handleMouseUp = () => {
    window.removeEventListener('mousemove', handleMouseMove);
    window.removeEventListener('mouseup', handleMouseUp);
    setIsDragging(false);
  };

  // New handler: on double-click, reset the width to default (519px)
  const handleDoubleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    document.documentElement.style.setProperty('--chat-min-width', `519px`);
    setIsDragging(false);
  };

  return (
    chatStarted && (
      <motion.div
        initial="closed"
        animate={showWorkbench ? 'open' : 'closed'}
        variants={workbenchVariants}
        className={`z-workbench ${showWorkbench ? 'open' : 'closed'}`}
      >
        <div
          className={classNames(
            'fixed top-[calc(var(--header-height)+30px)] bottom-0 w-[var(--workbench-inner-width)] mr-4 z-0  biela-ease-cubic-bezier',
            {
              'w-full': isSmallViewport,
              'left-[var(--workbench-left)]': showWorkbench,
              'left-[100%]': !showWorkbench,
            },
          )}
        >
          <div className={'absolute max-[800px]:top-[16px] max-[800px]:left-[-3px] top-[19px] bottom-0 left-[18px] '}>
            <div
              className={
                'absolute top-[-45px] bottom-0 p-0 hover:bg-white/5 flex items-center rounded-lg justify-center transition-colors flex flex-col gap-4'
              }
            >
              <span
                onClick={() => {
                  setShowChat(!showChat);
                }}
                className={`${showChat ? 'i-ph:caret-left' : 'i-ph:caret-right'} min-w-[20px] min-h-[20px] block bg-white cursor-pointer z-2 `}
              ></span>
              <div
                className="resize-handle max-[800px]:hidden"
                onMouseDown={(e) => handleMouseDown(e.nativeEvent)}
                onDoubleClick={handleDoubleClick}
                style={{
                  cursor: 'grab',
                  zIndex: 999,
                }}
              >
                <FaArrowsAltH size={14} className={'text-white'} />
              </div>
            </div>
          </div>
          <div
            className={`absolute inset-0 pl-[12px] max-[800px]:pl-[9px] pr-2 lg:px-5 workbench-height ml-6 ${showChat ? ' max-lg:w-[100vw]' : ''} `}
          >
            <div className="resize-from-left relative h-full flex flex-col bg-[var(--app-background-color)] border border-biela-elements-borderColor shadow-sm rounded-lg overflow-hidden">
              <div className="flex items-center gap-2 px-4 py-2 border-b border-white/5 max-h-[48px]">
                <button
                  onClick={() => setSelectedView('code')}
                  className={`flex items-center gap-2 px-3 py-1.5 rounded-md transition-colors text-sm ${selectedView === 'code' ? 'bg-[#4ADE80]/10 text-[#4ADE80]' : 'text-white/70 bg-white/10 !hover:bg-[#4ADE80]/10 !hover:text-[#4ADE80]'}`}
                >
                  <CodeBracketIcon className="w-4 h-4" />
                  Code
                </button>
                <button
                  onClick={() => setSelectedView('preview')}
                  className={`flex items-center gap-2 px-3 py-1.5 rounded-md transition-colors text-sm ${
                    selectedView === 'preview'
                      ? 'bg-[#4ADE80]/10 text-[#4ADE80]'
                      : `text-white/70 bg-white/10 ${!isStreaming && '!hover:bg-[#4ADE80]/10 !hover:text-[#4ADE80]'}`
                  }`}
                >
                  <FaEye className="w-4 h-4" />
                  Preview
                </button>
                <button
                  onClick={() => setSelectedView('settings')}
                  className={`
                    flex items-center gap-2 px-3 py-1.5 rounded-md transition-colors text-sm
                    ${selectedView === 'settings' ? 'bg-[#A78BFA]/10 text-[#A78BFA]' : 'text-white/70 hover:bg-white/10 bg-white/10 '},
                  `}
                >
                  <WrenchScrewdriverIcon className="w-4 h-4" />
                  Settings
                </button>
                <div className="ml-auto flex items-center gap-2">
                  {selectedView === 'code' && (
                    <div className="flex">
                      <div className="flex gap-1">
                        <div className={`${isStreaming ? 'opacity-50 pointer-events-none' : ''}`}>
                          <SupabaseConnectClient sendMessage={sendMessage} />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className={`relative ${isDragging && 'pointer-events-none'} flex-1 overflow-hidden`}>
                <View
                  initial={{ x: selectedView === 'code' ? 0 : '-100%' }}
                  animate={{ x: selectedView === 'code' ? 0 : '-100%' }}
                >
                  <EditorPanel />
                </View>
                <View
                  initial={{ x: selectedView === 'preview' ? 0 : '100%' }}
                  animate={{ x: selectedView === 'preview' ? 0 : '100%' }}
                >
                  <Preview
                    chatStarted={chatStarted}
                    isDeploying={isDeploying}
                    openApp={openApp}
                    handleDeploy={handleDeploy}
                    deviceMode={deviceMode}
                    DEVICES={DEVICES}
                    setDeviceMode={setDeviceMode}
                  />
                </View>
                <View
                  initial={{ x: selectedView === 'settings' ? 0 : '-100%' }}
                  animate={{ x: selectedView === 'settings' ? 0 : '-100%' }}
                >
                  {selectedView === 'settings' ? (
                    <SettingsView hasCommited={hasCommited} isForkingApp={isForkingApp} handleForkClick={handleForkClick} commitToRestore={commitToRestore} commits={commits} handleRestoreAppClick={handleRestoreAppClick} isStreaming={isStreaming} showSaveTooltip={showSaveTooltip} setShowSaveTooltip={setShowSaveTooltip} handleSaveClick={handleSaveClick} isSavingApp={isSavingApp} />
                  ) : <div />}
                </View>
              </div>
            </div>

          </div>
        </div>
        <ConfirmationDialog
          isOpen={dialogContent !== null}
          type={dialogContent?.type ?? DialogConfirmationType.FORK}
          description={dialogContent?.item.description ?? ''}
          isLoading={isSavingApp || isRestoring || isForkingApp}
          onConfirm={async () => {
            const handler = {
              ...emptyConfirmationDialogHandler,
              [DialogConfirmationType.FORK]: () => handleFork(),
              [DialogConfirmationType.SAVE]: () => handleSaveApp(),
              [DialogConfirmationType.RESTORE]: () => handleRestoreApp(),
            };

            if (dialogContent?.type) {
              await handler[dialogContent.type]();
            }

            closeDialog();
          }}
          onCancel={closeDialog}
          containerClassName={'overflow-visible'}
          content={
            dialogContent?.type === DialogConfirmationType.RESTORE &&
            !!commits?.length && (
              <div>
                <p>Please select a commit to restore:</p>
                <Dropdown
                  value={commitToRestore}
                  items={
                    commits?.map(({ sha: value, date }) => ({
                      value,
                      label: new Date(date).toLocaleString(),
                    })) || []
                  }
                  disabled={isRestoring}
                  onSelect={(select: DropdownItems) => {
                    if (select) {
                      setCommitToRestore(select);
                    }
                  }}
                />
              </div>
            )
          }
        />
      </motion.div>
    )
  );
});

Workbench.displayName = 'WorkbenchClient';

interface ViewProps extends HTMLMotionProps<'div'> {
  children: ReactElement;
}

const View = memo(({ children, ...props }: ViewProps) => {
  return (
    <motion.div className="absolute inset-0" transition={viewTransition} {...props}>
      {children}
    </motion.div>
  );
});
