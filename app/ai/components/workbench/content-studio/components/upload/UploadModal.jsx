import React, { useRef, useState, useEffect } from 'react';
import {
  XMarkIcon,
  FolderIcon,
  ArrowUpTrayIcon,
  ArrowPathIcon,
  ChevronRightIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';
import { formatFileSize, getImageDimensions } from '../../utils/fileUtils';
import { uploadResource, deleteImageFromS3 } from '../../../../../lib/stores/contentStudio';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';

const CONTENT_STUDIO_AWS_DEFAULT_PATH = import.meta.env.VITE_CONTENT_STUDIO_AWS_DEFAULT_PATH
const CONTENT_STUDIO_AWS_DEFAULT_PATH_CUSTOM = import.meta.env.VITE_CONTENT_STUDIO_AWS_DEFAULT_PATH_CUSTOM;

function UploadModal({ isOpen, onClose, onUploadComplete, contentAreaRef, contentStudioRef, allFolders}) {
  const { t } = useTranslation('contentStudio');
  const [files, setFiles] = useState([]);
  const [selectedFileIndex, setSelectedFileIndex] = useState(0);
  const [fileTitle, setFileTitle] = useState('');
  const [tags, setTags] = useState({});
  const [tagInput, setTagInput] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('all');
  const [isDragging, setIsDragging] = useState(false);
  const [isFolderSelectorOpen, setIsFolderSelectorOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [expandedFolders, setExpandedFolders] = useState({
    marketing: true,
    'website-assets': true,
    'client-projects': true,
  });

  const fileInputRef = useRef(null);
  const dropAreaRef = useRef(null);
  const folderSelectorRef = useRef(null);
  const modalRef = useRef(null);

  const MAX_FILES_UPLOADING = 10;
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB

  const deleteSingleImageFromS3 = (imageUrl) => {
    const splitUrl = imageUrl.split('/');
    const imageId = splitUrl[splitUrl.length - 1];
    return deleteImageFromS3(imageId);
  }

  // Define cleanupFiles function
  const cleanupFiles = async ({uploaded = false} = {}) => {
    // Revoke object URLs to prevent memory leaks
    await Promise.all(
        files.map((file) => {
          if (file.url) {
            URL.revokeObjectURL(file.url);
            if(!uploaded){
              return deleteSingleImageFromS3(file.url);
            } else {
              return Promise.resolve();
            }
          }
          return Promise.resolve();
        }
    ));

    // Reset all state
    setFiles([]);
    setSelectedFileIndex(0);
    setFileTitle('');
    setTags({});
    setTagInput('');
    setIsDragging(false);
    setIsFolderSelectorOpen(false);
  };

  // Custom close handler that cleans up before calling the parent's onClose
  const handleClose = ({ uploaded = false } = {}) => {
    cleanupFiles({ uploaded });
    setSelectedFolder('all');
    onClose();
  };

  // Add effect to handle outside click
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event) => {
      // Only close if clicking the backdrop (not the modal content)
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        handleClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Handle folder selector click outside
  useEffect(() => {
    if (!isFolderSelectorOpen) return;

    const handleFolderSelectorClickOutside = (event) => {
      if (folderSelectorRef.current && !folderSelectorRef.current.contains(event.target)) {
        setIsFolderSelectorOpen(false);
      }
    };

    document.addEventListener('mousedown', handleFolderSelectorClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleFolderSelectorClickOutside);
    };
  }, [isFolderSelectorOpen]);

  // Clear all files when modal is closed or unmounted
  useEffect(() => {
    // Clean up function to run when component unmounts
    return () => {
      cleanupFiles();
    };
  }, []);


  // If not open, don't render anything
  if (!isOpen) return null;

  const selectedFile = files.length > 0 ? files[selectedFileIndex] : null;

  // Filter out special folders and get regular folders
  const regularFolders = allFolders.filter((folder) => !folder.isRoot);

  // Get the selected folder name for display
  const getSelectedFolderName = () => {
    if (selectedFolder === 'all') return t('rootAllFiles', 'Root (All Files)');
    const folder = allFolders.find((f) => f.id === selectedFolder);
    return folder ? folder.name : t('rootAllFiles', 'Root (All Files)');
  };

  const handleFilesSelected = async (selectedFiles) => {
    if (selectedFiles.length > MAX_FILES_UPLOADING) {
      toast.error(t('errorMaxFiles', { count: MAX_FILES_UPLOADING }, `You can only select up to ${MAX_FILES_UPLOADING} files.`));
      fileInputRef.current.value = ''; // Clear the selection
      return;
    }

    const validFiles = Array.from(selectedFiles).filter(file => {
      if(file.size > MAX_FILE_SIZE){
        toast.error(t('errorFileSizeExceeded', { fileName: file.name }, `"${file.name}" exceeds the maximum file size accepted. Skipping...`));
        return false;
      } else if (!file.type || !file.type.startsWith('image/')){
        toast.error(t('errorFileTypeNotSupported', { fileName: file.name }, `"${file.name}"'s file type is not supported. Supported formats: JPG, PNG, GIF, WebP`));
        return false;
      }
      return true;
    });

    if(validFiles.length === 0){
      toast.error(t('errorNoValidFiles', 'No valid files to upload.'));
      return;
    }

    setIsUploading(true);
    // Convert FileList to array and create file objects with preview URLs
    const uploadedPromises = validFiles.map(async (file) => {
      // Create preview URL for image files
      let width = '1920';
      let height = '1080';

      try {
        ({ width, height } = await getImageDimensions(file));
      } catch (error) {
        console.error('Failed to load image dimensions:', error);
      }

      try {
        const uploadResult = await uploadResource(file);
        let url = '';
        if (CONTENT_STUDIO_AWS_DEFAULT_PATH && CONTENT_STUDIO_AWS_DEFAULT_PATH_CUSTOM) {
          url = uploadResult?.url.replace(
            CONTENT_STUDIO_AWS_DEFAULT_PATH,
            CONTENT_STUDIO_AWS_DEFAULT_PATH_CUSTOM,
          );
        } else {
          url = uploadResult?.url;
        }

        console.log(uploadResult);
        console.log(url);

        // Get file name without extension as default title
        const nameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;

        return {
          file,
          name: file.name,
          title: nameWithoutExt,
          size: file.size.toString(),
          type: file.type,
          dimensions: `${width} x ${height}`,
          url,
        };
      } catch (err) {
        console.error(err);
        toast.error(t('uploadError', 'There was an error while uploading.'));
        return null;
      }
    });

    const uploadedFiles = (await Promise.all(uploadedPromises)).filter(Boolean);

    // Set files state with the new files
    setFiles([...files, ...uploadedFiles]);

    // Select first file if this is the first upload
    if (files.length === 0 && uploadedFiles.length > 0) {
      setSelectedFileIndex(0);
      setFileTitle(uploadedFiles[0].title);
    }

    setIsUploading(false);
  };

  // Handle drag events for the drop area
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!dropAreaRef.current?.contains(e.relatedTarget)) {
      setIsDragging(false);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFilesSelected(e.dataTransfer.files);
    }
  };

  const handleAddMore = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (index) => {
    setSelectedFileIndex(index);
    setFileTitle(files[index].title);
  };

  const handleTitleChange = (e) => {
    setFileTitle(e.target.value);

    // Update the title in the files array
    const updatedFiles = [...files];
    updatedFiles[selectedFileIndex].title = e.target.value;
    setFiles(updatedFiles);
  };

  const handleTagKeyDown = (e) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      setTags(prevTags => ({
        ...prevTags,
        [selectedFileIndex]: [...new Set([...(prevTags[selectedFileIndex] || []), tagInput.trim()])],
      }));
      setTagInput('');
    }
  };

  const handleRemoveFile = async (index, e) => {
    e.stopPropagation(); // Prevent triggering selection when removing
    e.preventDefault(); // Prevent any default behavior

    // Create a copy of the files array
    const newFiles = [...files];

    // If we're removing the selected file
    if (index === selectedFileIndex) {
      // If it's the last file, select the previous one
      // If it's not the last file, keep the same index (it will point to the next file)
      if (index === files.length - 1 && index > 0) {
        setSelectedFileIndex(index - 1);
        setFileTitle(files[index - 1].title);
      } else if (files.length > 1) {
        setFileTitle(files[index === 0 ? 1 : index].title);
      }
    } else if (index < selectedFileIndex) {
      // If we're removing a file before the selected one, decrement the selected index
      setSelectedFileIndex(selectedFileIndex - 1);
    }

    // Release the object URL to avoid memory leaks
    URL.revokeObjectURL(files[index].url);

    // Delete the image from S3
    await deleteSingleImageFromS3(files[index].url);

    // Remove the file from the array
    newFiles.splice(index, 1);
    setFiles(newFiles);

    // If no files left, reset everything
    if (newFiles.length === 0) {
      setSelectedFileIndex(0);
      setFileTitle('');
      setTags({});
    }
  };

  const handleToggleFolder = (folderId) => {
    setExpandedFolders((prev) => ({
      ...prev,
      [folderId]: !prev[folderId],
    }));
  };

  const handleSelectFolder = (folderId) => {
    setSelectedFolder(folderId);
    setIsFolderSelectorOpen(false);
  };

  // Recursive function to render folder structure
  const renderFolders = (parentId = null, level = 0) => {
    const foldersToRender = regularFolders.filter((folder) => folder.parentId === parentId);

    return foldersToRender.map((folder) => {
      const hasChildren = regularFolders.some((f) => f.parentId === folder.id);
      const isExpanded = expandedFolders[folder.id];

      return (
        <div key={folder.id} style={{ paddingLeft: `${level * 12}px` }}>
          <div
            className={`flex items-center px-3 py-2 rounded-md cursor-pointer ${
              selectedFolder === folder.id ? 'bg-[#22D3EE]/10 text-[#22D3EE]' : 'hover:bg-white/5 text-white/80'
            }`}
            onClick={() => handleSelectFolder(folder.id)}
          >
            <div className="flex items-center gap-2 w-full">
              {hasChildren && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleToggleFolder(folder.id);
                  }}
                  className="p-1 bg-transparent hover:bg-white/10 rounded"
                >
                  {isExpanded ? (
                    <ChevronDownIcon className="w-3.5 h-3.5 text-white/50" />
                  ) : (
                    <ChevronRightIcon className="w-3.5 h-3.5 text-white/50" />
                  )}
                </button>
              )}
              {!hasChildren && <div className="w-5"></div>}
              <FolderIcon className="w-5 h-5" />
              <span className="text-sm truncate">{folder.name}</span>
            </div>
          </div>

          {hasChildren && isExpanded && <div>{renderFolders(folder.id, level + 1)}</div>}
        </div>
      );
    });
  };

  const handleUpload = () => {
    for (let file of files) {
      // Validate file name
      if (!file.title.trim()) {
        toast.error(t('errorMissingFileTitle', 'All files must have a name.'));
        return;
      }
    }

    // Prepare files with metadata
    const filesToUpload = files.map((file, index) => {
      return ({
      ...file,
      // Use the updated title if it's been set
      title: file.title,
      // Apply tags only to selected file in this simple version + adding the tag written in input, but without pressing enter
      tags: [
        ...(tags[index] || []),
        ...(index === selectedFileIndex && tagInput ? [tagInput] : [])
      ] || [],

      // Add the parent folder
      folderId: selectedFolder,
    })});

    onUploadComplete(filesToUpload);

    // Clean up and close. Passing true for uploaded so we do not delete the S3 images on cleanup
    handleClose({ uploaded: true });
  };

  // Format file type (e.g., "image/jpeg" to "JPEG")
  const getFormattedFileType = (mimeType) => {
    const type = mimeType.split('/')[1]?.toUpperCase() || '';
    return type;
  };

  return (
    <div
      className="absolute inset-0 bg-[#0A0F1C] backdrop-blur-sm z-50 flex items-center justify-center"
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
      }}
    >
      <div
        ref={modalRef}
        className="flex flex-col w-full h-full overflow-hidden relative"
        onClick={(e) => e.stopPropagation()} // Prevent clicks inside from closing
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <h2 className="text-xl font-medium text-white">{t('uploadFiles', 'Upload Files')}</h2>
          <button onClick={handleClose} className="p-2 rounded-full bg-transparent hover:bg-white/10 transition-colors">
            <XMarkIcon className="w-5 h-5 text-white/70" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {isUploading ? (
            // Main preview area
            <div className="flex-1 flex items-center justify-center p-6">
              <div
                className="w-full h-full flex flex-col items-center justify-center transition-all border-2 border-dashed border-white/20 rounded-xl p-10"
              >
                <div className="mb-6 w-20 h-20 rounded-full bg-white/5 flex items-center justify-center">
                  <ArrowPathIcon className="w-10 h-10 text-white/40 animate-spin" />
                </div>
                <h3 className="text-xl font-medium text-white/90 mb-2">
                  {t('uploading', 'Uploading...')}
                </h3>
              </div>
            </div>
          ) : (
            <>
              {/* Left side - Preview */}
              <div className="w-3/5 border-r border-white/10 flex flex-col">
                {/* Main preview area */}
                <div
                  ref={dropAreaRef}
                  className="flex-1 flex items-center justify-center p-6"
                  onDragEnter={handleDragEnter}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  {selectedFile ? (
                    <div className="w-full h-full max-w-[800px] max-h-[450px] flex items-center justify-center overflow-hidden rounded-md bg-black/20 border-2 border-white/10">
                      <img
                        src={selectedFile.url}
                        alt={selectedFile.name}
                        className="max-w-full max-h-full object-contain"
                      />
                    </div>
                  ) : (
                    <div
                      className={`w-full h-full flex flex-col items-center justify-center transition-all ${
                        isDragging
                          ? 'border-2 border-dashed border-[#4ADE80] bg-[#4ADE80]/5'
                          : 'border-2 border-dashed border-white/20'
                      } rounded-xl p-10`}
                    >
                      <div className="mb-6 w-20 h-20 rounded-full bg-white/5 flex items-center justify-center">
                        <ArrowUpTrayIcon className="w-10 h-10 text-white/40" />
                      </div>
                      <h3 className="text-xl font-medium text-white/90 mb-2">
                        {isDragging ? t('dropFilesHere', 'Drop files here') : t('dragDropFilesHere', 'Drag & Drop files here')}
                      </h3>
                      <p className="text-white/50 text-center mb-6 max-w-md">
                        {isDragging
                          ? t('releaseToUpload', 'Release to upload your files')
                          : t('dragDropOrBrowse', 'Drag and drop image files here, or click the button below to browse')}
                      </p>
                      <button
                        onClick={handleAddMore}
                        className="px-6 py-2.5 bg-white/10 hover:bg-white/15 text-white rounded-lg transition-colors"
                      >
                        {t('browseFiles', 'Browse Files')}
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Right side - Metadata */}
              <div className="w-2/5 flex flex-col">
                {selectedFile ? (
                  <div className={`flex flex-col p-6 h-full ${tags.length > 0 ? 'overflow-y-auto' : ''}`}>
                    <div className="flex items-start gap-4 mb-4">
                      <div className="w-14 h-14 bg-white/5 rounded overflow-hidden border border-white/10 flex items-center justify-center">
                        <img src={selectedFile.url} alt={selectedFile.name} className="w-full h-full object-cover" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-medium text-white mb-1">{selectedFile.name}</h3>
                        <p className="text-white/50 text-sm">
                          {formatFileSize(selectedFile.size)} • {getFormattedFileType(selectedFile.type)} •{' '}
                          {selectedFile.dimensions}
                        </p>
                      </div>
                    </div>

                    {/* File metadata form - sized to avoid scrolling initially */}
                    <div className="space-y-4 flex-1 flex flex-col justify-between">
                      {/* Top section */}
                      <div className="space-y-4">
                        <div>
                          <label className="block text-white/70 text-sm mb-1.5">{t('fileTitle', 'File Title')}</label>
                          <input
                            type="text"
                            value={fileTitle}
                            onChange={handleTitleChange}
                            className="w-full bg-[#1A1F2E] border border-white/10 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#4ADE80]/50"
                            placeholder={selectedFile.title}
                          />
                        </div>

                        <div>
                          <label className="block text-white/70 text-sm mb-1.5">
                            {t('tagsForFile', 'Tags for')} {fileTitle || selectedFile.name}
                          </label>
                          <input
                            type="text"
                            value={tagInput}
                            onChange={(e) => setTagInput(e.target.value)}
                            onKeyDown={handleTagKeyDown}
                            placeholder={t('placeholderAddTag', 'Add a tag and press Enter')}
                            className="w-full bg-[#1A1F2E] border border-white/10 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#4ADE80]/50 placeholder-white/30"
                          />
                          <p className="text-white/40 text-xs mt-1">{t('pressEnterToAddTag', 'Press Enter to add each tag')}</p>

                          <div className="flex flex-wrap gap-2 mt-2 max-h-24 overflow-y-auto">
                            {tags[selectedFileIndex]?.map((tag, i) => (
                              <span
                                key={i}
                                className="bg-white/10 text-white/80 px-2 py-1 rounded text-sm flex items-center gap-1"
                              >
                                {tag}
                                <button
                                  onClick={() => {
                                    setTags(prev => ({
                                      ...prev,
                                      [selectedFileIndex]: prev[selectedFileIndex].filter((_, index) => index !== i)
                                    }));
                                  }}

                                  className="bg-transparent hover:bg-white/10 rounded-full p-0.5"
                                >
                                  <XMarkIcon className="w-3.5 h-3.5" />
                                </button>
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Folder section - always at the bottom */}
                      <div className="mt-auto relative">
                        <label className="block text-white/70 text-sm mb-1.5">{t('folderLocation', 'Folder Location')}</label>
                        <div className="relative" ref={folderSelectorRef}>
                          <button
                            onClick={() => setIsFolderSelectorOpen(!isFolderSelectorOpen)}
                            className="w-full flex items-center justify-between bg-[#1A1F2E] border border-white/10 rounded-lg px-3 py-2 text-white hover:bg-[#1A1F2E]/70 transition-colors"
                          >
                            <div className="flex items-center gap-2">
                              <FolderIcon className="w-5 h-5 text-white/70" />
                              <span>{getSelectedFolderName()}</span>
                            </div>
                            <ChevronDownIcon
                              className={`w-5 h-5 text-white/50 transition-transform ${isFolderSelectorOpen ? 'transform rotate-180' : ''}`}
                            />
                          </button>

                          {/* Folder Selection Dropdown */}
                          {isFolderSelectorOpen && (
                            <div className="absolute bottom-full left-0 right-0 mb-1 bg-[#1A1F2E] border border-white/10 rounded-lg shadow-xl max-h-60 overflow-y-auto z-50">
                              <div className="p-2">
                                {/* Root option */}
                                <div
                                  className={`flex items-center px-3 py-2 rounded-md cursor-pointer ${
                                    selectedFolder === 'all'
                                      ? 'bg-[#22D3EE]/10 text-[#22D3EE]'
                                      : 'hover:bg-white/5 text-white/80'
                                  }`}
                                  onClick={() => handleSelectFolder('all')}
                                >
                                  <FolderIcon className="w-5 h-5 mr-2" />
                                  <span className="text-sm">{t('rootAllFiles', 'Root (All Files')}</span>
                                </div>

                                {/* Render folders recursively */}
                                {renderFolders()}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex-1 flex flex-col items-center justify-center text-center p-6">
                    <div className="mb-6 w-16 h-16 rounded-full bg-white/5 flex items-center justify-center">
                      <ArrowUpTrayIcon className="w-8 h-8 text-white/40" />
                    </div>
                    <h3 className="text-xl font-medium text-white/90 mb-2">{t('noFilesSelected', 'No files selected')}</h3>
                    <p className="text-white/50 mb-6 max-w-xs">
                      {t('selectFilesHint', 'Select files by dragging them to the upload area or using the browse button')}
                    </p>
                    <div className="space-y-2">
                      <p className="text-white/40 text-xs">{t('supportedFormats', 'Supported formats: JPG, PNG, GIF, WebP')}</p>
                      <p className="text-white/40 text-xs">{t('maxFileSize', 'Maximum file size: 5MB')}</p>
                    </div>
                  </div>
                )}

                {/* Hidden input for file selection */}
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => handleFilesSelected(e.target.files)}
                />
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-white/10 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {files.length > 0 ? (
                <>
                  <span className="text-white/70 text-sm">{t('filesToUpload', 'Files to upload')} ({files.length})</span>
                  <button
                    onClick={handleAddMore}
                    className="bg-transparent text-[#4ADE80] text-sm flex items-center gap-1 hover:text-[#4ADE80]/80"
                  >
                    + {t('addMore', 'Add More')}
                  </button>
                </>
              ) : (
                <span className="text-white/50 text-sm">{t('selectFilesToUpload', 'Select files to upload')}</span>
              )}
            </div>

            {/* Action buttons - always in the same place */}
            <div className="flex items-center gap-3">
              <button
                onClick={handleClose}
                className="px-5 py-2 bg-[#1A1F2E] text-white/80 rounded-lg hover:bg-[#1A1F2E]/70 transition-colors"
              >
                {t('cancel', 'Cancel')}
              </button>
              <button
                onClick={handleUpload}
                disabled={files.length === 0}
                className={`px-5 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                  files.length > 0
                    ? 'bg-[#4ADE80] text-black hover:bg-[#4ADE80]/90'
                    : 'bg-[#1A1F2E] text-white/40 cursor-not-allowed'
                }`}
              >
                <ArrowUpTrayIcon className="w-5 h-5" />
                {t('upload', 'Upload')} {files.length > 0 ? `(${files.length})` : '(0)'}
              </button>
            </div>
          </div>

          {/* Only show thumbnails if we have files */}
          {files.length > 0 && (
            <div className="flex gap-3 overflow-x-auto mt-3">
              {files.map((file, index) => (
                <div key={index} onClick={() => handleFileSelect(index)} className="relative group">
                  {/* Fixed: Added padding and improved selection indication */}
                  <div
                    className={`p-0.5 ${index === selectedFileIndex ? 'bg-[#4ADE80]' : 'bg-transparent'} rounded-md cursor-pointer`}
                  >
                    <div className="w-16 h-16 overflow-hidden rounded-sm">
                      <img src={file.url} alt={file.name} className="w-full h-full object-cover" />
                    </div>

                    {/* X button that only shows on hover */}
                    <button
                      onClick={(e) => handleRemoveFile(index, e)}
                      className="absolute top-0 right-0 w-5 h-5 bg-black/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-20"
                    >
                      <XMarkIcon className="w-3 h-3 text-white" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default UploadModal;
