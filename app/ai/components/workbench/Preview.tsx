import { useStore } from '@nanostores/react';
import { memo, useEffect, useRef, useState } from 'react';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { PortDropdown } from './PortDropdown';
import { ScreenshotSelector } from './ScreenshotSelector';
import { getAppState } from '~/ai/lib/stores/cloud/cloud';
import { IconButton } from '~/components/common/IconButton';
import { PanelHeaderButton } from '~/ai/components/PanelHeaderButton';
import { FaRocket } from 'react-icons/fa';
import WithTooltip from '~/ai/components/Tooltip';
import { chatStore } from '~/ai/lib/stores/chat';

export const Preview = memo(
  ({
    isDeploying,
    deviceMode,
   DEVICES,
   setDeviceMode,
     chatStarted,
     handleDeploy,
     openApp
  }: {
    isDeploying: boolean;
    chatStarted?:boolean
    DEVICES: { label: string, value: number, id: string, Icon:  JSX.Element }[],
    setDeviceMode: (device:{ label: string, value: number, id: string, Icon:  JSX.Element })=>void,
    handleDeploy: ()=> void,
    openApp: ()=> void,
    deviceMode: {
      label: string;
      value: number;
      id: string;
    };
  }) => {
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const selectedView = useStore(workbenchStore.currentView);
    const { isModifiedCode } = useStore(chatStore);
    const [activePreviewIndex, setActivePreviewIndex] = useState(0);
    const [isPortDropdownOpen, setIsPortDropdownOpen] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const hasSelectedPreview = useRef(false);
    const previews = useStore(workbenchStore.previews);
    const activePreview = previews[activePreviewIndex];

    const [url, setUrl] = useState<string | null>(null);
    const [iframeUrl, setIframeUrl] = useState<string | undefined>();
    const [isSelectionMode, setIsSelectionMode] = useState(false);

    const [appState, setAppState] = useState<any>(undefined);

    useEffect(() => {
      if (selectedView === 'preview') {
        reloadPreview();
      }
    }, [selectedView]);

    useEffect(() => {
      if (!activePreview) {
        setUrl(null);
        setIframeUrl(undefined);
        return;
      }

      const { baseUrl } = activePreview;

      setIframeUrl(baseUrl);
    }, [activePreview]);

    const fetchAppState = async () => {
      const state = await getAppState();
      setAppState(state);
    };

    useEffect(() => {
      if (chatStarted)
        fetchAppState();
    }, []);

    useEffect(() => {
      if (chatStarted)
        fetchAppState();
    }, [isDeploying]);

    useEffect(() => {
      const fallbackUrl = 'http://localhost:5173/';

      setUrl(appState?.cloudApp?.url ? `https://${appState.cloudApp.url}` : fallbackUrl);
    }, [appState]);

    const reloadPreview = () => {
      if (iframeRef.current) {
        iframeRef.current.src = iframeRef.current.src;
      }
    };

    const formatDeployedAt = (isoString?: string) => {
      if (!isoString) return 'Not deployed';
      const date = new Date(isoString);
      return new Intl.DateTimeFormat('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        day: '2-digit',
        month: 'long',
        year: 'numeric',
      }).format(date);
    };

    const toggleFullscreen = async () => {
      if (!isFullscreen && containerRef.current) {
        await containerRef.current.requestFullscreen();
      } else if (document.fullscreenElement) {
        await document.exitFullscreen();
      }
    };

    useEffect(() => {
      const handleFullscreenChange = () => {
        setIsFullscreen(!!document.fullscreenElement);
      };

      document.addEventListener('fullscreenchange', handleFullscreenChange);

      return () => {
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
      };
    }, []);

    useEffect(() => {
      const readyPreview = previews.find(preview => preview.ready);
      if (readyPreview) {
        setUrl(readyPreview.baseUrl);
      } else {
        setUrl(null);
      }
    }, [previews]);

    useEffect(() => {
      if (url) {
        localStorage.setItem('lastPreviewUrl', url);
      }
    }, [url]);

    useEffect(() => {
      if (!url) {
        const lastUrl = localStorage.getItem('lastPreviewUrl');
        if (lastUrl) {
          setUrl(lastUrl);
        }
      }
    }, [url]);

    const handleIframeLoad = () => {
      if (iframeRef.current && iframeRef.current.contentWindow) {
        setUrl(iframeRef.current.src);
      }
    };

    return (
      <div ref={containerRef} className="w-full h-full flex flex-col relative">
        {isPortDropdownOpen && (
          <div className="z-iframe-overlay w-full h-full absolute" onClick={() => setIsPortDropdownOpen(false)} />
        )}
        <div className="flex items-center justify-between px-4 py-2 border-b border-white/10 gap-4">
          <div className={'flex items-center gap-4 flex-1'}>
            <div className="flex items-center gap-1 bg-white/5 rounded-lg p-1">
              {DEVICES.map((device) => (
                <button
                  title={device.label}
                  key={device.id}
                  onClick={() => setDeviceMode(device)}
                  className={`p-1.5 rounded transition-colors  ${deviceMode.id === device.id ? 'bg-[#4ADE80]/10 text-[#4ADE80]' : 'text-white/50 bg-white/0 hover:bg-white/10'}`}
                >
                  {device.Icon}
                </button>
              ))}
            </div>
            <div className="flex-1 flex items-center gap-2 bg-white/5 rounded-lg px-3 py-1.5 min-w-[300px]">
              <WithTooltip
                tooltip={
                  <div className="flex flex-col text-left text-xs leading-tight">
                    <span className="font-medium text-white/80">Last deployment time</span>
                    <span className="text-white/60">{formatDeployedAt(appState?.cloudApp?.deployed_at)}</span>
                  </div>
                }
                position="bottom"
                className="bg-black/70 p-2 rounded-md mt-2"
              >
                <img src="/icons/globe.svg" className="w-[14px] h-[14px] cursor-pointer" alt="URL" />
              </WithTooltip>
              <div className="link-copy">
                <input
                  title="URL"
                  ref={inputRef}
                  className="flex-1 w-full bg-transparent text-sm text-white/70 outline-none"
                  type="text"
                  value={url || ''}
                  disabled
                />
              </div>
              {previews.length > 0 && (
                <PortDropdown
                  activePreviewIndex={activePreviewIndex}
                  setActivePreviewIndex={setActivePreviewIndex}
                  isDropdownOpen={isPortDropdownOpen}
                  setHasSelectedPreview={(value) => (hasSelectedPreview.current = value)}
                  setIsDropdownOpen={setIsPortDropdownOpen}
                  previews={previews}
                />
              )}
              <IconButton icon="refresh" onClick={reloadPreview} title="Reload Preview" className="w-6 !max-h-6" />
              <IconButton
                icon="expand"
                onClick={toggleFullscreen}
                size={'sm'}
                title={isFullscreen ? 'Exit Full Screen' : 'Full Screen'}
                className="w-6 !max-h-6"
              />
            </div>
          </div>

          <div className="right-btn-coding">
            <div className="flex overflow-y-auto gap-2">
              <PanelHeaderButton
                disabled={isDeploying || !isModifiedCode} // Disable button during deployment
                className="flex items-center gap-2 px-3 py-1.5 rounded-md transition-colors text-sm !bg-[#4ADE80]/10 !text-[#4ADE80] !hover:bg-[#4ADE80]/30"
                disabledClassName={`opacity-50 ${isModifiedCode ? 'cursor-wait' : 'cursor-not-allowed'}`}
                onClick={handleDeploy}
              >
                {isDeploying ? (
                  <>Deploying...</>
                ) : (
                  <>
                    <FaRocket className={'min-w-[16px] min-h-[16px] max-w-[16px] max-h-[16px]'} />
                    Deploy
                  </>
                )}
              </PanelHeaderButton>
              {appState?.cloudApp?.url && (
                <PanelHeaderButton
                  disabled={isDeploying}
                  className="flex items-center gap-2 px-3 py-1.5 rounded-md transition-colors text-sm !bg-[#4ADE80]/10 !text-[#4ADE80] !hover:bg-[#4ADE80]/30"
                  disabledClassName="opacity-50 cursor-wait"
                  onClick={openApp}
                >
                  Live App
                </PanelHeaderButton>
              )}
            </div>
          </div>
        </div>

        <div className="flex-1 border-t border-biela-elements-borderColor flex justify-center items-center overflow-auto">
          <div
            style={{
              width: `${deviceMode?.value ?? 100}%`,
              height: '100%',
              overflow: 'visible',
              background: '#fff',
              position: 'relative',
              display: 'flex',
            }}
            className="transition-width duration-300 ease-in-out"
          >
            {activePreview ? (
              <>
                <iframe
                  ref={iframeRef}
                  title="preview"
                  className="border-none w-full h-full bg-white"
                  src={iframeUrl}
                  onLoad={handleIframeLoad}
                  allowFullScreen
                />
                <ScreenshotSelector
                  isSelectionMode={isSelectionMode}
                  setIsSelectionMode={setIsSelectionMode}
                  containerRef={iframeRef}
                />
              </>
            ) : (
              <div className="flex w-full h-full justify-center items-center bg-white">No preview available</div>
            )}
          </div>
        </div>
      </div>
    );
  },
);

Preview.displayName = 'Preview';
