/*
 * @ts-nocheck
 * Preventing TS checks with files presented in the video for a better presentation.
 */
import { useStore } from '@nanostores/react';
import type { JSONValue, Message } from 'ai';
import { useChat } from 'ai/react';
import React, { memo, UIEvent, useCallback, useEffect, useRef, useState } from 'react';
import { useAnimate } from 'framer-motion';
import { toast } from 'react-toastify';
import { useSettings } from '~/ai/lib/hooks/useSettings';
import { description, generateProjectSlug, useChatHistory } from '~/ai/lib/persistence';
import { chatStore, setEstimationsData, setIsModifiedCode, setIsStreaming } from '~/ai/lib/stores/chat';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { AI_MODEL_STORAGE_KEY, AI_MODELS, PROMPT_COOKIE_KEY } from '~/utils/constants';
import { createScopedLogger, renderLogger } from '~/utils/logger';
import { BaseChat } from './BaseChat';
import Cookies from 'js-cookie';
import { debounce } from '~/utils/debounce';
import { useMessageParser, usePromptEnhancer, useShortcuts, useSnapScroll } from '~/ai/lib/hooks';
import { useSearchParams } from 'react-router-dom';
import { createSampler } from '~/utils/sampler';
import { getTemplates, selectStarterTemplate } from '~/utils/selectStarterTemplate';
import '~/components/styles/index.scss?url';
import { logStore } from '~/ai/lib/stores/logs';
import ErrorTooLongDialog from '~/ai/components/ErrorToLongDialog'; // Import the dialog here
import Wrapper from '~/components/common/Wrapper';
import SessionStore from '../lib/stores/session/sessionStore';
import { BackgroundLinesComponent } from '~/components/styles/backgroundLinesComponent';
import ErrorRunOutOfTokensDialog from '~/ai/components/ErrorRunOutOfTokensDialog';
import { createCheckoutSession } from '~/lib/stores/billing';
import { TokensSession, TokensStore } from '~/ai/lib/stores/tokens/tokensStore';
import { useUser } from '~/ai/lib/context/userContext';
import { updateProjectName } from '~/api/projectsApi';
import { backendApiFetch } from '~/ai/lib/backend-api';

const EXTENDED_THINKING_TOKEN_THRESHOLD = Number(import.meta.env.VITE_EXTENDED_THINKING_TOKEN_THRESHOLD) || 100000;

const logger = createScopedLogger('Chat');

export function Chat({ id }: { id: string }) {
  renderLogger.trace('Chat');

  const { ready, initialMessages, storeMessageHistory, importChat, exportChat, loadChatById } = useChatHistory();
  const title = useStore(description);

  if (title && id) {
    const slug = generateProjectSlug(id);

    if (slug) {
      updateProjectName(slug, title);
    }
  }

  useEffect(() => {
    workbenchStore.setReloadedMessages(initialMessages.map((m) => m.id));
  }, [initialMessages]);

  useEffect(() => {
    if (id) {
      const fetchChat = async () => {
        try {
          console.log('Loading chat with ID:', id);
          await loadChatById(id);
        } catch (error) {
          console.error('Error loading chat:', error);
        }
      };
      fetchChat();
    }
  }, [id]);

  return (
    <>
      {ready && (
        <ChatImpl
          description={title}
          initialMessages={initialMessages}
          storeMessageHistory={storeMessageHistory}
          importChat={importChat}
          exportChat={exportChat}
        />
      )}
    </>
  );
}

const processSampledMessages = createSampler(
  (options: {
    messages: Message[];
    initialMessages: Message[];
    isLoading: boolean;
    parseMessages: (messages: Message[], isLoading: boolean) => void;
    storeMessageHistory: (messages: Message[]) => Promise<void>;
  }) => {
    const validateAndFixMessages = (messages: Message[]) => {
      return messages.map((message, index) => {
        if (message.role !== 'assistant' || index === messages.length - 1) {
          return message;
        }

        let content = message.content;

        const actionOpenTags = content.match(/<bielaAction\b[^>]*>/g) || [];
        const actionCloseTags = content.match(/<\/bielaAction>/g) || [];
        const artifactOpenTags = content.match(/<bielaArtifact\b[^>]*>/g) || [];
        const artifactCloseTags = content.match(/<\/bielaArtifact>/g) || [];

        const actionOpenCount = actionOpenTags.length;
        const actionCloseCount = actionCloseTags.length;
        const artifactOpenCount = artifactOpenTags.length;
        const artifactCloseCount = artifactCloseTags.length;

        if (actionOpenCount > actionCloseCount) {
          content += '\n</bielaAction>'.repeat(actionOpenCount - actionCloseCount);

          if (artifactOpenCount > artifactCloseCount) {
            content += '\n</bielaArtifact>'.repeat(artifactOpenCount - artifactCloseCount);
          }
        }

        return {
          ...message,
          content,
        };
      });
    };

    const { messages, initialMessages, isLoading, parseMessages, storeMessageHistory } = options;
    parseMessages(validateAndFixMessages(messages), isLoading);

    if (messages.length > initialMessages.length) {
      storeMessageHistory(validateAndFixMessages(messages)).catch((error) => toast.error(error.message));
    }
  },
  50,
);

interface PreviewError {
  type: 'error';
  message: string;
  source: string;
  lineno: string;
  colno: string;
  error: Error;
}

interface ChatProps {
  initialMessages: Message[];
  storeMessageHistory: (messages: Message[]) => Promise<void>;
  importChat: (description: string, messages: Message[]) => Promise<void>;
  exportChat: (id?: string) => Promise<void>;
  description?: string;
}

export const ChatImpl = memo(
  ({ description, initialMessages, storeMessageHistory, importChat, exportChat }: ChatProps) => {
    useShortcuts();

    const [showPromptTooLongDialog, setShowPromptTooLongDialog] = useState(false);

    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const [chatStarted, setChatStarted] = useState(!!initialMessages?.length || false);
    const [uploadedFiles, setUploadedFiles] = useState<File[]>([]); // Move here
    const [imageDataList, setImageDataList] = useState<string[]>([]); // Move here
    const [searchParams, setSearchParams] = useSearchParams();
    const [fakeLoading, setFakeLoading] = useState(false);

    const actionAlert = useStore(workbenchStore.alert);
    const { autoSelectTemplate, contextOptimizationEnabled } = useSettings();
    const { showChat, isStreaming, estimationsProject } = useStore(chatStore);
    const [animationScope] = useAnimate();
    const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
    const { enhancingPrompt, promptEnhanced, enhancePrompt, resetEnhancer } = usePromptEnhancer();
    const { parsedMessages, parseMessages } = useMessageParser(isStreaming);
    const lastConstructedMessage = useRef<any>(null);
    const sessionStore = SessionStore.getInstance();
    const previewErros = useRef<PreviewError[]>([]);

    const handlePrevewErros = debounce(() => {
      previewErros.current;
      append({
        createdAt: new Date(),
        role: 'user',
        content: 'Biela has detected some error with the preview, and will try to rectify it for you.',
        reasoning: JSON.stringify(previewErros.current),
      });
    }, 5000);

    const TEXTAREA_MAX_HEIGHT = chatStarted ? 400 : 200;

    const customUseChatFetch = async (_: RequestInfo | URL, init?: RequestInit<CfProperties>): Promise<Response> => {
      const sessionData = await sessionStore.getSessionData();

      const pingUrl = `${sessionData.host}/ping`;
      const streamUrl = `${sessionData.host}/ai/stream`;

      const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

      const waitForPing = async (pingUrl: string, maxRetries = 60, intervalMs = 1000) => {
        for (let i = 0; i < maxRetries; i++) {
          try {
            const res = await fetch(pingUrl, { method: 'GET', credentials: 'include' });

            if (res.ok) {
              return true;
            }
          } catch (err) {
            // ignore, might just be server not ready yet
          }
          await wait(intervalMs);
        }
        throw new Error(`Ping failed after ${maxRetries} attempts`);
      };

      await waitForPing(pingUrl);

      return await fetch(streamUrl, { ...init, credentials: 'include' });
    };

    useEffect(() => {
      setChatStarted(!!initialMessages?.length);

      if (initialMessages.length > 0 && !estimationsProject) {
        const projectSlug = sessionStore.getSlug();
        let estResponse;
        setTimeout(async () => {
          estResponse = await backendApiFetch(`/user-projects/${projectSlug}/all-data`);

          const data = await estResponse.json();
          const formatedData = {
            timeMetrics: {
              traditional: data.estimations.estimatedTimeTraditional,
              actual: data.timeSpentAi,
            },
            estimatedCost: {
              traditional: data.estimations.estimatedCostTraditional,
              tokens: data.totalTokensCost,
            },
            estimations: {
              confidenceScore: data.estimations.confidenceScore,
              estimatedCostTraditional: data.estimations.estimatedCostTraditional,
              estimatedTimeTraditional: data.estimations.estimatedTimeTraditional,
              estimatedNumberOfDevelopers: data.estimations.estimatedNumberOfDevelopers,
              recommendedDeveloperLevel: data.estimations.recommendedDeveloperLevel,
              timeToMarket: data.estimations.timeToMarket,
              maintenanceCostPercentage: data.estimations.maintenanceCostPercentage,
              projectType: data.estimations.projectType,
              projectComplexity: data.estimations.projectComplexity,
              uniqueComponentCount: data.estimations.uniqueComponentCount,
              featureCount: data.estimations.featureCount,
              rangeOfUncertainty: data.estimations.rangeOfUncertainty,
              keyTechnologies: data.estimations.keyTechnologies,
              breakdown: data.estimations.breakdown,
            },
          };
          setEstimationsData(formatedData);
        }, 3000);
      }
    }, [initialMessages]);

    const { messages, isLoading, input, handleInputChange, setInput, stop, append } = useChat({
      fetch: customUseChatFetch,
      body: {
        // todo: delete files from here
        files: {
          iFiles: [],
          uFiles: [],
        },
        mode: chatStore.get().mode,
        contextOptimization: contextOptimizationEnabled,
        projectSlug: sessionStore.getSlug(),
        dynamicReasoning: localStorage.getItem('extendedThinkingMode') || 'first-response',
        model: localStorage.getItem(AI_MODEL_STORAGE_KEY) || AI_MODELS[0].value,
      },
      sendExtraMessageFields: true,
      onError: (error) => {
        logger.error('Request failed\n\n', error);

        let parsedError;

        if (error.message && typeof error.message === 'string' && error.message.includes('Not enough tokens')) {
          handleError(error);
          return;
        }

        try {
          parsedError = JSON.parse(error.message);
        } catch (e) {
          if (tokensLoaded && remainingTokens === 0) {
            setShowTokenCalculator(true);
          } else {
            toast.error('Our AI is pretty busy right now, please try again in a minute');
          }
        }

        if (parsedError.message === `AI stream error for project ${workbenchStore.getSlug()}: Prompt is too long`) {
          setShowPromptTooLongDialog(true);
        } else if (
          parsedError.message &&
          typeof parsedError.message === 'string' &&
          parsedError.message.includes('Not enough tokens')
        ) {
          handleError(parsedError);
        } else if (
          error.message.includes('ERR_HTTP2_PROTOCOL_ERROR') ||
          error.message.includes('Failed to fetch') ||
          error.message.includes('NetworkError') ||
          error.message.includes('network')
        ) {
          toast.error('Our AI is pretty busy right now, please try again in a minute');
        } else {
          toast.error(
            'There was an error processing your request: ' +
              (error.message ? error.message : 'No details were returned'),
          );
        }
      },
      onResponse: () => {
        sessionStore.onStreamStart.set(new Date());
      },
      onFinish: (_, response) => {
        const projectSlug = sessionStore.getSlug();
        let estResponse;
        if (!estimationsProject) {
          setTimeout(async () => {
            estResponse = await backendApiFetch(`/user-projects/${projectSlug}/all-data`);
            const data = await estResponse.json();
            const formatedData = {
              timeMetrics: {
                traditional: data.estimations.estimatedTimeTraditional,
                actual: data.timeSpentAi,
              },
              estimatedCost: {
                traditional: data.estimations.estimatedCostTraditional,
                tokens: data.totalTokensCost,
              },
              estimations: {
                confidenceScore: data.estimations.confidenceScore,
                estimatedCostTraditional: data.estimations.estimatedCostTraditional,
                estimatedTimeTraditional: data.estimations.estimatedTimeTraditional,
                estimatedNumberOfDevelopers: data.estimations.estimatedNumberOfDevelopers,
                recommendedDeveloperLevel: data.estimations.recommendedDeveloperLevel,
                timeToMarket: data.estimations.timeToMarket,
                maintenanceCostPercentage: data.estimations.maintenanceCostPercentage,
                projectType: data.estimations.projectType,
                projectComplexity: data.estimations.projectComplexity,
                uniqueComponentCount: data.estimations.uniqueComponentCount,
                featureCount: data.estimations.featureCount,
                rangeOfUncertainty: data.estimations.rangeOfUncertainty,
                keyTechnologies: data.estimations.keyTechnologies,
                breakdown: data.estimations.breakdown,
              },
            };
            setEstimationsData(formatedData);
          }, 3000);
        }
        sessionStore.onStreamEnd.set(new Date());

        // @TO_BE_REMOVED after Webcontainer update
        setIsModifiedCode(true);

        const usage = response?.usage;

        if (usage) {
          logger.debug('Token usage:', usage);

          // Check if Extended Thinking is enabled to alert the user when the usage is high (100k prompt tokens)
          const extendedThinkingMode = localStorage.getItem('extendedThinkingMode');

          if (
            usage.promptTokens > EXTENDED_THINKING_TOKEN_THRESHOLD &&
            (extendedThinkingMode === 'always' || extendedThinkingMode === 'first-response')
          ) {
            toast.warning(
              'You are using Extended Thinking with a very large prompt. Frequent use of this feature with large prompts may significantly increase your usage and costs.',
            );
          }
        }

        lastConstructedMessage.current = null;
        logger.debug('Finished streaming');
      },
      initialMessages,
      initialInput: Cookies.get(PROMPT_COOKIE_KEY) || '',
    });

    function setFirstUserMessageCookie(messages: Message[]) {
      if (!Array.isArray(messages) || messages.length === 0) {
        return;
      }

      const existingCookie = document.cookie.split('; ').find((row) => row.startsWith('first-user-message='));

      const cookieValue = existingCookie ? decodeURIComponent(existingCookie.split('=')[1]) : null;

      const firstUserMessage = messages.find((m) => m.role === 'user');

      if (!firstUserMessage || !firstUserMessage.content) {
        return;
      }

      if (cookieValue !== firstUserMessage.content) {
        document.cookie = `first-user-message=${encodeURIComponent(
          firstUserMessage.content,
        )}; path=/; max-age=${60 * 60 * 24 * 365}`;
      }
    }

    useEffect(() => {
      if (messages && messages.length) {
        setFirstUserMessageCookie(messages);
      }
    }, [messages.length]);

    const handleClose = () => {
      setShowPromptTooLongDialog(false);

      lastConstructedMessage.current = null;
    };

    const handleModelSwitch = (modelValue: string) => {
      if (lastConstructedMessage.current) {
        localStorage.setItem(AI_MODEL_STORAGE_KEY, modelValue);

        const msgToResend = { ...lastConstructedMessage.current };
        lastConstructedMessage.current = null;
        stop();
        setTimeout(() => {
          append({
            role: 'user',
            content:
              typeof msgToResend.content === 'string' ? msgToResend.content : JSON.stringify(msgToResend.content),
            id: Date.now().toString(),
          });
        }, 300);
      }
    };

    useEffect(() => {
      // Changed this from 'prompt' to 'chat-prompt' because google oauth returned a 'prompt' query and it would open up the chat, sending gibberish to the llm
      const prompt = searchParams.get('chat-prompt');

      if (!prompt) {
        return;
      }

      setSearchParams({});
      runAnimation();
      append({
        role: 'user',
        content: [
          {
            type: 'text',

            // [Model: ${model}]\n\n[Provider: ${provider.name}]\n\n
            text: `${prompt}`,
          },
        ] as any, // Type assertion to bypass compiler check
      });
    }, [searchParams]);

    useEffect(() => {
      chatStore.setKey('started', !!initialMessages.length);

      const storedApiKeys = Cookies.get('apiKeys');

      if (storedApiKeys) {
        setApiKeys(JSON.parse(storedApiKeys));
      }

      const listener = (event: MessageEvent<PreviewError>) => {
        // Get the message data from the event object
        const message = event.data;

        // Process the message depending on its type
        if (message.type === 'error') {
          previewErros.current.push(event.data);
          handlePrevewErros();
        }
      };

      window.addEventListener('message', listener);

      return () => window.removeEventListener('message', listener);
    }, []);

    useEffect(() => {
      if (isStreaming !== isLoading) {
        setIsStreaming(isLoading);
      }

      if (!messages.length) {
        return;
      }

      processSampledMessages({
        messages,
        initialMessages,
        isLoading,
        parseMessages,
        storeMessageHistory,
      });
    }, [JSON.stringify(messages), isLoading]);

    useEffect(() => {
      const textarea = textareaRef.current;

      if (textarea) {
        textarea.style.height = 'auto';

        const scrollHeight = textarea.scrollHeight;

        textarea.style.height = `${Math.min(scrollHeight, TEXTAREA_MAX_HEIGHT)}px`;
        textarea.style.overflowY = scrollHeight > TEXTAREA_MAX_HEIGHT ? 'auto' : 'hidden';
      }
    }, [input, textareaRef]);

    useEffect(() => {
      if (!actionAlert) {
        return () => void 0;
      }

      // Run and store timeouts for cleanup
      const logTimeout = ingestErrorLogs(actionAlert);
      const fixErrorTimeout = tryToFixError(actionAlert);

      return () => {
        clearTimeout(logTimeout);

        // Only clear fixErrorTimeout if it’s truthy
        if (fixErrorTimeout) {
          return;
        }

        clearTimeout(fixErrorTimeout);
      };
    }, [actionAlert]);

    const scrollTextArea = () => {
      const textarea = textareaRef.current;

      if (textarea) {
        textarea.scrollTop = textarea.scrollHeight;
      }
    };

    const abort = () => {
      stop();
      chatStore.setKey('aborted', true);
      workbenchStore.abortAllActions();
    };

    const runAnimation = async () => {
      if (chatStarted) {
        return;
      }

      /*
       * await Promise.all([
       *   animate('#examples', { opacity: 0, display: 'none' }, { duration: 0.1 }),
       *   animate('#intro', { opacity: 0, flex: 1 }, { duration: 0.2, ease: cubicEasingFn }),
       * ]);
       */

      chatStore.setKey('started', true);

      setChatStarted(true);
    };

    const sendMessageWrapper = (originalSendMessage: typeof sendMessage) => {
      return (event: UIEvent, messageInput?: string, messageOptions?: JSONValue) => {
        if (messageInput === 'Create a database for my project') {
          append({
            role: 'user',
            content: 'Creating the connection with the database',
          } as Message);

          originalSendMessage(event, messageInput, messageOptions);
        } else {
          originalSendMessage(event, messageInput, messageOptions);
        }
      };
    };

    const handleBuyTokens = async (tokens: string): Promise<void> => {
      await createCheckoutSession({
        mode: 'payment',
        tokens,
      });
    };

    const [remainingTokens, setRemainingTokens] = useState<number>(0);
    const [showTokenCalculator, setShowTokenCalculator] = useState<boolean>(false);
    const [dismissedNoTokensDialog, setDismissedNoTokensDialog] = useState(false);
    const [tokensLoaded, setTokensLoaded] = useState(false);

    const tokensStoreCallback = (data: TokensSession | null) => {
      setTokensLoaded(true);

      if (data) {
        setRemainingTokens(data.remainingTokens);
      }
    };

    const handleError = (error: any) => {
      if (error.message && error.message.includes('Not enough tokens')) {
        setShowTokenCalculator(true);
      }
    };

    const tokensStore = TokensStore.getInstance(tokensStoreCallback);

    async function refreshTokens() {
      const data = await tokensStore.refreshTokensData();
      tokensStoreCallback(data || null);
    }

    const { getUser } = useUser();
    const user = getUser();

    useEffect(() => {
      const wasInit = remainingTokens !== null;

      if ((!isStreaming || !wasInit) && user) {
        refreshTokens();
      }
    }, [isStreaming]);

    if (remainingTokens === null) {
      return null;
    }

    useEffect(() => {
      if (remainingTokens !== null && remainingTokens <= 0) {
        setDismissedNoTokensDialog(false);
      }
    }, [remainingTokens]);

    const sendMessage = async (_event: UIEvent, messageInput?: string, messageOptions?: JSONValue) => {
      const _input = messageInput || input;

      workbenchStore.userSelectedFileManually = false;

      if (_input.length === 0 || isLoading) {
        return;
      }

      /**
       * @note (delm) Usually saving files shouldn't take long but it may take longer if there
       * many unsaved files. In that case we need to block user input and show an indicator
       * of some kind so the user is aware that something is happening. But I consider the
       * happy case to be no unsaved files and I would expect users to save their changes
       * before they send another message.
       */

      chatStore.setKey('aborted', false);

      runAnimation();

      if (!chatStarted && messageInput && autoSelectTemplate) {
        setFakeLoading(true);
        setMessages([
          {
            id: `${new Date().getTime()}`,
            role: 'user',
            content: [
              {
                type: 'text',

                // [Model: ${model}]\n\n[Provider: ${provider.name}]
                text: `${_input}`,
              },
              ...imageDataList.map((imageData) => ({
                type: 'image',
                image: imageData,
              })),
            ] as any, // Type assertion to bypass compiler check
          },
        ]);

        // reload();

        const { template, title } = await selectStarterTemplate({
          message: messageInput,
        });

        if (template !== 'blank') {
          const temResp = await getTemplates(template, title).catch((e) => {
            if (e.message.includes('rate limit')) {
              toast.warning('Rate limit exceeded. Skipping starter template\n Continuing with blank template');
            } else {
              toast.warning('Failed to import starter template\n Continuing with blank template');
            }

            return null;
          });

          if (temResp) {
            const { assistantMessage, userMessage } = temResp;

            setMessages([
              {
                id: `${new Date().getTime()}`,
                role: 'user',
                content: messageInput,

                // annotations: ['hidden'],
              },
              {
                id: `${new Date().getTime()}`,
                role: 'assistant',
                content: assistantMessage,
              },
              {
                id: `${new Date().getTime()}`,
                role: 'user',

                // [Model: ${model}]\n\n[Provider: ${provider.name}]\n\n
                content: `${userMessage}`,
                annotations: ['hidden'],
              },
            ]);

            reload();
            setFakeLoading(false);

            return;
          } else {
            setMessages([
              {
                id: `${new Date().getTime()}`,
                role: 'user',
                content: [
                  {
                    type: 'text',

                    // [Model: ${model}]\n\n[Provider: ${provider.name}]\n\n
                    text: `${_input}`,
                  },
                  ...imageDataList.map((imageData) => ({
                    type: 'image',
                    image: imageData,
                  })),
                ] as any, // Type assertion to bypass compiler check
              },
            ]);
            reload();
            setFakeLoading(false);

            return;
          }
        } else {
          setMessages([
            {
              id: `${new Date().getTime()}`,
              role: 'user',
              content: [
                {
                  type: 'text',

                  // [Model: ${model}]\n\n[Provider: ${provider.name}]\n\n
                  text: `${_input}`,
                },
                ...imageDataList.map((imageData) => ({
                  type: 'image',
                  image: imageData,
                })),
              ] as any, // Type assertion to bypass compiler check
            },
          ]);
          reload();
          setFakeLoading(false);

          return;
        }
      }

      const newMessage = {
        role: 'user',
        content: _input,
        ...(imageDataList?.length > 0 && {
          experimental_attachments: imageDataList.map((imageData) => ({
            contentType: 'image',
            url: imageData,
          })),
        }),
        data: messageOptions,
      };

      // Store this message for potential resending
      lastConstructedMessage.current = newMessage;

      append(newMessage as Message, {
        ...(imageDataList?.length > 0 && {
          experimental_attachments: imageDataList.map((imageData) => ({
            contentType: 'image',
            url: imageData,
          })),
        }),
        body: {
          messages: [newMessage],
        },
      });

      setInput('');
      Cookies.remove(PROMPT_COOKIE_KEY);

      // Add file cleanup here
      setUploadedFiles([]);
      setImageDataList([]);

      resetEnhancer();

      textareaRef.current?.blur();
    };

    /**
     * Handles the change event for the textarea and updates the input state.
     * @param event - The change event from the textarea.
     */
    const onTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
      handleInputChange(event);
    };

    /**
     * Debounced function to cache the prompt in cookies.
     * Caches the trimmed value of the textarea input after a delay to optimize performance.
     */
    const debouncedCachePrompt = useCallback(
      debounce((event: React.ChangeEvent<HTMLTextAreaElement>) => {
        const trimmedValue = event.target.value.trim();
        Cookies.set(PROMPT_COOKIE_KEY, trimmedValue, { expires: 30 });
      }, 1000),
      [],
    );

    const [messageRef, scrollRef] = useSnapScroll();

    function ingestErrorLogs(actionAlert: any) {
      return setTimeout(() => {
        const projectSlug = workbenchStore.getSlug();
        logStore.ingestLog('user workbench alert', 'error', 'workbench', actionAlert, projectSlug);
      }, 1000);
    }

    const alertTimestampsRef = useRef<number[]>([]); // Keep timestamps persistent
    const alertMinutesInterval = 1;
    const alertFixThresholdInInterval = 3;

    function formatError(jsonData: any) {
      const errorObj = typeof jsonData === 'string' ? JSON.parse(jsonData) : { ...jsonData };

      if (errorObj.content && typeof errorObj.content === 'string') {
        const lines = errorObj.content.split('\n');
        const truncatedLines = lines.slice(0, 5);
        errorObj.content = truncatedLines.join('\n');
      }

      return `\`\`\`sh\n${errorObj.content}\n\`\`\``;
    }

    function tryToFixError(actionAlert: any) {
      if (!actionAlert || !actionAlert.size) {
        return null;
      }

      const now = Date.now();
      const xMinutesAgo = now - alertMinutesInterval * 60 * 1000;

      // Remove timestamps older than `alertMinutesInterval`
      alertTimestampsRef.current = alertTimestampsRef.current.filter((timestamp) => timestamp >= xMinutesAgo);

      if (alertTimestampsRef.current.length < alertFixThresholdInInterval) {
        alertTimestampsRef.current.push(now); // Record the new attempt

        return setTimeout(() => {
          const errorMessage = `Fix error: ${formatError(actionAlert)}`;
          sendMessage({} as UIEvent, errorMessage, { hideUserImage: true });

          // Delay clearing alert to ensure `sendMessage` completes
          setTimeout(() => {
            workbenchStore.clearAlert();
          }, 100);
        }, 0);
      } else {
        // Rate limit exceeded: Skipping alert handling
        return null;
      }
    }

    function extractTitle(content: string): string | null {
      const match = content.match(/data-message-id="([^"]+)"/);

      if (match && match[1]) {
        const messageId = match[1];
        const artifacts = workbenchStore.artifacts.get();
        const artifact = artifacts[messageId];

        if (artifact && artifact.title) {
          return artifact.title;
        }
      }

      return null;
    }

    useEffect(() => {
      const getCookie = (name: string) => {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);

        if (parts.length === 2) {
          return decodeURIComponent(parts.pop()!.split(';').shift()!);
        }

        return null;
      };

      const promptValue = getCookie('project_prompt');

      if (promptValue) {
        setChatStarted(true);
        setInput(promptValue);
        sendMessage({} as UIEvent, promptValue);
        document.cookie = 'project_prompt=; path=/; domain=biela.dev; expires=Thu, 01 Jan 1970 00:00:00 UTC;';
      }
    }, []);

    return (
      <Wrapper handleSendMessage={sendMessage} isStreaming={isLoading} handleStop={abort} footerOff={chatStarted} chatStarted={chatStarted}>
        <div
          style={{
            backgroundImage: 'url(/hero-2.png)',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'top',
            backgroundSize: '100%',
            backgroundColor: '#0A1730',
          }}
          className={`chat-page  relative flex flex-col ${!chatStarted ? 'min-h-[900px]' : 'h-screen'} w-screen `}
        >
          <div className={'background-biela'}>
            <BackgroundLinesComponent />
          </div>
          {/* Animated background elements */}
          <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
            <div className="absolute top-10 left-10 w-64 h-64 bg-purple-600/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 bg-cyan-600/10 rounded-full blur-3xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-emerald-600/5 rounded-full blur-3xl"></div>
          </div>
          <BaseChat
            ref={animationScope}
            textareaRef={textareaRef}
            input={input}
            showChat={showChat}
            chatStarted={chatStarted}
            isStreaming={isLoading || fakeLoading}
            enhancingPrompt={enhancingPrompt}
            promptEnhanced={promptEnhanced}
            sendMessage={sendMessageWrapper(sendMessage)}
            messageRef={messageRef}
            scrollRef={scrollRef}
            handleInputChange={(e) => {
              onTextareaChange(e);
              debouncedCachePrompt(e);
            }}
            handleStop={abort}
            description={description}
            importChat={importChat}
            exportChat={exportChat}
            messages={messages.map((message, i) => {
              if (message.role === 'user') {
                return message;
              }

              const title = extractTitle(parsedMessages[i] || '');

              return {
                ...message,
                content: parsedMessages[i] || '',
                title,
              };
            })}
            enhancePrompt={() => {
              enhancePrompt(
                input,
                imageDataList,
                (input) => {
                  setInput(input);
                  scrollTextArea();
                },

                /*
                 * model, // use specific LLM model (string)
                 * provider, // use specific LLM provider (string)
                 */
                apiKeys,
              );

              setUploadedFiles([]);
              setImageDataList([]);
            }}
            uploadedFiles={uploadedFiles}
            setUploadedFiles={setUploadedFiles}
            imageDataList={imageDataList}
            setImageDataList={setImageDataList}
            actionAlert={actionAlert}
            clearAlert={() => workbenchStore.clearAlert()}
          />

          <ErrorTooLongDialog
            isOpen={showPromptTooLongDialog}
            onClose={() => {
              setShowPromptTooLongDialog(false);
            }}
            onModelSwitch={handleModelSwitch}
          />
          <ErrorRunOutOfTokensDialog
            isOpen={tokensLoaded && ((remainingTokens === 0 && !dismissedNoTokensDialog) || showTokenCalculator)}
            onBuyTokens={handleBuyTokens}
            onClose={() => {
              handleClose();
              setShowTokenCalculator(false);
              setDismissedNoTokensDialog(true);
            }}
          />
        </div>
      </Wrapper>
    );
  },
);
