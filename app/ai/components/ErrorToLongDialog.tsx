import React, { FC } from 'react';
import * as RadixDialog from '@radix-ui/react-dialog';
import { motion, type Variants } from 'framer-motion';
import { useTranslation } from 'react-i18next';

interface ErrorTooLongDialogProps {
  isOpen: boolean;
  onEdit: () => void;
  onClose: () => void;
}

const transition = {
  duration: 0.15,
  ease: [0.4, 0, 0.2, 1],
};

const backdropVariants: Variants = {
  closed: {
    opacity: 0,
    transition,
  },
  open: {
    opacity: 1,
    transition,
  },
};

const dialogVariants: Variants = {
  closed: {
    x: '-50%',
    y: '-40%',
    scale: 0.96,
    opacity: 0,
    transition,
  },
  open: {
    x: '-50%',
    y: '-50%',
    scale: 1,
    opacity: 1,
    transition,
  },
};

const ErrorTooLongDialog: FC<ErrorTooLongDialogProps> = ({ isOpen, onEdit, onClose }) => {
  const { t } = useTranslation('translation');

  return (
    <RadixDialog.Root open={isOpen}>
      <RadixDialog.Portal>
        <RadixDialog.Overlay asChild>
          <motion.div
            className="bg-black/50 fixed inset-0 z-40"
            initial="closed"
            animate="open"
            exit="closed"
            variants={backdropVariants}
            onClick={onClose}
          />
        </RadixDialog.Overlay>

        <RadixDialog.Content asChild>
          <motion.div
            onClick={(e) => e.stopPropagation()} // previne închiderea când dai click în interior
            className="fixed top-[50%] left-[50%] z-max max-h-[85vh] w-[90vw] max-w-[600px] translate-x-[-50%] translate-y-[-50%] border border-biela-elements-borderColor rounded-lg bg-biela-elements-background-depth-2 shadow-lg focus:outline-none overflow-hidden modal-delete-chat"
            style={{borderRadius: '12px', border: '2px solid rgba(0, 0, 0, 0.00)', background: '#111727'}}
            initial="closed"
            animate="open"
            exit="closed"
            variants={dialogVariants}
          >
            {/* Decorative ellipse */}
            <div
              style={{
                borderRadius: '847px',
                // background: 'linear-gradient(rgb(242, 1, 0) 50.04%, rgb(3, 4, 4) 140.18%)',
                filter: 'blur(9px)',
                width: '94%',
                margin: '0 auto',
                height: '5px',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
              }}
            />

            <div className="">
              <div className="flex justify-center">
              </div>
              <div>
                <div className="grid justify-items-center md:flex items-center gap-4 xl:gap-2">
                  <h2 className="px-6 py-5 flex items-center justify-between text-white text-xl ">
                    {t('LotOfContext', ' Whoa, that’s a lot of context!')}
                  </h2>
                </div>
                <div className="border-b border-t border-gray-700/50 mb-6 px-6 py-5 text-gray-400 ">
                  <p>
                    {t('LotOfContextDescription1', 'It looks like the AI has hit its processing limit for this project. Most of the space is being used by the imported files, not the chat itself. Reimporting the project as a new one will clear the chat history and free up just enough space for a few more prompts — but keep in mind, the files will still take up most of the available context.')}
                  </p>
                  <br/>
                  <p>
                    {t('LotOfContextDescription2', 'To keep things running smoothly, try reimporting now for a fresh start.')}
                  </p>
                </div>

              </div>
            </div>

            <div className="px-6 pb-6 bg-bolt-elements-background-depth-2">
              <button
                onClick={onEdit}
                className="inline-flex items-center justify-center rounded-lg px-5 py-5 md:px-6 md:py-4 text-lg text-white leading-none focus:outline-none border border-[#374151] md:min-w-[190px] w-full md:w-auto bg-[#FF554A] hover:bg-biela-elements-button-danger-backgroundHover border-none"
              >
                {t('tryAgain', ' Try Again')}
              </button>
            </div>
          </motion.div>
        </RadixDialog.Content>
      </RadixDialog.Portal>
    </RadixDialog.Root>
  );
};

export default ErrorTooLongDialog;
