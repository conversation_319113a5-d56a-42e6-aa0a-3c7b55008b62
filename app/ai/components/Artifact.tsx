import { useStore } from '@nanostores/react';
import { AnimatePresence, motion } from 'framer-motion';
import { computed } from 'nanostores';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { type BundledLanguage, type BundledTheme, createHighlighter, type HighlighterGeneric } from 'shiki';
import type { ActionState } from '~/ai/lib/runtime/action-runner';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { classNames } from '~/utils/classNames';
import { cubicEasingFn } from '~/utils/easings';
import { WORK_DIR } from '~/utils/constants';
import { chatStore } from '~/ai/lib/stores/chat';
import { PlayIcon } from 'lucide-react';
import { toast } from 'react-toastify';

const highlighterOptions = {
  langs: ['shell'],
  themes: ['light-plus', 'dark-plus'],
};

const shellHighlighter: HighlighterGeneric<BundledLanguage, BundledTheme> =
  import.meta.hot?.data.shellHighlighter ?? (await createHighlighter(highlighterOptions));

if (import.meta.hot) {
  import.meta.hot.data.shellHighlighter = shellHighlighter;
}

interface ArtifactProps {
  messageId: string;
}

export const Artifact = memo(({ messageId }: ArtifactProps) => {
  const userToggledActions = useRef(false);
  const [showActions, setShowActions] = useState(false);
  const [allActionFinished, setAllActionFinished] = useState(false);
  const artifacts = useStore(workbenchStore.artifacts);
  const artifact = artifacts[messageId];
  const actions = useStore(computed(artifact.runner.actions, (actions) => Object.values(actions)));
  const { showPrompt } = useStore(chatStore);

  useEffect(() => {
    setShowActions(showPrompt);
  }, [showPrompt]);

  useMemo(() => {
    if (!actions.length) {
      return;
    }

    if (!showActions && !userToggledActions.current) {
      setShowActions(true);
    }

    const finished = actions.every((action) => action.status === 'complete');
    if (artifact.type === 'bundled' && allActionFinished !== finished) {
      setAllActionFinished(finished);
    }
  }, [JSON.stringify(actions)]);

  useMemo(() => {
    if (!actions.length) {
      return;
    }

    const isRunning = actions.some((action) => action.status === 'running');
    if (isRunning || chatStore.get().isStreaming) {
      return;
    }

    if (workbenchStore.currentView.get() !== 'preview') {
      workbenchStore.currentView.set('preview');
    }
  }, [chatStore.get().isStreaming]);

  return (
    <div className="artifact flex rounded-lg flex-col overflow-hidden w-full transition-border duration-150">
      <div className="flex">
        <button className="flex items-center bg-transparent w-full overflow-hidden gap-1">
          {artifact.type == 'bundled' && (
            <>
              <div>
                {allActionFinished ? (
                  <div className={'i-ph:files-light'} style={{ fontSize: '22px' }}></div>
                ) : (
                  <div className={'i-svg-spinners:90-ring-with-bg spinner'} style={{ fontSize: '2rem' }}></div>
                )}
              </div>
              <div className="bg-biela-elements-artifacts-borderColor w-[1px]" />
            </>
          )}
          <div className="w-full text-left">
            <div className="text-sm font-extralight text-white/90 pb-2">
              {artifact?.title}
            </div>
          </div>
        </button>
      </div>
      <AnimatePresence>
        {artifact.type !== 'bundled' && showPrompt && showActions && actions.length > 0 && (
          <motion.div
            className="actions"
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: '0px' }}
            transition={{ duration: 0.15 }}
          >
            <div className="text-left">
              <ActionList actions={actions} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

interface ShellCodeBlockProps {
  className?: string;
  code: string;
  runCommand?: boolean;
}

function ShellCodeBlock({ className, code, runCommand }: ShellCodeBlockProps) {
  const [expendCommand, setExpendCommand] = useState<boolean>(false);
  const clickTimeoutRef = useRef<number | null>(null);

  const handleClick = () => {
    if (!runCommand) return;

    if (clickTimeoutRef.current) {
      // A venit al doilea click: double-click
      clearTimeout(clickTimeoutRef.current);
      clickTimeoutRef.current = null;
      setExpendCommand(prev => !prev);
    } else {
      // Primul click: setăm timer-ul pentru a detecta dacă vine un al doilea click
      clickTimeoutRef.current = window.setTimeout(() => {
        // Nu a venit al doilea click în intervalul de 250ms => single click
        navigator.clipboard.writeText(code).then(() => {
          toast.success('Copied to clipboard');
          console.log('Command copied to clipboard:', code);
        });
        clickTimeoutRef.current = null;
      }, 250);
    }
  };

  return (
    <div
      onClick={handleClick}
      className={classNames(
        'text-xs',
        className,
        expendCommand ? 'show-multi-lines' : '',
        runCommand ? 'run-command' : ''
      )}
      dangerouslySetInnerHTML={{
        __html: shellHighlighter.codeToHtml(code, {
          lang: 'shell',
          theme: 'dark-plus',
        }),
      }}
    ></div>
  );
}


interface ActionListProps {
  actions: ActionState[];
}

const actionVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

function openArtifactInWorkbench(filePath: string) {
  workbenchStore.currentView.set('code');
  workbenchStore.setSelectedFile(`${WORK_DIR}/${filePath}`, true);
}

function normalizePath(filePath: string, baseDir: string) {
  return filePath.replace(baseDir, '');
}

function getBaseDir(path: string) {
  const srcIndex = path.indexOf('src/');
  return srcIndex !== -1 ? path.substring(0, srcIndex) : '/home/<USER>/';
}

// ─────────────────────────────────────────────────────────────────────────────
// Aici începe partea relevantă pentru “Run command” cu single-click & double-click
// ─────────────────────────────────────────────────────────────────────────────

const ActionList = memo(({ actions }: ActionListProps) => {
  const [fileStates, setFileStates] = useState<{ [key: string]: boolean }>({});
  const files = useStore(workbenchStore.files);
  const initialFilesRef = useRef<Set<string>>(new Set());
  const [createdFiles, setCreatedFiles] = useState<Set<string>>(new Set());
  useMemo(() => {
    if (!files && !actions.length) {
      return;
    }

    if (initialFilesRef.current.size === 0) {
      initialFilesRef.current = new Set(Object.keys(files).map((key) => normalizePath(key, getBaseDir(key))));
    }

    const newFileStates: { [key: string]: boolean } = {};

    for (const action of actions) {
      if (action.type !== 'file') {
        continue;
      }

      const normalizedPath = normalizePath(action.filePath, getBaseDir(action.filePath));
      const isCreated = createdFiles.has(normalizedPath);
      const isInitial = initialFilesRef.current.has(normalizedPath);

      newFileStates[action.filePath] = !isCreated && isInitial;

      if (!isCreated && !isInitial) {
        setCreatedFiles((prev) => new Set(prev.add(normalizedPath)));
      }
    }

    setFileStates(newFileStates);
  }, [files, JSON.stringify(actions), createdFiles]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.15 }}
    >
      <ul className="list-none space-y-2.5 ">
        {actions.map((action, idx) => {
          const { status, type, content } = action;
          const actionLabel = type === 'file' && fileStates[action.filePath] ? 'Update' : 'Create';
          localStorage.setItem('isBouncing', 'true');

          return (
            <motion.li
              key={(action.type === 'file' && action.filePath) || (action.type === 'migration' && action.path) || idx}
              variants={actionVariants}
              initial="hidden"
              animate="visible"
              transition={{
                duration: 0.2,
                ease: cubicEasingFn,
              }}
            >
              <div className="gap-1.5 text-sm">
                <div
                  className={classNames(
                    'flex items-center gap-2 rounded-md ',
                    type === 'start' || type === 'shell' || type === 'unit_testing_shell'
                      ? 'flex items-center gap-2 mb-2 text-white'
                      : 'bg-white/5 px-2 py-1.5',
                  )}
                >
                  <div className={classNames('text-lg', getIconColor(status))}>
                    {status === 'running' ? (
                      <>
                        {type === 'start' ? (
                          <PlayIcon className="w-4 h-4 text-white/70" />
                        ) : (
                          <div className="i-svg-spinners:90-ring-with-bg spinner w-[16px] h-[16px]"></div>
                        )}
                      </>
                    ) : status === 'pending' ? (
                      <div className="i-ph:circle-duotone w-[16px] h-[16px]"></div>
                    ) : status === 'complete' ? (
                      <>
                        {type === 'start' || type === 'shell' || type === 'unit_testing_shell' ? (
                          <PlayIcon className="w-4 h-4 text-white/70" />
                        ) : (
                          <div className="i-ph:check w-[16px] h-[16px]"></div>
                        )}
                      </>
                    ) : status === 'failed' || status === 'aborted' ? (
                      <div className="i-ph:x w-[16px] h-[16px]"></div>
                    ) : null}
                  </div>

                  {type === 'file' ? (
                    <div className="create-text flex items-center gap-2 text-sm font-extralight text-white/70">
                      {actionLabel}{' '}
                      <code
                        className={classNames([
                          'bg-white/5 px-2 py-0.5 rounded text-xs font-mono text-white/50',
                          'hover:underline cursor-pointer',
                        ])}
                        onClick={() => openArtifactInWorkbench(action.filePath)}
                      >
                        {action.filePath}
                      </code>
                    </div>
                  ) : type === 'shell' || type === 'unit_testing_shell' ? (
                    // Aici am pus onClick & double-click logic (timer)
                    <div className="flex items-center w-full text-sm text-white/70 font-extralight cursor-pointer">
                      <span className="flex-1 font-extralight ">Run command</span>
                    </div>
                  ) : type === 'start' ? (
                    <a
                      onClick={(e) => {
                        e.preventDefault();
                        workbenchStore.currentView.set('preview');
                      }}
                      className="flex items-center w-full text-sm !text-white/70"
                    >
                      <span className="flex-1">Start Application</span>
                    </a>
                  ) : type === 'migration' ? (
                    <div className="flex items-center w-full text-sm text-white/70">
                      <span className="flex-1">
                        Run migration
                        {action.migrationTitle && (
                          <>
                            : <span>{action.migrationTitle}</span>
                          </>
                        )}
                      </span>
                    </div>
                  ) : null}
                </div>
              </div>

              {/* Afișăm blocul cu cod doar dacă e tip shell / unit_testing_shell / start */}
              {/* DAR îl afișăm doar dacă e expandat (pentru shell) sau mereu (pentru start) */}
              {type === 'start' && <ShellCodeBlock className="mt-1" code={content} />}
              {(type === 'shell' || type === 'unit_testing_shell') && (
                <ShellCodeBlock runCommand={true} className="mt-1" code={content} />
              )}
            </motion.li>
          );
        })}
      </ul>
    </motion.div>
  );
});
ActionList.displayName = 'ActionList';

// Funcție care decide culoarea iconiței în funcție de status
function getIconColor(status: ActionState['status']) {
  switch (status) {
    case 'pending': {
      return 'text-biela-elements-textTertiary';
    }
    case 'running': {
      return 'text-biela-elements-loader-progress';
    }
    case 'complete': {
      return 'text-biela-elements-icon-success';
    }
    case 'aborted': {
      return 'text-biela-elements-textSecondary';
    }
    case 'failed': {
      return 'text-biela-elements-icon-error';
    }
    default: {
      return undefined;
    }
  }
}
