import React, { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowRightIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { useChatOpen } from '../lib/context/chatOpenContext';

const CHANNEL_NAME = 'single-tab-channel';
const ACTIVE_TAB_KEY = 'active_tab';

interface SingleTabEnforcerProps {
  children: ReactNode;
  userEmail?: string; // Add this line
}

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  duration: number;
  delay: number;
}

export default function SingleTabEnforcer({ children, userEmail }: SingleTabEnforcerProps): JSX.Element {
  if (
    userEmail === '<EMAIL>' ||
    userEmail === '<EMAIL>' ||
    userEmail === '<EMAIL>'
  ) {
    return <>{children}</>;
  }

  const { t } = useTranslation('translation');
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [isHovering, setIsHovering] = useState<boolean>(false);
  const [particleCount] = useState<number>(15);
  const [particles, setParticles] = useState<Particle[]>([]);
  const tabId = useMemo(() => Math.random().toString(36).substring(2, 10), []);
  const channelRef = useRef<BroadcastChannel | null>(null);

  const { isChatOpen } = useChatOpen();

  useEffect(() => {
    const newParticles: Particle[] = Array.from({ length: particleCount }).map((_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 1,
      duration: Math.random() * 20 + 10,
      delay: Math.random() * 5,
    }));
    setParticles(newParticles);
  }, [particleCount]);

  useEffect(() => {
    const channel = new BroadcastChannel(CHANNEL_NAME);
    channelRef.current = channel;
    let hasResponded = false;

    const claimTab = () => {
      const current = localStorage.getItem(ACTIVE_TAB_KEY);
      if (!current && isChatOpen) {
        localStorage.setItem(ACTIVE_TAB_KEY, tabId);
        channel.postMessage({ type: 'active-tab', id: tabId });
        setActiveTab(tabId);
      }
    };

    const handleMessage = (event: MessageEvent) => {
      const { type, id } = event.data;

      if (type === 'active-tab') {
        hasResponded = true;
        setActiveTab(id);
      }

      if (type === 'tab-exists') {
        const current = localStorage.getItem(ACTIVE_TAB_KEY);
        if (current === tabId) {
          channel.postMessage({ type: 'active-tab', id: tabId });
        }
      }
    };

    channel.addEventListener('message', handleMessage);

    if (isChatOpen) {
      channel.postMessage({ type: 'tab-exists', id: tabId });

      const current = localStorage.getItem(ACTIVE_TAB_KEY);
      if (!hasResponded && !current) {
        claimTab();
      }
    }

    const handleUnload = () => {
      const current = localStorage.getItem(ACTIVE_TAB_KEY);
      if (current === tabId) {
        localStorage.removeItem(ACTIVE_TAB_KEY);
        channel.postMessage({ type: 'active-tab', id: null });
      }
    };

    window.addEventListener('beforeunload', handleUnload);

    return () => {
      channel.removeEventListener('message', handleMessage);
      channel.close();
      window.removeEventListener('beforeunload', handleUnload);
    };
  }, [tabId, isChatOpen]);

  const handleUseHere = () => {
    if (channelRef.current) {
      channelRef.current.postMessage({ type: 'active-tab', id: tabId });
      setActiveTab(tabId);
      localStorage.setItem(ACTIVE_TAB_KEY, tabId);
      window.location.reload();
    }
  };

  const shouldShowOverlay =
    isChatOpen && activeTab !== null && activeTab !== tabId && localStorage.getItem(ACTIVE_TAB_KEY) !== tabId;

  return (
    <>
      {shouldShowOverlay && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          style={{
            backgroundImage: 'url("/hero-bg-shade-empty.png")',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 20, delay: 0.2 }}
            className="w-full max-w-md rounded-xl border border-[#4ADE80]/20 bg-[#1A1F2E] overflow-hidden relative"
          >
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              {particles.map((particle) => (
                <motion.div
                  key={particle.id}
                  className="absolute rounded-full bg-[#4ADE80]"
                  style={{
                    left: `${particle.x}%`,
                    top: `${particle.y}%`,
                    width: `${particle.size}px`,
                    height: `${particle.size}px`,
                    opacity: 0.2,
                  }}
                  animate={{ y: [0, -100], opacity: [0, 0.5, 0] }}
                  transition={{
                    duration: particle.duration,
                    repeat: Infinity,
                    delay: particle.delay,
                    ease: 'linear',
                  }}
                />
              ))}
            </div>

            <motion.div
              className="absolute inset-0 rounded-xl"
              animate={{
                boxShadow: isHovering
                  ? [
                      '0 0 0 1px rgba(74, 222, 128, 0.3), 0 0 15px 2px rgba(74, 222, 128, 0.2)',
                      '0 0 0 1px rgba(74, 222, 128, 0.3), 0 0 20px 3px rgba(74, 222, 128, 0.3)',
                      '0 0 0 1px rgba(74, 222, 128, 0.3), 0 0 15px 2px rgba(74, 222, 128, 0.2)',
                    ]
                  : '0 0 0 1px rgba(74, 222, 128, 0.2), 0 0 10px 1px rgba(74, 222, 128, 0.1)',
              }}
              transition={{
                duration: 2,
                repeat: isHovering ? Infinity : 0,
                repeatType: 'reverse',
              }}
            />

            <div className="p-8 relative z-10">
              <motion.div
                className="flex items-center justify-center mb-6"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.4, type: 'spring' }}
              >
                <MoonIcon className="w-16 h-16 text-[#4ADE80]" />
              </motion.div>

              <motion.h2
                className="text-2xl font-medium text-white text-center mb-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                {t('inactiveTitle', 'This tab is inactive')}
              </motion.h2>

              <motion.p
                className="text-white/80 text-center mb-8 leading-relaxed"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
              >
                {t('inactiveDescription', 'Click the button below to make this the active tab and continue using the app.')}
              </motion.p>

              <motion.div
                className="flex items-center justify-center"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                <motion.button
                  onMouseEnter={() => setIsHovering(true)}
                  onMouseLeave={() => setIsHovering(false)}
                  onClick={handleUseHere}
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                  className="px-6 py-3 bg-gradient-to-r from-[#4ADE80] to-[#22D3EE] text-black font-medium rounded-lg flex items-center gap-2 transition-all duration-300"
                >
                  <span>{t('inactiveButton', 'Use this tab')}</span>
                  <ArrowRightIcon className="w-4 h-4" />
                </motion.button>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {!shouldShowOverlay && children}
    </>
  );
}
