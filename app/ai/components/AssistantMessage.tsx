import { memo } from 'react';
import { Markdown } from './Markdown';
import { ReasoningModal } from './ReasoningModal';
import type { JSONValue } from 'ai';

interface AssistantMessageProps {
  content: string;
  annotations?: JSONValue[];
  reasoning?: string;
}

export const AssistantMessage = memo(({ content, annotations, reasoning }: AssistantMessageProps) => {
  return (
    <div className="overflow-hidden w-full">
      {reasoning && (
        <ReasoningModal
          reasoning={reasoning}
        />
      )}
      <Markdown html>{content}</Markdown>
    </div>
  );
});
