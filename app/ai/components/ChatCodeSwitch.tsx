import { useStore } from '@nanostores/react';
import { memo } from 'react';
import { chatStore, setChatMode } from '~/ai/lib/stores/chat';
import { ChatCodeSlider, type ChatCodeSliderOptions } from '~/ai/components/ChatCodeSlider';

const modeOptions: ChatCodeSliderOptions<'chat' | 'code'> = {
  left: {
    value: 'chat',
    text: 'Chat',
  },
  right: {
    value: 'code',
    text: 'Code',
  },
};

interface ChatCodeSwitchProps {
  activeTab: string;
  changeTabToChat: () => void;
  disableLayoutAnimation?: boolean;
}

export const ChatCodeSwitch = memo(({ activeTab, changeTabToChat, disableLayoutAnimation = false }: ChatCodeSwitchProps) => {
  const mode = useStore(chatStore).mode;

  return <ChatCodeSlider
    isOtherTabActive={activeTab !== 'chat'}
    selected={mode}
    options={modeOptions}
    disableLayoutAnimation={disableLayoutAnimation}
    setChatCodeSelected={(mode) => {
    changeTabToChat()
    setChatMode(mode)
  }} />;
});
