import { backendApiFetch } from '~/ai/lib/backend-api';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { getCachedAppState, setAppData } from './cloud-storage';

export async function getAppState(cached?: boolean) {
  if (cached) {
    const projectSlug = workbenchStore.getSlug();
    const cachedData = getCachedAppState(projectSlug);

    if (cachedData) {
      return cachedData;
    }
  }

  const projectSlug = workbenchStore.getSlug();

  const url = `/cloud/app/${projectSlug}`;

  if (projectSlug.includes('undefined')) {
    return null;
  }

  try {
    const response = await backendApiFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'wallet-token': localStorage.getItem('wallet-token') || '',
      },
    });

    const data = await response.json();

    // console.log('app status:', data);

    setAppData(projectSlug, data);

    return data;
  } catch (error) {
    if (error instanceof Error && error.message.includes('No logged-in user found')) {
      return null;
    }

    console.error('Error backendApiFetching app state:/', error);

    return null;
  }
}

export async function deploy() {
  try {
    const { content, projectSlug } = await workbenchStore.getFilesZip();

    if (content) {
      const formData = new FormData();
      formData.append('file', content, `${projectSlug}.zip`);

      const url = `/cloud/app/${projectSlug}/deploy`;

      const response = await backendApiFetch(url, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      setAppData(projectSlug, data);
    } else {
      console.error('Error deploying project: No files to deploy');
      alert('Failed to deploy project. Please try again later.');
    }
  } catch (error) {
    console.error('Error deploying project:', error instanceof Error ? error.message : String(error));
    alert('Failed to deploy project. Please try again later.');
  }
}
