import { expirationSettings, UserSession } from '~/types/user';
import {
  confirmUserEmail,
  doRefresh,
  getUserByToken,
  handleGitHubLogin,
  handleGoogleLogin,
  handlePaypalLogin,
  loginUser,
  logoutUser,
  resendVerificationEmail
} from './user';
import { add, isAfter, isBefore } from 'date-fns';

/**
 * Singleton class for managing user session state.
 */
export class UserStore {
  /**
   * Singleton UserStore instance
   */
  private static _instance: UserStore;

  /**
   * Existing user session.
   */
  private _user: UserSession | null = null;

  /**
   * Google state
   */
  private _googleState: string | null = null;

  /**
   * Promise to track ongoing token refresh operations.
   * This prevents multiple concurrent refresh attempts within the same tab.
   */
  private _refreshPromise: Promise<void> | null = null;

  /**
   * Key used in localStorage to implement a cross-tab locking mechanism.
   * This prevents concurrent token refresh attempts across multiple browser tabs.
   */
  private _refreshLockKey = 'token_refresh_lock';

  /**
   * Maximum time that a refresh lock can be held.
   * If a tab holds the lock longer than this timeout, other tabs will assume the lock is stale
   * and attempt to acquire it. This prevents deadlocks if a tab crashes while holding the lock.
   */
  private _refreshLockTimeout = 10000;
  private readonly _setUserCallback: (user: UserSession | null) => void;

  /**
   * Creates an instance of UserStore.
   * @param {function(UserSession | null): void} setUserCallback - Callback to update React state.
   */
  constructor(setUserCallback: (user: UserSession | null) => void) {
    this._setUserCallback = setUserCallback;
  }

  private getCookieDomain(): string {
    const hostname = window.location.hostname;

    if (hostname === 'localhost') {
      return '';
    }

    const domainName = import.meta.env.CUSTOM_DOMAIN_NAME || 'biela.dev';

    return '.' + domainName;
  }

  private setCookie(name: string, value: string, expirationDays: number): void {
    const domain = this.getCookieDomain();
    const expires = new Date();
    expires.setDate(expires.getDate() + expirationDays);

    let cookieString = `${name}=${value}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;

    if (domain) {
      cookieString += `; domain=${domain}`;
    }

    if (typeof document !== 'undefined') {
      document.cookie = cookieString;
    }
  }

  private getCookie(name: string): string | null {
    let cookies = '';

    if (typeof document !== 'undefined') {
      cookies = document.cookie.split(';');
    }

    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();

      if (cookie.startsWith(name + '=')) {
        return cookie.substring(name.length + 1);
      }
    }

    return null;
  }

  private deleteCookie(name: string): void {
    const domain = this.getCookieDomain();
    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax`;

    if (domain) {
      cookieString += `; domain=${domain}`;
    }

    if (typeof document !== 'undefined') {
      document.cookie = cookieString;
    }
  }

  /**
   * Sets the current user session and updates localStorage.
   * @param {UserSession | null} user - The user session to set.
   */
  setUser = (user: UserSession | null) => {
    this._user = user;

    if (user?.token) {
      localStorage.setItem('token', user.token);
    }

    if (user?.refreshToken) {
      localStorage.setItem('refreshToken', user.refreshToken);
      this.setCookie('user-cookie', user.refreshToken, expirationSettings.refreshTokenExpirationDays);
    }

    if (user) {
      localStorage.setItem('user', JSON.stringify(user));
    } else {
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      this.deleteCookie('user-cookie');
    }

    this._setUserCallback(user); // This updates React's state via the callback
  };

  /**
   * Retrieves the current user session.
   * @returns {UserSession | null} The current user session.
   */
  getUser = (): UserSession | null => {
    if (this._user) {
      return this._user;
    }

    let localUser: UserSession | null = null;

    try {
      localUser = JSON.parse(localStorage.getItem('user') as string);
      // eslint-disable-next-line
    } catch (_) {
      // ignore
    }

    return localUser;
  };

  /**
   * Set the current google state and update localStorage
   * @param {string | null} state - The state to set.
   */
  setGoogleState = (state: string | null) => {
    this._googleState = state;

    localStorage.setItem('googleState', state ? state : '');
  };

  /**
   * Retrieves the current google state
   */
  getGoogleState = (): string | null => {
    if (this._googleState) {
      return this._googleState;
    }

    let localGoogleState: string | null = null;

    try {
      localGoogleState = localStorage.getItem('googleState') as string;
    } catch (_) {
      // ignore
    }

    return localGoogleState;
  };

  /**
   * Sets expiration timestamps for the user's token and refresh token.
   * @param {UserSession} user - The user session to update.
   */
  setUserExpiration = (user: UserSession) => {
    const tokenExpiresAt = new Date(),
      refreshTokenExpiresAt = new Date();

    user.tokenExpiresAt = add(tokenExpiresAt, {
      /*
       * minutes: expirationSettings.tokenExpirationMinutes,
       * temp fix
       */
      days: 7,
    });
    user.refreshTokenExpiresAt = add(refreshTokenExpiresAt, {
      days: expirationSettings.refreshTokenExpirationDays,
    });

    this.setUser(user);
  };

  /**
   * Retrieves the authentication token.
   * @returns {string | null} The token, if available.
   */
  getToken = (): string | null => this.getUser()?.token ?? localStorage.getItem('token');

  /**
   * Retrieves the refresh token.
   * @returns {string | null} The refresh token, if available.
   */
  getRefreshToken = (): string | null => {
    const cookieRefreshToken = this.getCookie('user-cookie');

    return cookieRefreshToken;
  };

  /**
   * Checks if a user is logged in.
   * @returns {boolean} True if a user session exists, false otherwise.
   */
  isLoggedIn = (): boolean => {
    const hasRefreshCookie = !!this.getCookie('user-cookie');

    if (hasRefreshCookie) {
      return true;
    }

    return !!this.getUser();
  };

  /**
   * Logs in a user with provided credentials.
   * @param {{ username: string; password: string;}} formData - The login credentials.
   * @throws {Error} If username or password is missing.
   */
  login = async (formData: { username: string; password: string }, turnstileToken: string) => {
    const { username, password } = formData;

    if (!username || !password || !turnstileToken) {
      throw new Error('Missing username, password or Turnstile token');
    }

    if (this.isLoggedIn()) {
      return;
    }

    const loggedUser = await loginUser({ username, password }, turnstileToken);

    this.setUserExpiration(loggedUser as UserSession);
  };

  /**
   * Handles GitHub OAuth login.
   * @param {string} code - The GitHub authorization code.
   * @throws {Error} If the code is missing.
   */
  githubLogin = async (code: string) => {
    if (!code) {
      throw new Error('Missing GitHub code for login.');
    }
    if (this.isLoggedIn()) {
      return;
    }

    try {
      const loggedUser = await handleGitHubLogin(code);

      this.setUserExpiration(loggedUser as UserSession);
    } catch (error) {
      window.location.href = '/login';
    }
  };

  paypalLogin = async (code: string) => {
    if (!code) {
      throw new Error('Missing Paypal code for login.');
    }

    if (this.isLoggedIn()) {
      return;
    }

    try {
      const loggedUser = await handlePaypalLogin(code);

      this.setUserExpiration(loggedUser as UserSession);
    } catch (error) {
      window.location.href = '/login';
    }
  };

  /**
   * Handles Google OAuth login.
   * @param {string} code - The Google authorization code.
   * @param {string} state - The random generated state
   * @throws {Error} If the code is missing.
   */
  googleLogin = async (code: string, state: string) => {
    if (!code) {
      throw new Error('Missing Google code for login.');
    }
    if (!state) {
      throw new Error('Missing Google state for login.');
    }

    const localGoogleState = this.getGoogleState();

    if (localGoogleState != state) {
      throw new Error('Provided state is invalid');
    }

    if (this.isLoggedIn()) {
      return;
    }

    // User Store si local storage stocam state ul si dupa il verif si sterg
    try {
      const loggedUser = await handleGoogleLogin(code);

      this.setUserExpiration(loggedUser as UserSession);
    } catch (error) {
      window.location.href = '/login';
    }
  };

  /**
   * Logs out the current user and clears session data.
   */
  logout = async () => {
    await logoutUser();
    this.setUser(null);
  };

  /**
   * Confirms user email using a token.
   * @param {string} token - The email confirmation token.
   * @throws {Error} If the token is missing.
   */
  confirmEmail = async (token: string) => {
    if (!token) {
      throw new Error('Missing required data to validate email.');
    }

    const loggedUser = await confirmUserEmail(token);

    this.setUserExpiration(loggedUser);
  };

  /**
   * Resends confirmation email using username or email.
   * @param {string} usernameOrEmail - The user's email or username.
   * @throws {Error} If the request fails.
   */
  resendConfirmation = async (payload: { email?: string; username?: string }): Promise<void> => {
    const { email, username } = payload;

    if (!email && !username) {
      throw new Error('Username or email is required to resend confirmation.');
    }

    const filteredPayload: { email?: string; username?: string } = {};

    if (email) {
      filteredPayload.email = email;
    }

    if (username) {
      filteredPayload.username = username;
    }

    await resendVerificationEmail(filteredPayload);
  };

  /**
   * Retrieves the current user refresh token promise.
   * @returns {Promise<void> | null} The current user session.
   */
  getRefreshPromise = (): Promise<void> | null => this._refreshPromise;

  /**
   * Sets the current user refresh token promise with a new one.
   * @param {Promise<void>} promise - The promise to be set.
   */
  setRefreshPromise = (promise: Promise<void>) => {
    this._refreshPromise = promise;
  };

  private authenticateWithRefreshToken = async (refreshToken: string): Promise<void> => {
    try {
      const refreshedUser = await doRefresh(refreshToken);

      if (refreshedUser) {
        this.setUserExpiration(refreshedUser as UserSession);
      }
    } catch (error) {
      console.error('Authentication with refresh token failed:', error);
      throw error;
    }
  };

  /**
   * Refreshes the user's authentication token.
   * @returns {Promise<UserSession | null>} The refreshed user session.
   */
  refreshToken = async (): Promise<UserSession | null> => {
    const token = this.getRefreshToken();

    if (!token) {
      return null;
    }

    return await doRefresh(token);
  };

  /**
   * Authenticates the stored token and retrieves the user session.
   * @returns {Promise<void>}
   */
  authenticateToken = async (): Promise<void> => {
    const token = this.getToken();

    if (!token) {
      const refreshToken = this.getCookie('user-cookie');

      if (refreshToken) {
        try {
          await this.authenticateWithRefreshToken(refreshToken);
        } catch (error) {
          this.logout();
        }
      }

      return;
    }

    const user = await getUserByToken(token);

    this.setUserExpiration(user);
  };

  /**
   * Returns the singleton instance of UserStore.
   * @param {function(UserSession | null): void} [setUserCallback] - Optional callback to update React state.
   * @returns {UserStore} The UserStore instance.
   */
  static getInstance(setUserCallback?: (user: UserSession | null) => void): UserStore {
    if (!UserStore._instance && setUserCallback) {
      UserStore._instance = new UserStore(setUserCallback);
    }

    return UserStore._instance;
  }
}

/**
 * Creates a new instance of UserStore.
 * @param {function(UserSession | null): void} setUserCallback - Callback to update React state.
 * @returns {UserStore} A new UserStore instance.
 */
export const createUserStore = (setUserCallback: (user: UserSession | null) => void): UserStore =>
  new UserStore(setUserCallback);
