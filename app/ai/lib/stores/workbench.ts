import { atom, map, type MapStore, type Readable<PERSON>tom, type WritableAtom } from 'nanostores';
import type { EditorDocument, ScrollPosition } from '~/ai/components/CodeMirrorEditor';
import { ActionRunner } from '~/ai/lib/runtime/action-runner';
import type { ActionCallbackData, ArtifactCallbackData } from '~/ai/lib/runtime/message-parser';
import { webcontainer } from '~/ai/lib/webcontainer';
import type { ITerminal } from '~/types/terminal';
import { unreachable } from '~/utils/unreachable';
import { EditorStore } from './editor';
import { type FileMap, FilesStore } from './files';
import { PreviewsStore } from './previews';
import { TerminalStore } from './terminal';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { Octokit, type RestEndpointMethodTypes } from '@octokit/rest';
import * as nodePath from 'node:path';
import { extractRelativePath } from '~/utils/diff';
import { chatId, chatTimestamp, description } from '~/ai/lib/persistence';
import { toast } from 'react-toastify';
import Cookies from 'js-cookie';
import { createSampler } from '~/utils/sampler';
import type { ActionAlert } from '~/types/actions';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import { WORK_DIR } from '~/utils/constants';
import ignore from 'ignore';

export interface ArtifactState {
  id: string;
  title: string;
  type?: string;
  closed: boolean;
  runner: ActionRunner;
}

export type ArtifactUpdateState = Pick<ArtifactState, 'title' | 'closed'>;

type Artifacts = MapStore<Record<string, ArtifactState>>;

export type WorkbenchViewType = 'code' | 'preview' | 'settings';

export class WorkbenchStore {
  #previewsStore = new PreviewsStore(webcontainer);
  #filesStore = new FilesStore(webcontainer);
  #editorStore = new EditorStore();
  #terminalStore = new TerminalStore(webcontainer);

  #reloadedMessages = new Set<string>();

  artifacts: Artifacts = import.meta.hot?.data.artifacts ?? map({});

  showWorkbench: WritableAtom<boolean> = import.meta.hot?.data.showWorkbench ?? atom(false);
  currentView: WritableAtom<WorkbenchViewType> = import.meta.hot?.data.currentView ?? atom('code');
  unsavedFiles: WritableAtom<Set<string>> = import.meta.hot?.data.unsavedFiles ?? atom(new Set<string>());
  actionAlert: WritableAtom<ActionAlert | undefined> =
    import.meta.hot?.data.unsavedFiles ?? atom<ActionAlert | undefined>(undefined);
  modifiedFiles = new Set<string>();
  artifactIdList: string[] = [];
  #globalExecutionQueue = Promise.resolve();

  #lastGeneratedFilePath?: string;
  userSelectedFileManually = false;

  constructor() {
    if (import.meta.hot) {
      import.meta.hot.data.artifacts = this.artifacts;
      import.meta.hot.data.unsavedFiles = this.unsavedFiles;
      import.meta.hot.data.showWorkbench = this.showWorkbench;
      import.meta.hot.data.currentView = this.currentView;
      import.meta.hot.data.actionAlert = this.actionAlert;
    }
  }

  addToExecutionQueue(callback: () => Promise<void>) {
    this.#globalExecutionQueue = this.#globalExecutionQueue.then(() => callback());
  }

  get previews() {
    return this.#previewsStore.previews;
  }

  get files() {
    return this.#filesStore.files;
  }

  get currentDocument(): ReadableAtom<EditorDocument | undefined> {
    return this.#editorStore.currentDocument;
  }

  get selectedFile(): ReadableAtom<string | undefined> {
    return this.#editorStore.selectedFile;
  }

  get firstArtifact(): ArtifactState | undefined {
    return this.#getArtifact(this.artifactIdList[0]);
  }

  get showTerminal() {
    return this.#terminalStore.showTerminal;
  }

  get bielaTerminal() {
    return this.#terminalStore.bielaTerminal;
  }

  get bielaUnitTestingTerminal() {
    return this.#terminalStore.bielaUnitTestingTerminal;
  }

  get alert() {
    return this.actionAlert;
  }

  async executeScriptCommands(
    scriptName: 'start' | 'stop' | 'restart' | 'test' | 'build',
    fallback?: () => Promise<void>,
  ) {
    let scripts;

    try {
      scripts = await this.#filesStore.findFile('.scripts.json');
    } catch (error) {
      console.error('Error finding scripts:', error);
    }

    if (!scripts) {
      if (fallback) {
        try {
          await fallback();
        } catch (error) {
          console.error('Error executing fallback:', error);
        }
      }

      return;
    }

    const { always, ...rest } = JSON.parse(scripts.content);

    const commands = [...always, ...rest[scriptName]];

    if (commands.length === 0) {
      if (fallback) {
        try {
          await fallback();
        } catch (error) {
          console.error('Error executing fallback:', error);
        }
      }

      return;
    }

    for (const command of commands) {
      try {
        await this.bielaTerminal.executeCommand(`${Date.now()}`, command, () => {});
      } catch (error) {
        console.error('Error executing command:', error);
      }
    }
  }

  async restartBielaTerminalServer() {
    await this.executeScriptCommands('restart', () => this.startBielaTerminalServer());
  }

  async startBielaTerminalServer() {
    await this.executeScriptCommands('start', async () => {
      try {
        await this.bielaTerminal.executeCommand(`${Date.now()}`, 'npm install', () => {});
      } catch (error) {
        console.error('Error starting biela terminal:', error);
      }

      try {
        await this.bielaTerminal.executeCommand(`${Date.now()}`, 'npm run dev', () => {});
      } catch (error) {
        console.error('Error starting biela terminal:', error);
      }
    });
  }
  async installDependenciesBielaTerminalServer() {
    await this.executeScriptCommands('start', async () => {
      try {
        await this.bielaTerminal.executeCommand(`${Date.now()}`, 'npm install', () => {});
      } catch (error) {
        console.error('Error starting biela terminal:', error);
      }
    });
  }
  async buildProjectBielaTerminalServer() {
    await this.executeScriptCommands('start', async () => {
      try {
        await this.bielaTerminal.executeCommand(`${Date.now()}`, 'npm run build', () => {});
      } catch (error) {
        console.error('Error starting biela terminal:', error);
      }
    });
  }
  async startProjectBielaTerminalServer() {
    await this.executeScriptCommands('start', async () => {
      try {
        await this.bielaTerminal.executeCommand(`${Date.now()}`, 'npm run dev', () => {});
      } catch (error) {
        console.error('Error starting biela terminal:', error);
      }
    });
  }

  async stopBielaTerminalServer() {
    await this.executeScriptCommands('stop', async () => {
      try {
        await this.bielaTerminal.executeCommand(`${Date.now()}`, '', () => {});
      } catch (error) {
        console.error('Error stopping biela terminal:', error);
      }
    });
  }

  clearAlert() {
    this.actionAlert.set(undefined);
  }

  toggleTerminal(value?: boolean) {
    this.#terminalStore.toggleTerminal(value);
  }

  attachTerminal(terminal: ITerminal) {
    this.#terminalStore.attachTerminal(terminal);
  }

  attachbielaTerminal(terminal: ITerminal) {
    this.#terminalStore.attachbielaTerminal(terminal);
  }

  attachUnitTestingTerminal(terminal: ITerminal) {
    this.#terminalStore.attachBielaUnitTestingTerminal(terminal);
  }

  onTerminalResize(cols: number, rows: number) {
    this.#terminalStore.onTerminalResize(cols, rows);
  }

  setDocuments(files: FileMap) {
    this.#editorStore.setDocuments(files);

    if (this.#filesStore.filesCount > 0 && this.currentDocument.get() === undefined) {
      // we find the first file and select it
      for (const [filePath, dirent] of Object.entries(files)) {
        if (dirent?.type === 'file') {
          this.setSelectedFile(filePath);
          break;
        }
      }
    }
  }

  setShowWorkbench(show: boolean) {
    this.showWorkbench.set(show);
  }

  setCurrentDocumentContent(newContent: string) {
    const filePath = this.currentDocument.get()?.filePath;

    if (!filePath) {
      return;
    }

    const originalContent = this.#filesStore.getFile(filePath)?.content;
    const unsavedChanges = originalContent !== undefined && originalContent !== newContent;

    this.#editorStore.updateFile(filePath, newContent);

    const currentDocument = this.currentDocument.get();

    if (currentDocument) {
      const previousUnsavedFiles = this.unsavedFiles.get();

      if (unsavedChanges && previousUnsavedFiles.has(currentDocument.filePath)) {
        return;
      }

      const newUnsavedFiles = new Set(previousUnsavedFiles);

      if (unsavedChanges) {
        newUnsavedFiles.add(currentDocument.filePath);
      } else {
        newUnsavedFiles.delete(currentDocument.filePath);
      }

      this.unsavedFiles.set(newUnsavedFiles);
    }
  }

  setCurrentDocumentScrollPosition(position: ScrollPosition) {
    const editorDocument = this.currentDocument.get();

    if (!editorDocument) {
      return;
    }

    const { filePath } = editorDocument;

    this.#editorStore.updateScrollPosition(filePath, position);
  }

  setSelectedFile(filePath: string | undefined, manual = false) {
    if (manual) {
      if (filePath && filePath === this.#lastGeneratedFilePath) {
        this.userSelectedFileManually = false;
      } else {
        this.userSelectedFileManually = true;
      }
    }

    this.#editorStore.setSelectedFile(filePath);
  }

  async saveFile(filePath: string) {
    const documents = this.#editorStore.documents.get();
    const document = documents[filePath];

    if (document === undefined) {
      return;
    }

    await this.#filesStore.saveFile(filePath, document.value);

    const newUnsavedFiles = new Set(this.unsavedFiles.get());
    newUnsavedFiles.delete(filePath);

    this.unsavedFiles.set(newUnsavedFiles);
  }

  async saveCurrentDocument() {
    const currentDocument = this.currentDocument.get();

    if (currentDocument === undefined) {
      return;
    }

    await this.saveFile(currentDocument.filePath);
  }

  resetCurrentDocument() {
    const currentDocument = this.currentDocument.get();

    if (currentDocument === undefined) {
      return;
    }

    const { filePath } = currentDocument;
    const file = this.#filesStore.getFile(filePath);

    if (!file) {
      return;
    }

    this.setCurrentDocumentContent(file.content);
  }

  async saveAllFiles() {
    for (const filePath of this.unsavedFiles.get()) {
      await this.saveFile(filePath);
    }
  }

  getFileModifications() {
    return this.#filesStore.getFileModifications();
  }

  resetAllFileModifications() {
    this.#filesStore.resetFileModifications();
  }

  abortAllActions() {
    // TODO: what do we wanna do and how do we wanna recover from this?
  }

  setReloadedMessages(messages: string[]) {
    this.#reloadedMessages = new Set(messages);
  }

  addArtifact({ messageId, title, id, type }: ArtifactCallbackData) {
    const artifact = this.#getArtifact(messageId);

    if (artifact) {
      return;
    }

    if (!this.artifactIdList.includes(messageId)) {
      this.artifactIdList.push(messageId);
    }

    this.artifacts.setKey(messageId, {
      id,
      title,
      closed: false,
      type,
      runner: new ActionRunner(
        webcontainer,
        () => this.bielaTerminal,
        () => this.bielaUnitTestingTerminal,
        (alert) => {
          if (this.#reloadedMessages.has(messageId)) {
            return;
          }

          this.actionAlert.set(alert);
        },
      ),
    });
  }

  updateArtifact({ messageId }: ArtifactCallbackData, state: Partial<ArtifactUpdateState>) {
    const artifact = this.#getArtifact(messageId);

    if (!artifact) {
      return;
    }

    this.artifacts.setKey(messageId, { ...artifact, ...state });
  }

  addAction(data: ActionCallbackData) {
    // this._addAction(data);

    this.addToExecutionQueue(() => this._addAction(data));
  }

  async _addAction(data: ActionCallbackData) {
    const { messageId } = data;

    const artifact = this.#getArtifact(messageId);

    if (!artifact) {
      unreachable('Artifact not found');
    }

    return artifact.runner.addAction(data);
  }

  runAction(data: ActionCallbackData, isStreaming: boolean = false) {
    if (isStreaming) {
      this.actionStreamSampler(data, isStreaming);
    } else {
      this.addToExecutionQueue(() => this._runAction(data, isStreaming));
    }
  }

  async _runAction(data: ActionCallbackData, isStreaming: boolean = false) {
    const { messageId } = data;

    const artifact = this.#getArtifact(messageId);

    if (!artifact) {
      unreachable('Artifact not found');
    }

    const action = artifact.runner.actions.get()[data.actionId];

    if (!action || action.executed) {
      return;
    }

    if (data.action.type === 'file') {
      const wc = await webcontainer;
      const fullPath = nodePath.join(wc.workdir, data.action.filePath);

      this.#lastGeneratedFilePath = fullPath;

      if (!this.userSelectedFileManually && this.selectedFile.value !== fullPath) {
        this.setSelectedFile(fullPath);
      }

      // if (this.currentView.value !== 'code') {
        // this.currentView.set('code');
      // }

      const doc = this.#editorStore.documents.get()[fullPath];

      if (!doc) {
        await artifact.runner.runAction(data, isStreaming);
      }

      this.#editorStore.updateFile(fullPath, data.action.content);

      if (!isStreaming) {
        await artifact.runner.runAction(data);
        this.resetAllFileModifications();
      }
    } else {
      await artifact.runner.runAction(data);
    }
  }

  actionStreamSampler = createSampler(async (data: ActionCallbackData, isStreaming: boolean = false) => {
    return await this._runAction(data, isStreaming);
  }, 100); // TODO: remove this magic number to have it configurable

  #getArtifact(id: string) {
    const artifacts = this.artifacts.get();
    return artifacts[id];
  }

  async downloadZip() {
    const zip = new JSZip();
    const files = this.files.get();

    // Get the project name from the description input, or use a default name
    const projectName = (description.value ?? 'project').toLocaleLowerCase().split(' ').join('_');

    // Generate a simple 6-character hash based on the current timestamp
    const timestampHash = Date.now().toString(36).slice(-6);
    const uniqueProjectName = `${projectName}_${timestampHash}`;

    for (const [filePath, dirent] of Object.entries(files)) {
      if (dirent?.type === 'file' && !dirent.isBinary) {
        const relativePath = extractRelativePath(filePath);

        // split the path into segments
        const pathSegments = relativePath.split('/');

        // if there's more than one segment, we need to create folders
        if (pathSegments.length > 1) {
          let currentFolder = zip;

          for (let i = 0; i < pathSegments.length - 1; i++) {
            currentFolder = currentFolder.folder(pathSegments[i])!;
          }
          currentFolder.file(pathSegments[pathSegments.length - 1], dirent.content);
        } else {
          // if there's only one segment, it's a file in the root
          zip.file(relativePath, dirent.content);
        }
      }
    }

    // Generate the zip file and save it
    const content = await zip.generateAsync({ type: 'blob' });
    saveAs(content, `${uniqueProjectName}.zip`);
  }

  async syncFiles(targetHandle: FileSystemDirectoryHandle) {
    const files = this.files.get();
    const syncedFiles = [];

    for (const [filePath, dirent] of Object.entries(files)) {
      if (dirent?.type === 'file' && !dirent.isBinary) {
        const relativePath = extractRelativePath(filePath);
        const pathSegments = relativePath.split('/');
        let currentHandle = targetHandle;

        for (let i = 0; i < pathSegments.length - 1; i++) {
          currentHandle = await currentHandle.getDirectoryHandle(pathSegments[i], { create: true });
        }

        // create or get the file
        const fileHandle = await currentHandle.getFileHandle(pathSegments[pathSegments.length - 1], {
          create: true,
        });

        // write the file content
        const writable = await fileHandle.createWritable();
        await writable.write(dirent.content);
        await writable.close();

        syncedFiles.push(relativePath);
      }
    }

    return syncedFiles;
  }

  async pushToGitHub(repoName: string, githubUsername?: string, ghToken?: string) {
    try {
      // Use cookies if username and token are not provided
      const githubToken = ghToken || Cookies.get('githubToken');
      const owner = githubUsername || Cookies.get('githubUsername');

      if (!githubToken || !owner) {
        throw new Error('GitHub token or username is not set in cookies or provided.');
      }

      // Initialize Octokit with the auth token
      const octokit = new Octokit({ auth: githubToken });

      // Check if the repository already exists before creating it
      let repo: RestEndpointMethodTypes['repos']['get']['response']['data'];

      try {
        const resp = await octokit.repos.get({ owner, repo: repoName });
        repo = resp.data;
      } catch (error) {
        if (error instanceof Error && 'status' in error && error.status === 404) {
          // Repository doesn't exist, so create a new one
          const { data: newRepo } = await octokit.repos.createForAuthenticatedUser({
            name: repoName,
            private: false,
            auto_init: true,
          });
          repo = newRepo;
        } else {
          console.log('cannot create repo!');
          throw error; // Some other error occurred
        }
      }

      // Get all files
      const files = this.files.get();

      if (!files || Object.keys(files).length === 0) {
        throw new Error('No files found to push');
      }

      // Create blobs for each file
      const blobs = await Promise.all(
        Object.entries(files).map(async ([filePath, dirent]) => {
          if (dirent?.type === 'file' && dirent.content) {
            const { data: blob } = await octokit.git.createBlob({
              owner: repo.owner.login,
              repo: repo.name,
              content: Buffer.from(dirent.content).toString('base64'),
              encoding: 'base64',
            });
            return { path: extractRelativePath(filePath), sha: blob.sha };
          }

          return null;
        }),
      );

      const validBlobs = blobs.filter(Boolean); // Filter out any undefined blobs

      if (validBlobs.length === 0) {
        throw new Error('No valid files to push');
      }

      // Get the latest commit SHA (assuming main branch, update dynamically if needed)
      const { data: ref } = await octokit.git.getRef({
        owner: repo.owner.login,
        repo: repo.name,
        ref: `heads/${repo.default_branch || 'main'}`, // Handle dynamic branch
      });
      const latestCommitSha = ref.object.sha;

      // Create a new tree
      const { data: newTree } = await octokit.git.createTree({
        owner: repo.owner.login,
        repo: repo.name,
        base_tree: latestCommitSha,
        tree: validBlobs.map((blob) => ({
          path: blob!.path,
          mode: '100644',
          type: 'blob',
          sha: blob!.sha,
        })),
      });

      // Create a new commit
      const { data: newCommit } = await octokit.git.createCommit({
        owner: repo.owner.login,
        repo: repo.name,
        message: 'Initial commit from your app',
        tree: newTree.sha,
        parents: [latestCommitSha],
      });

      // Update the reference
      await octokit.git.updateRef({
        owner: repo.owner.login,
        repo: repo.name,
        ref: `heads/${repo.default_branch || 'main'}`, // Handle dynamic branch
        sha: newCommit.sha,
      });

      // Success notification
      const message = `Repository created successfully: ${repo.html_url}`;
      toast(message, {
        type: 'success',
        position: 'bottom-right',
        autoClose: 3000,
      });
    } catch (error) {
      console.error('Error pushing to GitHub:', error instanceof Error ? error.message : String(error));
    }
  }

  async getFilesZip() {
    const zip = new JSZip();
    const files = this.files.get();

    const projectSlug = this.getSlug();

    if (files && Object.keys(files).length > 0) {
      for (const [filePath, dirent] of Object.entries(files)) {
        if (dirent?.type === 'file' && !dirent.isBinary) {
          const relativePath = extractRelativePath(filePath);

          // split the path into segments
          const pathSegments = relativePath.split('/');

          // if there's more than one segment, we need to create folders
          if (pathSegments.length > 1) {
            let currentFolder = zip;

            for (let i = 0; i < pathSegments.length - 1; i++) {
              currentFolder = currentFolder.folder(pathSegments[i])!;
            }
            currentFolder.file(pathSegments[pathSegments.length - 1], dirent.content);
          } else {
            // if there's only one segment, it's a file in the root
            zip.file(relativePath, dirent.content);
          }
        }
      }

      // Generate the zip file and save it
      const content = await zip.generateAsync({ type: 'blob' });

      return {
        projectSlug,
        content,
      };
    } else {
      return {
        projectSlug,
        content: null,
      };
    }
  }

  getChatMetadata() {
    const projectName = (description.value ?? 'project').toLocaleLowerCase().split(' ').join('_');
    return {
      chatId: chatId.value,
      chatTimestamp: chatTimestamp.value,
      description: description.value,
      projectName,
    };
  }

  getSlug() {
    const metadata = this.getChatMetadata();

    if (!metadata.chatId) {
      throw new Error('Chat ID is not set');
    }

    const userStore = UserStore.getInstance();
    const user = userStore.getUser();

    const uniqueProjectId = `${metadata.chatId}-${user?.id}`;

    return uniqueProjectId.toLowerCase();
  }

  async loadProjectFromZipBuffer(zipArrayBuffer: ArrayBuffer) {
    try {
      const typedArray = new Uint8Array(zipArrayBuffer);
      const zip = await JSZip.loadAsync(typedArray);

      const newFileMap: FileMap = {};
      const ignorePatterns = [
        'dist/**',
        'build/**',
        'node_modules/**',
        '.git/**',
        '.github/**',
        '**/*lock.json',
        '**/*lock.yaml',
      ];

      const ig = ignore().add(ignorePatterns);

      // Iterate over all files in the archive
      await Promise.all(
        Object.keys(zip.files).map(async (relativePath) => {
          const fileEntry = zip.files[relativePath];

          if (!fileEntry.dir && !ig.ignores(relativePath)) {
            const content = await fileEntry.async('string');

            newFileMap[`${WORK_DIR}/${relativePath}`] = {
              type: 'file',
              isBinary: false,
              content,
            };
          }
        }),
      );

      this.#filesStore.files.set(newFileMap);

      const filePaths = Object.keys(newFileMap);

      this.setDocuments(newFileMap);

      this.unsavedFiles.set(new Set(filePaths));

      await this.saveAllFiles();

      this.resetAllFileModifications();

      localStorage.setItem('fileTreeData', JSON.stringify(newFileMap));

      if (filePaths.length) {
        this.setSelectedFile(filePaths[0]);
      }

      return newFileMap;
    } catch (error) {
      console.error('Error loading downloaded project:', error);

      return null;
    }
  }
}

export const workbenchStore = new WorkbenchStore();
