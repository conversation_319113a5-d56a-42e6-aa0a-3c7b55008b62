import { map, MapStore } from 'nanostores';

interface ChatStore {
  started: boolean;
  aborted: boolean;
  showChat: boolean;
  showPrompt: boolean;
  checkingList: boolean;
  errorDetails: boolean;
  cleaningProject: boolean;
  projectImportName: string;
  mode: 'chat' | 'code';
  isStreaming: boolean;
  isModifiedCode: boolean;
  errorTracking: {
    content: string | null;
    type: string | null;
    autoSendCount: number;
    showAlert: boolean;
  };
}

export const chatStore: MapStore<ChatStore> = map({
  started: false,
  aborted: false,
  showChat: true,
  showPrompt: true,
  checkingList: false,
  errorDetails: false,
  cleaningProject: false,
  projectImportName: "",
  mode: 'code' as 'chat' | 'code',
  isStreaming: false,
  isModifiedCode: true,
  errorTracking: {
    content: null,
    type: null,
    autoSendCount: 0,
    showAlert: false
  },
});

export function setChatMode(mode: 'chat' | 'code') {
  chatStore.setKey('mode', mode);
}

export function setIsStreaming(isStreaming: boolean) {
  chatStore.setKey('isStreaming', isStreaming);
}

export function setShowChat(showChat: boolean) {
  chatStore.setKey('showChat', showChat);
}

export function setShowPrompt(showPrompt: boolean) {
  chatStore.setKey('showPrompt', showPrompt);
}

export function setCheckingList(checkingList: boolean) {
  chatStore.setKey('checkingList', checkingList);
}

export function setCleaningProject(cleaningProject: boolean) {
  chatStore.setKey('cleaningProject', cleaningProject);
}
export function setErrorDetails(errorDetails: boolean) {
  chatStore.setKey('errorDetails', errorDetails);
}
export function setProjectImportName(projectImportName: string) {
  chatStore.setKey('projectImportName', projectImportName);
}
export function setIsModifiedCode(isModifiedCode: boolean) {
  chatStore.setKey('isModifiedCode', isModifiedCode);
}
export function trackError(content: string | null, type: string | null) {
  const currentState = chatStore.get();
  const currentTracking = currentState.errorTracking;

  if (content === currentTracking.content && type === currentTracking.type) {
    const newCount = currentTracking.autoSendCount + 1;

    chatStore.setKey('errorTracking', {
      content,
      type,
      autoSendCount: newCount,
      showAlert: newCount >= 3
    });

    return newCount <= 3;
  } else {
    chatStore.setKey('errorTracking', {
      content,
      type,
      autoSendCount: 1,
      showAlert: false
    });

    return true;
  }
}

export function resetErrorTracking() {
  chatStore.setKey('errorTracking', {
    content: null,
    type: null,
    autoSendCount: 0,
    showAlert: false
  });
}
