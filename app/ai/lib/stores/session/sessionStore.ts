import { UserStore } from '../user/userStore';
import { getUser<PERSON><PERSON><PERSON><PERSON><PERSON> } from '../user/user';
import { workbenchStore } from '../workbench';
import { atom } from 'nanostores';
import { backendApiFetch } from '../../backend-api';
import { ProjectCommits } from '../projects/code';

const sum = (arr:number[]) => arr.reduce((a, b) => a + b, 0);

const LOCAL_DEV_API = import.meta.env.CUSTOM_LOCAL_WITH_DEV_API;

interface SessionData {
  host: string;
  editorPath: string;
  hashedPassword: string;
  userIp: string;
  webcontainerApiPath: string;
  webcontainerId: string;
  portPathTemplate: string;
}

class SessionStore {
  private static instance: SessionStore;
  sessionData = null as unknown as SessionData;
  hasWebcontainerSession = atom(false);
  availablePorts = atom<number[]>([]); // array cu toate porturile găsite

  onStreamEnd = atom<Date>(new Date());
  onStartPreview = atom<Date>(new Date());
  onTerminalOutput = atom<string>('');

  private constructor() {
    this.onStreamEnd.listen(() => {
      this.saveProject();
   
    });
    this.hasWebcontainerSession.listen(()=>{
      this.listenForPorts();
      this.listenForTerminalOutpout();
    });
  }

  private listenForPorts() {
    const url = `${this.sessionData.host}/process/listen/${this.getSlug()}`;

    const eventSource = new EventSource(url, { withCredentials: true });
    eventSource.onmessage = (event) => {
      const ports = JSON.parse(event.data);
      this.setPorts(ports);
    };

    eventSource.onerror = (err) => {
      console.error('SSE error:', err);
      eventSource.close(); // optional: close on error
    };
  }

  public listenForTerminalOutpout() {
    const url = `${this.sessionData.host}${this.sessionData.webcontainerApiPath}/terminal/output/${this.getSlug()}`;

    const eventSource = new EventSource(url, { withCredentials: true });
    eventSource.onmessage = (event) => {
      this.onTerminalOutput.set(event.data);
    };

    eventSource.onerror = (err) => {
      console.error('SSE error:', err);
      eventSource.close(); // optional: close on error
    };
  }

  public static getInstance(): SessionStore {
    if (!SessionStore.instance) {
      SessionStore.instance = new SessionStore();
    }
    return SessionStore.instance;
  }

  getSlug() {
    return workbenchStore.getSlug();
  }

  public async restoreProject(commitHash: string) {
    return this.extensionApiFetch(`/user-projects/code/${this.getSlug()}/restore/${commitHash}`).then((x) =>
      x.json<ProjectCommits[]>(),
    );
  }

  public async saveProject() {
    return this.extensionApiFetch(`/user-projects/code/${this.getSlug()}/save`).then((x) => x.json<ProjectCommits[]>());
  }

  public async runTask({ command, isStartTask }: { command: string; isStartTask?: boolean }) {
    try {
      const response = await this.extensionApiFetch(
        `/terminal/run/${this.getSlug()}?command=${command}&isStartTask=${isStartTask}`,
      );

      if (response.ok) return true;
    } catch (err) {
      console.error('Error running task:', err);
      return false;
    } finally {
      await this.fetchPreviewPorts();
    }
  }

  public async openFile({ filePath }: { filePath: string }) {
    const response = await this.extensionApiFetch(`/file/open/${this.getSlug()}/${filePath}`);
    if (response.ok) return true;
  }

  public async getFileContent({ filePath }: { filePath: string }) {
    const response = await this.extensionApiFetch(`/file/${this.getSlug()}/${filePath}`);
    if (response.ok) {
      const content = await response.text();
      return content;
    }
  }

  async getSessionData() {
    if (this.sessionData) return this.sessionData;
    try {
      const response = await backendApiFetch('/webcontainers/get-session');
      const data = await response.json<SessionData>();

      this.sessionData = data;
      this.hasWebcontainerSession.set(true);
      return data;
    } catch (err: any) {
      console.error(err.message || 'Failed to fetch session data');
      return null as unknown as SessionData;
    }
  }

  public previewUrl(port: string) {
    const portPathTemplate = this.sessionData!.portPathTemplate;
    return `${portPathTemplate.replace('PORT', port)}`;
  }

  public getAvailablePorts() {
    return this.availablePorts.get();
  }

  public async fetchPreviewPorts(): Promise<string[]> {
    const slug = this.getSlug();

    const response = await this.extensionApiFetch(`/process/details/${slug}`);
    const ports = await response.json<string[]>();

    this.setPorts(ports);
    return ports;
  }

  setPorts = (ports: string[]) => {
    const newPorts = ports.map(Number);
    if (sum(newPorts) != sum(this.availablePorts.get()) || newPorts.length != this.availablePorts.get().length)
      this.availablePorts.set(newPorts);
  };

  async extensionApiFetch(path: string, options: RequestInit = {}, maxRetries = 5, retryDelay = 1000): Promise<Response> {
    await this.getSessionData();
   
    const EXTENSION_URL = `${this.sessionData?.host}${this.sessionData.webcontainerApiPath}`;

    const headers: Record<string, string> = {
      ...(options.headers as Record<string, string>),
    };

    if (LOCAL_DEV_API && this.sessionData?.hashedPassword) {
      headers['Cookie'] = `user-server-session=${this.sessionData.hashedPassword}`;
    }

    let attempt = 0;
    while (attempt < maxRetries) {
      try {
        const response = await fetch(`${EXTENSION_URL}${path}`, {
          body: options.body,
          method: options.method,
          signal: options.signal,
          headers,
          credentials: 'include',
        });

        if (!response.ok && response.status >= 500) {
          throw new Error(`Temporary error: ${response.status}`);
        }

        return response;
      } catch (error) {
        attempt++;
        if (attempt >= maxRetries) {
          throw error;
        }

        // Optional: Log the retry attempt
        console.warn(`Fetch attempt ${attempt} failed. Retrying in ${retryDelay}ms...`);

        await new Promise((resolve) => setTimeout(resolve, retryDelay));
      }
    }
    throw new Error('Failed to fetch after max retries');
  }
}

export const sessionStore = SessionStore.getInstance();

export default SessionStore;
