import React, { useEffect, useState } from 'react';
import { Link, useNavigate, useSearchParams } from '@remix-run/react';
import '../components/styles/signin.scss';
import CopyRighting from '~/components/common/Footer/copyRight';
import { useUser } from '~/ai/lib/context/userContext';
import { handleGitHubAuth, handleGoogleAuth, handlePaypalAuth } from '~/ai/lib/stores/user/user';
import { UserLoginContextType } from '~/types/user';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import { FaEye, FaEyeSlash, FaQuoteRight, FaSpinner } from 'react-icons/fa';
import { AnimatePresence, motion } from 'framer-motion';
import { LuInfo } from 'react-icons/lu';
import { FaCircleCheck } from 'react-icons/fa6';
import bielaLogo from '/biela-logo-only.svg';
import bielaText from '/biela-text.svg';
import LanguageSelector from '~/components/LanguageSelector';
import { LoadingBiela } from '~/components/LoadingBiela';
import GitHubIcon from '~/assets/icons/fi-brands-github.svg';
import GoogleLogin from '~/assets/icons/google-login.svg';
import { useTranslation } from 'react-i18next';

declare global {
  interface Window {
    turnstile: any;
  }
}

const turnstileSiteKey = import.meta.env.VITE_TURNSTILE_SITE_KEY;

function Login() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const redirectUrl = searchParams.get('redirectUrl'); // e.g. "https://dashboard.mywebsite.com/signin"

  const { login, isLoggedIn, setGoogleState } = useUser();
  const userStore = UserStore.getInstance();
  const [isSuccess, setIsSuccess] = useState(false);
  const { t } = useTranslation('translation');

  const [formData, setFormData] = useState<UserLoginContextType>({
    username: '',
    password: '',
  });
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);
  const [fieldError, setFieldError] = useState<{ username?: string; password?: string }>({});
  const [loading, setLoading] = useState(false);
  const [captchaLoaded, setCaptchaLoaded] = useState(false);

  // Company testimonials
  const testimonials = [
    {
      quote: 'Biela has revolutionized our development process, cutting our time-to-market by 70%.',
      company: 'TechGiant Inc.',
      logo: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      color: 'from-blue-500/20 to-blue-600/20',
    },
    {
      quote: 'The AI-powered code generation has transformed how our engineering team builds products.',
      company: 'InnovateCorp',
      logo: 'https://images.pexels.com/photos/5473298/pexels-photo-5473298.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      color: 'from-purple-500/20 to-purple-600/20',
    },
    {
      quote: 'Biela helped us launch our startup with enterprise-grade code in just weeks instead of months.',
      company: 'StartupBoost',
      logo: 'https://images.pexels.com/photos/5473950/pexels-photo-5473950.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      color: 'from-green-500/20 to-green-600/20',
    },
    {
      quote: "Our developers are now 5x more productive thanks to Biela's AI assistance.",
      company: 'Global Solutions',
      logo: 'https://images.pexels.com/photos/5473954/pexels-photo-5473954.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      color: 'from-yellow-500/20 to-yellow-600/20',
    },
  ];

  // Animation variants for testimonial slide effect
  const slideVariants = {
    enter: (direction) => ({
      x: direction > 0 ? 500 : -500,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction) => ({
      x: direction < 0 ? 500 : -500,
      opacity: 0,
    }),
  };

  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [direction, setDirection] = useState(1);

  // Auto-rotate testimonials every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setDirection(1); // Slide from right to left
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  // Helper to redirect user after login
  const redirectAfterLogin = () => {
    if (redirectUrl) {
      if (redirectUrl.startsWith('http')) {
        const newUrl = new URL(redirectUrl);
        window.location.href = newUrl.toString();
      } else {
        navigate(redirectUrl);
      }
    } else {
      const chatId = localStorage.getItem('chatId');
      navigate(chatId ? `/chat/${chatId}` : '/');
    }
  };

  // Redirect if already logged in
  useEffect(() => {
    if (!isLoggedIn()) {
      return;
    }

    setSuccess(
      redirectUrl ? `You're already logged in! Redirecting...` : `You're already logged in! Redirecting to home...`,
    );
    redirectAfterLogin();
  }, [navigate, isLoggedIn, redirectUrl]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleResendConfirmation = async () => {
    const input = formData.username.trim();

    if (!input) {
      setFieldError((prev) => ({
        ...prev,
        username: 'Please enter your email or username to resend confirmation.',
      }));
      return;
    }

    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    const payload = isEmail ? { email: input } : { username: input };

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      await userStore.resendConfirmation(payload);

      setSuccess(t('ResendConfirmationSuccess', 'Verification email has been resent! Please check your inbox.'));
    } catch (err: any) {
      setError(t('ResendConfirmationError', 'Failed to resend confirmation email.'));
    } finally {
      setLoading(false);
    }
  };

  // Updated handleSubmit with proper error handling
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (loading) {
      return;
    }

    // Reset errors and set loading
    setLoading(true);
    setError('');
    setSuccess('');
    setFieldError({});

    let hasFieldError = false;

    if (!formData.username.trim()) {
      setFieldError((prev) => ({ ...prev, username: t('UsernameRequired', 'Username is required') }));
      hasFieldError = true;
    }

    if (!formData.password.trim()) {
      setFieldError((prev) => ({ ...prev, password: t('PasswordRequired', 'Password is required') }));
      hasFieldError = true;
    }

    if (hasFieldError) {
      setLoading(false);
      return;
    }

    try {
      const token = document.querySelector<HTMLInputElement>('[name="cf-turnstile-response"]')?.value;

      if (!token) {
        throw new Error(t('CaptchaRequired', 'Please complete the CAPTCHA'));
      }

      await login(
        {
          username: formData.username.trim(),
          password: formData.password.trim(),
        },
        token,
      );

      setIsSuccess(true);
      setSuccess(t('LoginSuccess', 'Login successful! Redirecting...'));
      setTimeout(() => {
        redirectAfterLogin();
      }, 4500);
    } catch (err: any) {
      setError(err.message || t('LoginFailed', 'Login failed'));

      if (window.turnstile && window.turnstile.reset) {
        window.turnstile.reset('#turnstile-widget');
      }
    } finally {
      setLoading(false);
    }
  };

  const preHandleAuth = () => {
    setFieldError({});
    setError('');
    setSuccess('');
    setFormData({ username: '', password: '' });
  };

  // Auth handlers for external providers
  const preHandleGoogleAuth = () => {
    preHandleAuth();

    const state = crypto.randomUUID().toString();
    setGoogleState(state);
    handleGoogleAuth(state);
  };

  const preHandleGitHubAuth = () => {
    preHandleAuth();
    handleGitHubAuth();
  };

  const preHandlePaypalAuth = () => {
    preHandleAuth();
    handlePaypalAuth();
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (window.turnstile && document.getElementById('turnstile-widget')) {
        window.turnstile.render('#turnstile-widget', {
          sitekey: turnstileSiteKey,
          theme: 'dark',
          appearance: 'interaction-only',
          'before-interactive-callback': () => {
            document.getElementById('turnstile-widget')?.classList.remove('hidden');
          },
          callback: () => {
            setCaptchaLoaded(true);
          },
        });
        clearInterval(interval);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);


  useEffect(() => {
    document.title = t('meta.login.title') || 'Login to Your biela.dev Account';
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        'content',
        t('meta.login.description') ||
          'Access your biela.dev dashboard to manage and build your AI-generated projects.',
      );
    }
  }, [t]);

  return (
    <>
      <div
        className="min-h-screen flex items-center flex-col justify-center p-4 bg-auth"
        style={{
          backgroundImage: 'url(/hero-bg-shade-empty.png)',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
        }}
      >
        <div className="container px-[70px] mx-auto absolute top-0 w-full flex items-center justify-between max-sm:!pl-[40px] max-sm:px-[25px]">
          <Link smooth to="https://biela.dev/" className="flex items-center gap-2 sm:gap-3 group">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              style={{ transform: 'none', height: '32px', minWidth: '40px' }}
              className="relative"
            >
              <img
                src={bielaLogo}
                alt="BIELA"
                style={{
                  position: 'absolute',
                  right: 0,
                  top: '-7px',
                }}
                className="h-[80px] min-w-[80px] max-sm:min-w-[70px] max-sm:h-[70px]"
              />
              <div className="absolute inset-0 bg-accent/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300" />
            </motion.div>
            <img src={bielaText} alt="biela.dev" className="h-[60px] xl:h-[80px] w-[143px] max-sm:w-[100px]" />
          </Link>
          <div className="bg-black/50 border rounded-lg border-transparent">
            <LanguageSelector />
          </div>
        </div>

        {isSuccess ? (
          <LoadingBiela />
        ) : (
          <>
            <div className="w-full max-w-6xl flex flex-col md:flex-row items-stretch my-12">
              <div className="w-full md:w-1/2 p-8 md:p-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="w-full max-w-md mx-auto"
                >
                  <h2 className="text-3xl font-thin text-white mb-8">Log In</h2>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-6">
                      <input
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          fieldError.username ? 'border-red-500 focus:ring-red-500' : 'border-white/20'
                        } transition-all`}
                        placeholder={t('EmailOrUsernamePlaceholder', 'Email/Username')}
                      />
                      {fieldError.username && <span className="text-red-400 text-sm">{fieldError.username}</span>}
                    </div>
                    <div className="space-y-2 relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          fieldError.password ? 'border-red-500' : 'border-white/20'
                        } transition-all`}
                        placeholder={t('PasswordPlaceholder', 'Password')}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="bg-transparent absolute right-3 top-2.5 my-auto flex items-center justify-center text-white/50 hover:text-white transition-colors"
                      >
                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                      </button>
                      {fieldError.password && <span className="text-red-400 text-sm">{fieldError.password}</span>}
                    </div>

                    <div className="flex justify-end">
                      <Link to="/forget-password" className="text-white/60 hover:text-white text-sm transition-colors">
                        {t('ForgotPassword?', 'Forgot Password?')}
                      </Link>
                    </div>
                    <div id="turnstile-widget" className="hidden" />

                    <button
                      type="submit"
                      className={`w-full ${
                        captchaLoaded && !loading && !isLoggedIn()
                          ? 'bg-green-500 hover:bg-green-600'
                          : 'bg-gray-500 opacity-70 cursor-not-allowed'
                      } text-white py-3 rounded-lg transition-colors relative flex items-center font-thin justify-center gap-2`}
                      disabled={loading || isLoggedIn() || !captchaLoaded}
                    >
                      {loading ? (
                        <FaSpinner className="animate-spin" />
                      ) : !captchaLoaded ? (
                        <div className="flex items-center gap-2">
                          <FaSpinner className="animate-spin" />
                          <span>{t('loading', 'Loading')}...</span>
                        </div>
                      ) : (
                        t('LoginToProfile', 'Log In To Your Profile')
                      )}
                    </button>

                    {error === 'User account not confirmed' ? (
                      <div className="rounded-lg p-4 text-red-400 bg-red-400/5 space-y-2">
                        <div className="flex items-center gap-2">
                          <LuInfo />
                          <p className="font-semibold">{t('UserNotConfirmed', 'User account not confirmed')}</p>
                        </div>
                        <p className="text-sm text-red-300">
                          {t('ConfirmEmailNotice', 'You need to confirm your email address to activate your account.')}{' '}
                          <span
                            className="text-green-500 underline hover:text-green-400 cursor-pointer transition"
                            onClick={handleResendConfirmation}
                          >
                            {t('ResendConfirmationEmail', 'Resend Confirmation Email')}
                          </span>
                        </p>
                      </div>
                    ) : error ? (
                      <div className="flex items-center gap-2 rounded-lg p-3 text-red-400 bg-red-400/5">
                        <LuInfo />
                        <p>{error}</p>
                      </div>
                    ) : null}

                    {success && (
                      <div className="flex items-center justify-center gap-2 rounded-lg p-3 text-green-400 bg-green-400/5">
                        <FaCircleCheck />
                        <p>{success}</p>
                      </div>
                    )}
                  </form>
                  <div className="mt-6 text-center">
                    <div className="flex items-center">
                      <div className="w-full border-t border-white/20"></div>
                      <span className="px-2 text-white/60 text-sm">OR</span>
                      <div className="w-full border-t border-white/20"></div>
                    </div>

                    {/*<button*/}
                    {/*  onClick={preHandlePaypalAuth}*/}
                    {/*  disabled={isLoggedIn()}*/}
                    {/*  className="w-full mt-6 bg-white/10 hover:bg-white/20 border border-white/20 text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-0"*/}
                    {/*>*/}
                    {/*  <img className="mr-[10px]" src={PaypalLogin} alt={'paypal-login'} />*/}
                    {/*  <span>*/}
                    {/*    {t('LoginWithPaypal', 'Login With Paypal')}*/}
                    {/*  </span>*/}
                    {/*</button>*/}

                    <button
                      onClick={preHandleGoogleAuth}
                      disabled={isLoggedIn()}
                      className="w-full mt-6 bg-white/10 hover:bg-white/20 border border-white/20 text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-0"
                    >
                      <img className="mr-[10px]" src={GoogleLogin} alt={'google-login'} />
                      <span>{t('LoginWithGoogle', 'Login With Google')}</span>
                    </button>

                    <button
                      onClick={preHandleGitHubAuth}
                      disabled={isLoggedIn()}
                      className="w-full mt-6 bg-white/10 hover:bg-white/20 border border-white/20 text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-0"
                    >
                      <img alt="github-login" className="mr-[10px]" src={GitHubIcon} />
                      <span>{t('LoginWithGitHub', 'Login With GitHub')}</span>
                    </button>
                  </div>
                  <div className="mt-8 text-center">
                    <p className="text-white/60 font-light tracking-normal">
                      {t('NoAccountPrompt', "Don't have an account?")}{' '}
                      <Link to="/register" className="text-green-400 hover:text-green-300 transition-colors">
                        {t('SignMeUp', 'Sign Me Up')}
                      </Link>
                    </p>
                  </div>
                </motion.div>
              </div>
              <div className="hidden md:flex md:w-1/2 p-8 md:p-0 items-center justify-center">
                <div className="w-full max-w-md relative h-[500px] overflow-hidden">
                  <AnimatePresence custom={direction} mode="popLayout">
                    <motion.div
                      key={currentTestimonial}
                      custom={direction}
                      variants={slideVariants}
                      initial="enter"
                      animate="center"
                      exit="exit"
                      transition={{
                        x: { type: 'spring', stiffness: 300, damping: 30 },
                        opacity: { duration: 0.2 },
                      }}
                      className={`bg-gradient-to-r ${testimonials[currentTestimonial].color} rounded-xl p-8 w-full absolute inset-0`}
                    >
                      <div className="absolute top-4 right-4 flex space-x-1">
                        <div className="w-2 h-2 rounded-full bg-white/80"></div>
                        <div className="w-2 h-2 rounded-full bg-white/80"></div>
                        <div className="w-2 h-2 rounded-full bg-white/80"></div>
                      </div>

                      <FaQuoteRight className="text-white/20 text-4xl mb-6" />

                      <h3 className="text-white text-2xl font-light leading-relaxed mb-12">
                        "{testimonials[currentTestimonial].quote}"
                      </h3>

                      <div className="absolute bottom-[80px] left-8 right-8">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 rounded-full overflow-hidden bg-black/20">
                            <img
                              src={testimonials[currentTestimonial].logo}
                              alt={testimonials[currentTestimonial].company}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div>
                            <p className="text-white font-medium">{testimonials[currentTestimonial].company}</p>
                          </div>
                        </div>
                      </div>

                      <div className="absolute bottom-7 left-0 right-0 flex justify-center gap-1">
                        {testimonials.map((_, i) => (
                          <div
                            key={i}
                            className={`w-2 h-2 rounded-full ${
                              i === currentTestimonial ? 'bg-green-500' : 'bg-white/40'
                            }`}
                          />
                        ))}
                      </div>
                    </motion.div>
                  </AnimatePresence>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      <CopyRighting />
    </>
  );
}

export default Login;
