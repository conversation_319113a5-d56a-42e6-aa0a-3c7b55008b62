import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from '@remix-run/react';
import '../components/styles/signin.scss';
import { forgetPassword } from '~/ai/lib/stores/user/user';
import CopyRighting from '~/components/common/Footer/copyRight';
import { motion } from 'framer-motion';
import bielaLogo from '/biela-logo-only.svg';
import bielaText from '/biela-text.svg';
import LanguageSelector from '~/components/LanguageSelector';
import { FaArrowLeft, FaEnvelope, FaSpinner } from 'react-icons/fa';
import { MetaFunction } from '@remix-run/cloudflare';
import { useTranslation } from 'react-i18next';

const turnstileSiteKey = import.meta.env.VITE_TURNSTILE_SITE_KEY;

export const meta: MetaFunction = () => {
  return [
    { title: "Forgot Password - biela.dev" },
    { name: "description", content: "Reset your biela.dev account password and regain access." },
  ];
};

function ForgetPassword() {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [captchaLoaded, setCaptchaLoaded] = useState(false);
  const { t } = useTranslation();

  const validateEmail = (email: string) => {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
  };

  const handleSubmit = async (e: { preventDefault: () => void }) => {
    e.preventDefault();

    if (!email.trim()) {
      setError(t('EmailRequired', 'Email is required'));
      return;
    }

    if (!validateEmail(email)) {
      setError(t('EmailInvalid', 'Please enter a valid email address'));
      return;
    }

    setError('');
    setIsSubmitting(true);

    try {
      const token = document.querySelector<HTMLInputElement>('[name="cf-turnstile-response"]')?.value;

      if (!token) {
        throw new Error(t('CaptchaRequired', 'Please complete the CAPTCHA'));
      }
      const userData = await forgetPassword({
        email,
      }, token);
      console.log(userData,'userDAta');
      if (userData.ok) {
        setTimeout(() => {
          setIsSubmitting(false);
          setIsSubmitted(true);

          setTimeout(() => {
            navigate('/login/', { state: { email } });
          }, 5000);
        }, 1500);
      } else if (userData.status === 404) {
        setError(t('EmailInvalid', 'Please enter a valid email address'));
        setIsSubmitting(false);
        return;
      }
      else if(userData.status === 429){
        setError(t('TooManyRequests','Too many requests, please try again later'));
        setIsSubmitting(false);
        return;
      }
      else if (userData.status === 500) {
        setError(t('SomethingWentWrongMessage','Something went wrong, please try again later'));
        setIsSubmitting(false);
        return;
      }
    } catch (error) {
      console.log(error,'error');
      console.error('Error:', error);
      setError(t('SomethingWentWrongMessage','Something went wrong, please try again later'));
      setIsSubmitting(false);
      return;
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (window.turnstile && document.getElementById('turnstile-widget')) {
        window.turnstile.render('#turnstile-widget', {
          sitekey: turnstileSiteKey,
          theme: 'dark',
          appearance: 'interaction-only',
          'before-interactive-callback': () => {
            document.getElementById('turnstile-widget')?.classList.remove('hidden');
          },
          callback: () => {
            setCaptchaLoaded(true);
          },
        });
        clearInterval(interval);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);

  return (
    <div>
      <div className="flex items-center flex-col justify-between h-screen signin-bg">
        <div className="container px-[70px] mx-auto absolute top-0 w-full flex items-center justify-between max-sm:!pl-[40px] max-sm:px-[25px]">
          <Link smooth to="/" className="flex items-center gap-2 sm:gap-3 group">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              style={{
                transform: 'none',
                height: '32px',
                minWidth: '40px',
              }}
              className="relative"
            >
              <img
                src={bielaLogo}
                alt="BIELA"
                style={{
                  position: 'absolute',
                  right: 0,
                  top: '-7px',
                }}
                className="h-[80px] min-w-[80px] max-sm:min-w-[70px] max-sm:h-[70px]"
              />
              <div className="absolute inset-0 bg-accent/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300" />
            </motion.div>
            <img src={bielaText} alt="biela.dev" className="h-[60px] xl:h-[80px] w-[143px] max-sm:w-[100px]" />
          </Link>
          <div className="bg-black/20 border rounded-lg border-transparent">
            <LanguageSelector />
          </div>
        </div>

        <section className="flex items-center justify-center h-screen">
          <div className="w-full max-w-xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-900/10 backdrop-blur-sm rounded-xl p-8 border border-gray-800/50"
            >
              <div className="mb-6">
                <Link to="/login/" className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors">
                  <FaArrowLeft />
                  <span className="font-extralight">
                     {t('BackToLogin', 'Back to Login')}
                  </span>
                </Link>
              </div>

              <h2 className="text-3xl font-extralight text-white mb-4">
                {t('ForgotPasswordHeading', 'Forgot Password')}
              </h2>
              <p className="text-gray-400 font-extralight mb-8">
                {t('ForgotPasswordDescription', ' Enter your email address and we\'ll send you a verification link to reset your password.')}
              </p>

              {isSubmitted ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="bg-green-500/20 border border-green-500/50 rounded-lg p-4 text-center"
                >
                  <p className="text-green-400 font-light">
                    {t('VerificationLinkSent', 'Verification link sent! Please check your email.')}
                  </p>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <div className="relative">
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => {
                          setEmail(e.target.value);

                          if (error) {
                            setError('');
                          }
                        }}
                        className={`w-full bg-white/10 backdrop-blur-sm border !leading-none rounded-lg pl-12 pr-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 ${error ? 'border-red-500 focus:ring-red-500' : 'border-white/20'} transition-all`}
                        placeholder={t('EnterYourEmailPlaceholder', 'Enter your email')}
                      />
                      <div className="absolute left-4 top-0 bottom-0 flex items-center justify-center">
                        <FaEnvelope className="text-gray-400" />
                      </div>
                    </div>
                    {error && <span className="text-red-400 text-sm">{error}</span>}
                  </div>
                  <div id="turnstile-widget" className="hidden" />

                  <button
                    type="submit"
                    className={`w-full ${
                      captchaLoaded && !isSubmitting
                        ? 'bg-green-500 hover:bg-green-600'
                        : 'bg-gray-500 opacity-70 cursor-not-allowed'
                    } text-white py-3 rounded-lg transition-colors font-extralight flex items-center justify-center`}
                    disabled={isSubmitting || !captchaLoaded}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>{t('Sending', 'Sending…')}</span>
                      </div>
                    ) : !captchaLoaded ? (
                      <div className="flex items-center gap-2">
                        <FaSpinner className="animate-spin" />
                        <span>{t('loading', 'Loading')}...</span>
                      </div>
                    ) : (
                      <span>{t('SendVerificationCode', 'Send Verification Code')}</span>
                    )}
                  </button>
                </form>
              )}
            </motion.div>
          </div>
        </section>
      </div>

      <CopyRighting />
    </div>
  );
}

export default ForgetPassword;
