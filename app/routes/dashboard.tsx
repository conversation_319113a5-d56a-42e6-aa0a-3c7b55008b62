import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { fetchProjects } from '~/api/projectsApi';
import AffiliateBanner from '~/backOffice/components/dashboard/AffiliateBanner';
import ProjectsTypeFilter from '~/backOffice/components/dashboard/ProjectsTypeFilter';
import Wrapper from '~/components/common/Wrapper';
import { ClientOnly } from 'remix-utils/client-only';
import ProjectsListClient from '~/backOffice/components/dashboard/ProjectsList.client';
import QuickActions from '~/backOffice/components/dashboard/QuickActions.client';
import { useUser } from '~/ai/lib/context/userContext';
import { useNavigate } from '@remix-run/react';
import ContestStatus from '~/backOffice/components/dashboard/ContestStatus';
import '~/components/styles/contest-btn.css';
import JoinUsLiveYoutube from '~/backOffice/components/settings/IdentitySettings/JoinUsLiveYoutube';

type Project = {
  id: number;
  title: string;
  createdAt: string;
  type: string;
};

function Dashboard() {
  const { t } = useTranslation('translation');
  const { isLoggedIn } = useUser();
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState<{ quickActions: any[]; folders: any[]; projects: Project[] }>({
    quickActions: [],
    folders: [],
    projects: [],
  });
  const [selectedType, setSelectedType] = useState<string>('all');
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [filterLoading, setFilterLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [showNewFolderInput, setShowNewFolderInput] = useState<boolean>(false);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [publishedProjects, setPublishedProjects] = useState<string[]>([]);
  const [refreshContestProjects, setRefreshContestProjects] = useState(0);
  const [searchTermChanged, setSearchTermChanged] = useState(false);

  const triggerRefreshContestProjects = () => {
    setRefreshContestProjects((prev) => prev + 1);
  };

  const updatePublishedProjects = (projectSlug: string, isPublished: boolean) => {
    setPublishedProjects((prev) =>
      isPublished ? [...prev, projectSlug] : prev.filter((slug) => slug !== projectSlug),
    );
  };

  useEffect(() => {
    if (loading && searchTermChanged) {
      return;
    }

    const loadData = async () => {
      try {
        setLoading(true);

        let data;
        if (selectedFolder) {
          data = await fetchProjects(
            selectedFolder,
            currentPage,
            10,
            'updatedAt',
            'DESC',
          );
        } else {
          data = await fetchProjects(undefined, currentPage, 10, 'updatedAt', 'DESC');
        }
        setTotalPages(data.totalPages);

        const projects = data.items.map((project) => ({
          ...project,
          projectName: project.projectName || project.projectSlug,
        }));
        setDashboardData((prevData) => ({ ...prevData, projects }));
        setFilteredProjects(projects);
        setSearchTermChanged(false);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        setError(error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedFolder, currentPage]);

  const refreshProjects = async () => {
    try {
      setLoading(true);
      let data = await fetchProjects(selectedFolder, currentPage, 10, 'updatedAt', 'DESC');
      setTotalPages(data.totalPages);
      const projects = data.items.map((project) => ({
        ...project,
        projectName: project.projectName || project.projectSlug,
      }));
      setDashboardData((prevData) => ({ ...prevData, projects }));
      setFilteredProjects(projects);
    } catch (error) {
      console.error('Error refreshing projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = async (type: string) => {
    setFilterLoading(true);
    setSelectedType(type);
    setSelectedFolder(type === 'all' ? '' : type);
    setCurrentPage(0);

    try {
      const data = await fetchProjects(type === 'all' ? null : type, 0, 10, 'updatedAt', 'DESC');

      setTotalPages(data.totalPages);
      const projects = data.items.map((project) => ({
        ...project,
        projectName: project.projectName || project.projectSlug,
      }));

      setFilteredProjects(projects);
      setDashboardData((prev) => ({ ...prev, projects }));
    } catch (error) {
      console.error('Error filtering projects:', error);
    } finally {
      setFilterLoading(false);
    }
  };

  useEffect(() => {
    if (!isLoggedIn()) {
      navigate('/login');
    }
  }, [navigate, isLoggedIn]);

  useEffect(() => {
    document.title = t('meta.dashboard.title') || 'Your Projects Dashboard – biela.dev';
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        'content',
        t('meta.dashboard.description') ||
        'Manage your AI-built websites and apps, edit live projects, and track your build history—all in one place.',
      );
    }
  }, [t]);

  return (
    <Wrapper
      handleSendMessage={(e, messageInput) => console.log('Message sent:', messageInput)}
      isStreaming={false}
      isDashboardPage={true}
      handleStop={() => console.log('Stream stopped')}
    >
      <div className="w-full min-h-screen bg-[transparent]">
        <div className="min-h-screen background-image-full color-white flex flex-col">
          <div className="container mx-auto px-8 py-8 max-w-[1600px] space-y-8">
            <div className="md:flex items-center justify-between">
              <h1 className="text-4xl font-light uppercase">{t('dashboard', 'Dashboard')}</h1>
              {/* <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 bg-gray-800/50 backdrop-blur-sm px-4 py-2 rounded-full">
                <FaRobot className="text-green-400" />
                <span className="text-sm font-light">1,750 {t('dashboardData.title')}</span>
              </div>
            </div> */}
            </div>
            <JoinUsLiveYoutube />
            <ClientOnly>
              {() => (
                <QuickActions actions={dashboardData.quickActions} setShowNewFolderInput={setShowNewFolderInput} />
              )}
            </ClientOnly>
            <AffiliateBanner />
            <ContestStatus
              updatePublishedProjects={updatePublishedProjects}
              refreshContestProjects={refreshContestProjects}
            />
            <div className="flex flex-col lg:flex-row gap-6">
              <ProjectsTypeFilter
                projects={dashboardData.projects}
                onFilterChange={setFilteredProjects}
                showNewFolderInput={showNewFolderInput}
                // selectedFolder={(e) => {
                //   setSelectedFolder(e);
                // }}
                selectedFolder={handleFilterChange}
                setShowNewFolderInput={setShowNewFolderInput}
                selectedType={selectedType}
                setSelectedType={setSelectedType}
                refreshProjects={handleFilterChange}
              />
              <ClientOnly key={selectedType}>
                {() => (
                  <ProjectsListClient
                    nextPage={(page) => {
                      setCurrentPage(page);
                    }}
                    totalPagesNumber={totalPages}
                    setTotalPagesNumber={setTotalPages}
                    projects={filteredProjects}
                    selectedType={selectedType}
                    selectedFolder={selectedFolder}
                    loading={loading}
                    refreshProjects={refreshProjects}
                    filterLoading={filterLoading}
                    publishedProjects={publishedProjects}
                    updatePublishedProjects={updatePublishedProjects}
                    triggerRefreshContestProjects={triggerRefreshContestProjects}
                  />
                )}
              </ClientOnly>
            </div>
          </div>
        </div>
      </div>
    </Wrapper>
  );
}

export default Dashboard;
