import React, { useEffect, useState } from 'react';
import '../components/styles/signin.scss';
import { Link, useNavigate, useSearchParams } from '@remix-run/react';
import { confirmPassword } from '~/ai/lib/stores/user/user';
import CopyRighting from '~/components/common/Footer/copyRight';
import InfoIcon from '~/assets/icons/infoIcon.svg';
import { FaArrowLeft, FaEye, FaEyeSlash, FaLock } from 'react-icons/fa';
import { motion } from 'framer-motion';
import bielaLogo from '/biela-logo-only.svg';
import bielaText from '/biela-text.svg';
import LanguageSelector from '~/components/LanguageSelector';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import { useTranslation } from 'react-i18next';

interface FormData {
  password: string;
  confirmPassword: string;
}

export default function ConfirmPassword() {

  const [formData, setFormData] = useState<FormData>({ password: '', confirmPassword: '' });
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');
  const [searchParams] = useSearchParams();
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [passwordFeedback, setPasswordFeedback] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const redirectUrl = searchParams.get('redirectUrl'); // e.g., "https://dashboard.mywebsite.com/signin"
  const { t } = useTranslation();


  const token = searchParams.get('token');

  const navigate = useNavigate();
  const userStore = UserStore.getInstance(); // get the user store instance

  const redirectAfterConfirm = () => {
    if (redirectUrl) {
      const newUrl = new URL(redirectUrl);
      window.location.href = newUrl.toString();
    } else {
      const chatId = localStorage.getItem('chatId');
      navigate(chatId ? `/chat/${chatId}` : '/');
    }
  };


  if (!token) {
    setError(t('InvalidConfirmationLink', 'Invalid confirmation link'));

    return null;
  }

  useEffect(() => {
    const password = formData.password;
    if (!password) {
      setPasswordStrength(0);
      setPasswordFeedback("");
      return;
    }

    let strength = 0;
    let feedback = "";

    // Checks for various password conditions
    if (password.length >= 8) {
      strength += 1;
    }
    if (/\d/.test(password)) {
      strength += 1;
    }
    if (/[a-z]/.test(password)) {
      strength += 1;
    }
    if (/[A-Z]/.test(password)) {
      strength += 1;
    }
    if (/[^A-Za-z0-9]/.test(password)) {
      strength += 1;
    }

    // Determine feedback based on computed strength
    if (strength <= 2) {
      feedback = "Weak password";
    } else if (strength <= 4) {
      feedback = "Good password";
    } else {
      feedback = "Strong password";
    }

    setPasswordStrength(strength);
    setPasswordFeedback(feedback);
  }, [formData.password]);


  const getStrengthColor = () => {
    if (passwordStrength <= 2) return "bg-red-500";
    if (passwordStrength <= 4) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getFeedbackColor = () => {
    if (passwordStrength <= 2) return "text-red-400";
    if (passwordStrength <= 4) return "text-yellow-400";
    return "text-green-400";
  };


  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validatePassword = (password: string) => {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z]).{8,}$/;
    return passwordRegex.test(password);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!validatePassword(formData.password)) {
      setError(
          t(
             'PasswordRequirements',
             'Password must be at least 8 characters long and include an uppercase letter and a lowercase letter.'
            )
          );
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError(t('PasswordsMismatch', 'Passwords do not match.'));
      return;
    }

    try {
      await confirmPassword({ token, password: formData.password });
      setSuccess(t('PasswordUpdatedSuccess', 'Password updated successfully!'));
      setTimeout(() => {
        redirectAfterConfirm();
      }, 1000);
    } catch (err) {
      setError(
  (err as { message: string }).message ||
          t('SomethingWentWrong', 'Something went wrong')
        );
    }
  };

  return (
    <div>
      <div className="flex items-center flex-col justify-between h-screen signin-bg">
        <div
          className="container px-[70px] mx-auto absolute top-0 w-full flex items-center justify-between max-sm:!pl-[40px] max-sm:px-[25px]">


          <Link smooth to="/" className="flex items-center gap-2 sm:gap-3 group">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              style={{
                transform: 'none',
                height: '32px',
                minWidth: '40px'
              }}
              className="relative"
            >
              <img
                src={bielaLogo}
                alt="BIELA"
                style={{
                  position: 'absolute',
                  right: 0,
                  top: '-7px'
                }}
                className="h-[80px] min-w-[80px] max-sm:min-w-[70px] max-sm:h-[70px]"
              />
              <div
                className="absolute inset-0 bg-accent/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300" />
            </motion.div>
            <img
              src={bielaText}
              alt="biela.dev"
              className="h-[60px] xl:h-[80px] w-[143px] max-sm:w-[100px]"
            />
          </Link>
          <div className="bg-black/20 border rounded-lg border-transparent">
            <LanguageSelector />
          </div>
        </div>

        <section className="w-full max-w-md mx-auto">

          <div className="flex items-center min-h-screen">
            <div
              className="w-full max-w-xl mx-auto md:w-full bg-gray-900/20 backdrop-blur-sm rounded-xl p-8 border border-gray-800/50 ">
              <Link
                to="/login/"
                className="mb-6 flex items-center gap-2 text-[#9CA3AF] hover:text-white transition-colors"
              >
                <FaArrowLeft />
                <span className="font-light">{t('Back', 'Back')}</span>
              </Link>
              <h1 className="text-3xl font-light text-white mb-4">
                {t('ResetPassword', 'Reset Password')}
              </h1>
              <p className="text-gray-400 font-light mb-8">
                {t( 'ResetPasswordDescription', 'Create a new password for your account')}
              </p>

              <form onSubmit={handleSubmit} className="flex flex-col gap-[15px]">
                <div className="input-container">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="outlined-basic"
                    className={`w-full bg-[#ffffff1a] border rounded-lg pl-12.5  pr-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 border-white/20 transition-all ${error ? 'border-red-500 focus:ring-red-500' : 'border-white/20'} transition-all`}
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    placeholder={t('NewPasswordPlaceholder','New password')}
                    autoComplete="off"
                  />
                  <div className="absolute left-4 top-0 bottom-0 flex items-center justify-center">
                    <FaLock className="text-gray-400" />
                  </div>
                  <div
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-[17px] my-auto flex items-center justify-center text-white/50 hover:text-white transition-colors !mt-0 cursor-pointer"
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </div>
                </div>

                {formData.password && (
                  <div className="">
                    <div className="flex justify-between items-center mb-2">
                    <span className={`text-xs ${getFeedbackColor()} font-light`}>
                      {passwordFeedback}
                    </span>
                      <span className="text-xs text-gray-400 font-light">
                        {passwordStrength}/5
                      </span>
                    </div>
                    <div className="h-1 w-full bg-[#374151] rounded-full overflow-hidden">
                      <div
                        className={`h-full ${getStrengthColor()} transition-all duration-300`}
                        style={{
                          width: `${(passwordStrength / 5) * 100}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                )}

                <div className="input-container">

                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="outlined-basic"
                    className={`w-full bg-[#ffffff1a] border rounded-lg pl-12.5  pr-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 border-white/20 transition-all ${error ? 'border-red-500 focus:ring-red-500' : 'border-white/20'} transition-all`}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    placeholder={t('ConfirmNewPasswordPlaceholder','Confirm new password')}
                    autoComplete="off"
                  />
                  <div className="absolute left-4 top-0 bottom-0 flex items-center justify-center">
                    <FaLock className="text-gray-400" />
                  </div>
                  <div
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-[17px] my-auto flex items-center justify-center text-white/50 hover:text-white transition-colors !mt-0 cursor-pointer"
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </div>
                </div>

                <button type="submit"
                        className="mt-2 w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg transition-colors font-light flex items-center justify-center">
                  {t('ResetPasswordButton', 'Reset Password')}
                </button>

                {/* Display error or success messages here */}
                {error && (
                  <div className="flex items-start rounded-lg p-3 bg-[rgba(134,244,156,0.07)]">
                    <img src={InfoIcon} alt="Information icon" className="mr-[12px]" />
                    <p className="error-signin !mt-0 !text-[13px] font-light leading-[15px]">{error}</p>
                  </div>
                )}
                {success && (
                  <div className="flex items-start rounded-lg p-3 bg-[rgba(134,244,156,0.07)]">
                    <img src={InfoIcon} alt="Information icon" className="mr-[12px]" />
                    <p className="success-signin !mt-0 !text-white !text-[13px] font-light leading-[15px]">{success}</p>
                  </div>
                )}
              </form>
            </div>
          </div>
        </section>

      </div>

      <div>
        <CopyRighting />
      </div>
    </div>
  );
}
