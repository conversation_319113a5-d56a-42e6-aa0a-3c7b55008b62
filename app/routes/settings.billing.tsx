import React, { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import {
  FaCalculator,
  FaCalendar,
  FaChevronLeft,
  FaChevronRight,
  FaCreditCard,
  FaCrown,
  FaDownload,
  FaEdit,
  FaEnvelope,
  FaEye,
  FaGem,
  FaLock,
  FaRobot
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import InvoicePreviewModal from '~/backOffice/components/settings/BillingSettings/InvoicePreviewModal';
import { InvoicePDF } from '~/backOffice/components/billings/InvoicePDF';
import { fetchPlans } from '~/api/plansApi';
import IconButton from '~/backOffice/components/dashboard/IconButton';
import { ExternalLink } from 'lucide-react';

function SettingsBilling() {
  const { t } = useTranslation();
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [billingCycle, setBillingCycle] = useState("monthly");
  const [showTokenCalculator, setShowTokenCalculator] = useState(false);
  const [customTokens, setCustomTokens] = useState("");
  const [showEmailEdit, setShowEmailEdit] = useState(false);
  const [billingEmail, setBillingEmail] = useState("<EMAIL>");
  const [tempEmail, setTempEmail] = useState("<EMAIL>");
  const [currentPlanIndex, setCurrentPlanIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const iconProps = { size: 18, strokeWidth: 1.5 };

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  useEffect(() => {
    const loadPlans = async () => {
      try {
        const plansFromApi = await fetchPlans();

        setDynamicPlans(plansFromApi);
      } catch (error) {
        console.error('Failed to load plans:', error);
      }
    };

    loadPlans();
  }, []);

  const [dynamicPlans, setDynamicPlans] = useState([]);

  const plans = [
    ...dynamicPlans.map((plan, index) => ({
      name: plan.name,
      price: plan.price,
      tokens: plan.tokens,
      period: "tokens per month",
      features: plan.features.length > 0 ? plan.features : ["No features listed"],
      color: index === 0 ? "from-blue-500/20 to-blue-600/20" : "from-purple-500/20 to-purple-600/20",
      icon: index === 0 ? <FaGem /> : <FaCrown />,
      popular: index === 0,
    })),
  ];

  const recentInvoices = [
    {
      date: "December 4, 2025",
      description: "Monthly payment",
      amount: "$29.00",
      seats: 1,
      status: "Paid",
      id: "INV-2025-12",
    },
    {
      date: "November 4, 2025",
      description: "Monthly payment",
      amount: "$29.00",
      seats: 1,
      status: "Unpaid",
      id: "INV-2025-11",
      dueDate: "November 18, 2025",
    },
    {
      date: "October 4, 2025",
      description: "Monthly payment",
      amount: "$29.00",
      seats: 1,
      status: "Paid",
      id: "INV-2025-10",
    },
    {
      date: "September 4, 2025",
      description: "Monthly payment",
      amount: "$29.00",
      seats: 1,
      status: "Unpaid",
      id: "INV-2025-09",
      dueDate: "September 18, 2025",
    },
  ];

  const dummyInvoiceData = {
    invoiceNumber: 'B.D 0001',
    issuedDate: '01.03.2025',
    dueDate: '30.03.2025',
    from: {
      companyName: 'TeachMeCode Institute',
      address: '404, Building 08, Business Bay, Bay Square, \nDubai (UAE)',
      bankDetails: {
        accountNo: '7854 8747 8467',
        code: '138 527 62',
      },
    },
    to: {
      companyName: 'Apex Solutions Ltd.',
      address: '123 Innovation Street, Suite 456, Metropolis (USA)',
    },
    items: [
      { description: 'Website Design', qty: 1, price: 1000 },
      { description: 'Logo Design', qty: 1, price: 500 },
    ],
    subTotal: 1500,
    tax: 100,
    total: 1600,
    contact: {
      email: '<EMAIL>',
      phone: '+*********** 346',
    },
  };

  const calculateTokenPrice = (tokens) => {
    return (tokens / 1000000) * 20;
  };

  const handleEmailSave = () => {
    setBillingEmail(tempEmail);
    setShowEmailEdit(false);
  };

  const handlePayInvoice = (invoiceId) => {
    console.log(`Processing payment for invoice ${invoiceId}`);
  };

  const nextPlan = () => {
    setCurrentPlanIndex((prev) => (prev === plans.length - 1 ? 0 : prev + 1));
  };

  const prevPlan = () => {
    setCurrentPlanIndex((prev) => (prev === 0 ? plans.length - 1 : prev - 1));
  };

  const PlanCard = ({ plan, index }) => (
    <motion.div
      className={`relative w-full ${index === 1 ? "pt-0" : "pt-12"}`}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <div
        className={`bg-gradient-to-r ${plan.color} rounded-xl overflow-hidden ${plan.popular ? "ring-2 ring-green-500" : ""
          } h-full flex flex-col`}
      >
        <div className={`p-6 relative overflow-hidden ${index === 1 ? "py-10" : ""}`}>
          {plan.popular && (
            <div className="absolute top-0 right-0">
              <div className="bg-green-500 px-4 py-1 rounded-bl-lg text-sm font-light">Most Popular</div>
            </div>
          )}

          <div className="relative z-10 flex items-center gap-4">
            <div className={`p-4 bg-black/20 rounded-xl ${index === 1 ? "scale-110" : ""}`}>{plan.icon}</div>
            <div>
              <h3 className={`font-light ${index === 1 ? "text-2xl" : "text-xl"}`}>{plan.name}</h3>
              <div className="flex items-baseline gap-1 mt-1">
                <span className={`font-light ${index === 1 ? "text-5xl" : "text-4xl"}`}>${plan.price}</span>
                <span className="text-gray-400 text-sm font-light">/{billingCycle === "monthly" ? "mo" : "yr"}</span>
              </div>
            </div>
          </div>

          <div className="absolute -bottom-12 -right-12 text-[200px] opacity-5">{plan.icon}</div>
        </div>

        <div className={`p-6 relative z-10 flex-1 flex flex-col ${index === 1 ? "pb-10" : ""}`}>
          <div className="bg-black/20 rounded-lg px-4 py-3 mb-6">
            <div className="flex items-center gap-2">
              <FaRobot className="text-green-400" />
              <span className="text-green-400 font-light">{plan.tokens}</span>
              <span className="text-gray-400 text-sm font-light">{plan.period}</span>
            </div>
          </div>

          <div className="space-y-4 flex-1 mb-6">
            {plan.features.map((feature, idx) => (
              <div key={idx} className="flex items-center gap-3 text-sm">
                <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
                <span className="font-light">{feature}</span>
              </div>
            ))}
          </div>

          <button
            className={`w-full py-3 rounded-lg transition-colors font-light ${plan.current
              ? "bg-gray-700 text-gray-400 cursor-not-allowed"
              : "bg-green-500 hover:bg-green-600"
              } ${index === 1 ? "py-4 text-lg" : ""}`}
          >
            {plan.current ? "Current Plan" : "Upgrade Plan"}
          </button>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="container mx-auto px-4 sm:px-8 py-8 max-w-[1600px] rounded-xl">
      <div className="text-white space-y-12">
        <div className="text-center space-y-4">
          <h2 className="text-4xl font-light">Choose a Plan or Reload Tokens as Needed</h2>
          <p className="text-gray-400 font-light text-lg">
            Pick from our three flexible plans or purchase{" "}
            <button
              onClick={() => setShowTokenCalculator(true)}
              className="text-green-400 hover:text-green-300 transition-colors bg-transparent"
            >
              extra tokens
            </button>{" "}
            anytime to keep coding without limits.
          </p>
        </div>

        <div className="flex justify-center mb-8">
          <div className="bg-gray-800/50 rounded-full p-1">
            <button
              onClick={() => setBillingCycle("monthly")}
              className={`px-6 py-2 rounded-full transition-all font-light ${billingCycle === "monthly" ? "bg-green-500" : "bg-transparent text-gray-400 hover:text-white"
                }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle("yearly")}
              className={`px-6 py-2 rounded-full transition-all font-light ${billingCycle === "yearly" ? "bg-green-500" : "bg-transparent text-gray-400 hover:text-white"
                }`}
            >
              Yearly
            </button>
          </div>
        </div>

        {isMobile ? (
          <div className="relative">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentPlanIndex}
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ duration: 0.3 }}
                className="w-full"
              >
                <PlanCard plan={plans[currentPlanIndex]} index={currentPlanIndex} />
              </motion.div>
            </AnimatePresence>

            <div className="flex justify-center gap-4 mt-6">
              <button
                onClick={prevPlan}
                className="p-2 bg-gray-800/50 rounded-full hover:bg-gray-700/50 transition-colors"
              >
                <FaChevronLeft />
              </button>
              <div className="flex gap-2">
                {plans.map((_, idx) => (
                  <button
                    key={idx}
                    onClick={() => setCurrentPlanIndex(idx)}
                    className={`w-2 h-2 rounded-full transition-colors ${currentPlanIndex === idx ? "bg-green-500" : "bg-gray-600"
                      }`}
                  />
                ))}
              </div>
              <button
                onClick={nextPlan}
                className="p-2 bg-gray-800/50 rounded-full hover:bg-gray-700/50 transition-colors"
              >
                <FaChevronRight />
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 items-stretch justify-center">
            {plans.map((plan, index) => (
              <PlanCard key={index} plan={plan} index={index} />
            ))}
          </div>
        )}

        {/* Billing Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 relative overflow-hidden">
            <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
              <FaCalendar />
            </div>
            <div className="relative z-10">
              <h3 className="text-sm text-gray-400 font-light mb-4">Next invoice issue date</h3>
              <div className="flex items-center gap-3">
                <FaCalendar className="text-gray-400" />
                <span className="text-2xl font-light">January 4, 2025</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 relative overflow-hidden">
            <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
              <FaCreditCard />
            </div>
            <div className="relative z-10">
              <h3 className="text-sm text-gray-400 font-light mb-4">Invoice total</h3>
              <div className="flex items-center gap-3">
                <FaCreditCard className="text-gray-400" />
                <span className="text-2xl font-light">$50.00</span>
              </div>
            </div>
          </div>

          <div className="md:col-span-2 lg:col-span-1 bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 relative overflow-hidden">
            <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
              <FaEnvelope />
            </div>
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm text-gray-400 font-light">Billing email</h3>
                <button
                  onClick={() => setShowEmailEdit(true)}
                  className="text-gray-400 bg-transparent hover:text-white transition-colors"
                >
                  <FaEdit />
                </button>
              </div>
              {showEmailEdit ? (
                <div className="space-y-3">
                  <input
                    type="email"
                    value={tempEmail}
                    onChange={(e) => setTempEmail(e.target.value)}
                    className="w-full bg-black/20 border border-gray-700 rounded-lg px-4 py-3 font-light focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                  />
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => setShowEmailEdit(false)}
                      className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors font-light"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleEmailSave}
                      className="px-4 py-2 bg-green-500 hover:bg-green-600 rounded-lg transition-colors font-light"
                    >
                      Save
                    </button>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <FaEnvelope className="text-gray-400" />
                  <span className="text-xl font-light">{billingEmail}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Recent Invoices */}
        <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-light">Recent Invoices</h3>
            <p className="text-gray-400 text-sm font-light">Here will be your recent invoice</p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full table-auto">
              <thead>
                <tr className="text-sm text-gray-400 font-light">
                  <th className="text-left px-4 py-2 w-[14.28%]">Date</th>
                  <th className="text-left px-4 py-2 w-[14.28%]">Description</th>
                  <th className="text-left px-4 py-2 w-[14.28%]">Amount</th>
                  <th className="text-left px-4 py-2 w-[14.28%]">Seats invoiced</th>
                  <th className="text-left px-4 py-2 w-[14.28%]">Status</th>
                  <th className="text-left px-4 py-2 w-[14.28%]">Due Date</th>
                  <th className="text-left px-4 py-2 w-[14.28%]">Action</th>
                </tr>
              </thead>
              <tbody>
                {recentInvoices.map((invoice, index) => (
                  <tr key={index} className="hover:bg-gray-800/50 rounded-lg transition-colors">
                    <td className="px-4 py-3 font-light">{invoice.date}</td>
                    <td className="px-4 py-3 font-light">{invoice.description}</td>
                    <td className="px-4 py-3 font-light">{invoice.amount}</td>
                    <td className="px-4 py-3 font-light">{invoice.seats}</td>
                    <td
                      className={`px-4 py-3 font-light ${invoice.status === "Paid" ? "text-green-400" : "text-yellow-400"
                        }`}
                    >
                      {invoice.status}
                    </td>
                    <td className="px-4 py-3 font-light text-gray-400">{invoice.dueDate || "-"}</td>
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        {invoice.status === "Unpaid" ? (
                          <IconButton
                            icon={<ExternalLink {...iconProps} />}
                            variant="primary"
                            data-tooltip-id="invoice-tooltip"
                            data-tooltip-content="Pay Now"
                            text="Pay Now"
                            onClick={() => handlePayInvoice(invoice.id)}
                          />
                        ) : (
                          <div className="action-group">
                            <IconButton
                              icon={<FaEye {...iconProps} />}
                              variant="secondary"
                              data-tooltip-id="invoice-tooltip"
                              data-tooltip-content="View Invoice"
                              onClick={() => {
                                setSelectedInvoice(dummyInvoiceData);
                                setShowInvoiceModal(true);
                              }}
                            />
                            <IconButton
                              icon={<FaDownload {...iconProps} />}
                              variant="secondary"
                              data-tooltip-id="invoice-tooltip"
                              data-tooltip-content="Download Invoice"
                            />
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Token Calculator Modal */}
        {showTokenCalculator && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 max-w-md w-full mx-4"
            >
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-light">Token Calculator</h3>
                <button
                  onClick={() => setShowTokenCalculator(false)}
                  className="text-gray-400 hover:text-white transition-colors bg-transparent"
                >
                  <FaLock />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="text-sm text-gray-400 font-light mb-2 block">How many tokens would you like?</label>
                  <div className="relative">
                    <input
                      type="number"
                      value={customTokens}
                      onChange={(e) => setCustomTokens(e.target.value)}
                      className="w-full bg-gray-800/50 backdrop-blur-sm rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 font-light"
                      placeholder="Enter amount of tokens..."
                    />
                    <FaCalculator className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400" />
                  </div>
                </div>

                {customTokens && (
                  <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400 font-light">Price:</span>
                      <span className="text-2xl text-green-400 font-light">${calculateTokenPrice(customTokens)}</span>
                    </div>
                    <div className="text-sm text-gray-500 font-light mt-2">At $20 per million tokens</div>
                  </div>
                )}

                <button className="w-full bg-green-500 hover:bg-green-600 py-3 rounded-lg transition-colors font-light">
                  Purchase Tokens
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
      <InvoicePreviewModal
        isOpen={showInvoiceModal}
        onClose={() => setShowInvoiceModal(false)}
        invoiceData={<InvoicePDF invoiceData={selectedInvoice} />}
      />
    </div>
  );
}

export default SettingsBilling;
