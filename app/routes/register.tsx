import React, { useEffect, useState } from 'react';
import '../components/styles/signin.scss';
import { Link, useLocation, useNavigate } from '@remix-run/react';
import CopyRighting from '~/components/common/Footer/copyRight';
import { useUser } from '~/ai/lib/context/userContext';
import { handleGitHubAuth, handleGoogleAuth, signUp } from '~/ai/lib/stores/user/user';
import { UserSignupContextType } from '~/types/user';
import { AnimatePresence, motion } from 'framer-motion';
import bielaLogo from '/biela-logo-only.svg';
import bielaText from '/biela-text.svg';
import { FaCheck, FaEye, FaEyeSlash, FaInfoCircle, FaQuoteRight, FaSpinner } from 'react-icons/fa';
import { LuInfo } from 'react-icons/lu';
import { FaCircleCheck } from 'react-icons/fa6';
import PhoneInputField from '~/components/common/PhoneInputField';
import { IoIosArrowForward } from 'react-icons/io';
import { useTranslation } from 'react-i18next';
import GoogleLogin from '~/assets/icons/google-login.svg';
import GitHubIcon from '~/assets/icons/fi-brands-github.svg';
import LanguageSelector from '~/components/LanguageSelector';
import Cookies from 'js-cookie';
const turnstileSiteKey = import.meta.env.VITE_TURNSTILE_SITE_KEY;

function Register() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation("translation");
  const { t: p } = useTranslation('profile');

  const [formData, setFormData] = useState<UserSignupContextType>({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    phoneNumber: '',
    password: '',
    confirmPassword: '',
    referral: '',
    acceptTerms: false,
  });

  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');
  const { isLoggedIn } = useUser();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [direction, setDirection] = useState(1);
  const [fieldError, setFieldError] = useState<{
    firstName?: string;
    lastName?: string;
    username?: string;
    email?: string;
    phoneNumber?: string;
    password?: string;
    confirmPassword?: string;
    acceptTerms?: string;
  }>({});
  const [loading, setLoading] = useState(false);
  const [showReferralInfo, setShowReferralInfo] = useState(false);
  const [foundInUrl, setFoundInUrl] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [captchaLoaded, setCaptchaLoaded] = useState(false);

  // Company testimonials
  const testimonials = [
    {
      quote: 'Biela has revolutionized our development process, cutting our time-to-market by 70%.',
      company: 'TechGiant Inc.',
      logo: 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      color: 'from-blue-500/20 to-blue-600/20',
    },
    {
      quote: 'The AI-powered code generation has transformed how our engineering team builds products.',
      company: 'InnovateCorp',
      logo: 'https://images.pexels.com/photos/5473298/pexels-photo-5473298.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      color: 'from-purple-500/20 to-purple-600/20',
    },
    {
      quote: 'Biela helped us launch our startup with enterprise-grade code in just weeks instead of months.',
      company: 'StartupBoost',
      logo: 'https://images.pexels.com/photos/5473950/pexels-photo-5473950.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      color: 'from-green-500/20 to-green-600/20',
    },
    {
      quote: "Our developers are now 5x more productive thanks to Biela's AI assistance.",
      company: 'Global Solutions',
      logo: 'https://images.pexels.com/photos/5473954/pexels-photo-5473954.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      color: 'from-yellow-500/20 to-yellow-600/20',
    },
  ];

  // Animation variants for slide effect
  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 500 : -500,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 500 : -500,
      opacity: 0,
    }),
  };

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setDirection(1); // Always slide from right to left
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  useEffect(() => {
    if (!isLoggedIn()) return;
    Cookies.remove('referral');
    setSuccess(t('AlreadyLoggedInRedirectHome', "You're already logged in! Redirecting to home..."));
    setTimeout(() => {
      navigate('/');
    }, 2000);
  }, [navigate, isLoggedIn]);

  function getCookie(name: string) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);

    if (parts.length === 2) {
      return parts.pop()!.split(';').shift();
    }

    return null;
  }
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const urlReferral = params.get('referral');
    const cookieReferral = getCookie('referral');

    if (urlReferral) {
      localStorage.setItem('referral', urlReferral);
      setFormData((prev) => ({ ...prev, referral: urlReferral }));
      setFoundInUrl(true);
    } else if (cookieReferral) {
      localStorage.setItem('referral', cookieReferral);
      setFormData((prev) => ({ ...prev, referral: cookieReferral }));
      setFoundInUrl(true);
    } else {
      const storedReferral = localStorage.getItem('referral');

      if (storedReferral) {
        setFormData((prev) => ({ ...prev, referral: storedReferral }));
        setFoundInUrl(true);
      }
    }
  }, [location.search]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const setPhoneValue = (name: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (loading) return;

    setLoading(true);
    setError('');
    setSuccess('');
    setFieldError({});

    const payload = {
      ...formData,
      email: formData.email.trim().toLowerCase(),
      username: formData.username.trim().toLowerCase(),
    };
    // Validate matching passwords
    if (payload.password.trim() !== payload.confirmPassword.trim()) {
      setLoading(false);
      setError(t('PasswordsMismatch', 'Passwords do not match.'));
      return;
    }

    let hasValidationErrors = false;
    if (!payload.firstName?.trim()) {
      setFieldError((prev) => ({ ...prev, firstName: t('FirstNameRequired', 'First name is required') }));
      hasValidationErrors = true;
    }
    if (!payload.lastName?.trim()) {
      setFieldError((prev) => ({ ...prev, lastName: t('LastNameRequired', 'Last name is required') }));
      hasValidationErrors = true;
    }
    if (!payload.username?.trim()) {
      setFieldError((prev) => ({ ...prev, username: t('UsernameRequired', 'Username is required') }));
      hasValidationErrors = true;
    }
    if (!payload.email?.trim()) {
      setFieldError((prev) => ({ ...prev, email: t('EmailRequired', 'Email is required') }));
      hasValidationErrors = true;
    }
    if (!payload.email?.trim()) {
      setFieldError((prev) => ({ ...prev, email: t('EmailRequired', 'Email is required') }));
      hasValidationErrors = true;
    }
    if (!payload.phoneNumber?.trim()) {
      setFieldError((prev) => ({ ...prev, password: t('PhoneRequired', 'Phone number is Required') }));
      hasValidationErrors = true;
    }
    if (!payload.confirmPassword?.trim()) {
      setFieldError((prev) => ({
        ...prev,
        confirmPassword: t('ConfirmPasswordRequired', 'Please confirm your password'),
      }));
      hasValidationErrors = true;
    }
    if (!payload.acceptTerms) {
      setFieldError((prev) => ({
        ...prev,
        acceptTerms: t('AcceptTermsRequired', 'You must accept the Terms of Service and Privacy Policy'),
      }));
      hasValidationErrors = true;
    }

    if (hasValidationErrors) {
      setLoading(false);
      return;
    }

    try {
      const token = document.querySelector<HTMLInputElement>('[name="cf-turnstile-response"]')?.value;

      if (!token) {
        throw new Error(t('CaptchaRequired', 'Please complete the CAPTCHA'));
      }

      const response = await signUp(payload, token);

      const data = await response.json(); // Parse response once
      if (!response.ok) {
        const errMsg = data.message || t('RegistrationFailed', 'Registration failed');
        throw new Error(errMsg);
      }
      if (data) {
        setSuccess(
          t('EmailConfirmationSent', 'Email confirmation was sent. Please confirm your email and then log in.'),
        );
        setIsSuccess(true);
      } else {
        setError(t('RegistrationServerError', 'Registration failed (server returned false).'));
      }
    } catch (err: any) {
      console.error('Registration error:', err);
      setError(err.message || t('SomethingWentWrong', 'Something went wrong'));

      if (window.turnstile && window.turnstile.reset) {
        window.turnstile.reset('#turnstile-widget');
      }
    } finally {
      setLoading(false);
    }
  };

  const preHandleGitHubAuth = () => {
    setFieldError({});
    setError('');
    setSuccess('');
    handleGitHubAuth();
  };

  const preHandleGoogleAuth = () => {
    setFieldError({});
    setError('');
    setSuccess('');
    const state = crypto.randomUUID().toString();
    handleGoogleAuth(state);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (window.turnstile && document.getElementById('turnstile-widget')) {
        window.turnstile.render('#turnstile-widget', {
          sitekey: turnstileSiteKey,
          theme: 'dark',
          appearance: 'interaction-only',
          'before-interactive-callback': () => {
            document.getElementById('turnstile-widget')?.classList.remove('hidden');
          },
          callback: () => {
            setCaptchaLoaded(true);
          },
        });
        clearInterval(interval);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    document.title = t('meta.register.title') || 'Sign Up for biela.dev – Start Building with AI';
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        'content',
        t('meta.register.description') ||
          'Create your biela.dev account to start building websites and apps using AI-powered prompts.',
      );
    }
  }, [t]);

  return (
    <>
      <div
        className="min-h-screen flex items-center flex-col justify-center p-4 bg-auth"
        style={{
          backgroundImage: 'url(/hero-bg-shade-empty.png)',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
        }}
      >
        {isSuccess ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-8"
          >
            <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaCheck className="text-green-400 text-2xl" />
            </div>
            <h2 className="text-3xl font-light text-white mb-4">Check your email to confirm your registration</h2>
            <p className="text-gray-400 font-light mb-4">We've sent you an email with a confirmation link.</p>
            <Link
              to="/"
              className="text-white !underline font-light mb-8 inline-block flex items-center justify-center gap-1"
            >
              Go to homepage <IoIosArrowForward className="mb-[-4px]" />
            </Link>
            <div className="w-full bg-gray-700 h-1 rounded-full overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: '100%' }}
                transition={{ duration: 3 }}
                className="h-full bg-green-500"
              />
            </div>
          </motion.div>
        ) : (
          <>
            <div
              className="container px-[70px] mx-auto absolute top-0 w-full flex items-center justify-between max-sm:!pl-[40px] max-sm:px-[25px]">
              <Link smooth to="https://biela.dev/" className="flex items-center gap-2 sm:gap-3 group">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{ transform: 'none', height: '32px', minWidth: '40px' }}
                  className="relative"
                >
                  <img
                    src={bielaLogo}
                    alt="BIELA"
                    style={{ position: 'absolute', right: 0, top: '-7px' }}
                    className="h-[80px] min-w-[80px] max-sm:min-w-[70px] max-sm:h-[70px]"
                  />
                  <div
                    className="absolute inset-0 bg-accent/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300" />
                </motion.div>
                <img src={bielaText} alt="biela.dev" className="h-[60px] xl:h-[80px] w-[143px] max-sm:w-[100px]" />
              </Link>

              <div className="bg-black/50 border rounded-lg border-transparent">
                <LanguageSelector />
              </div>
            </div>

            <div className="w-full max-w-6xl flex flex-col md:flex-row items-stretch my-12">
              <div className="w-full md:w-1/2 p-8 md:p-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="w-full max-w-md mx-auto"
                >
                  <h2 className="text-3xl font-thin text-white mb-8">{t('SignUpHeading', 'Sign Up')}</h2>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleChange}
                          className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${
                            fieldError.firstName ? 'border-red-500 focus:ring-red-500' : 'border-white/20'
                          } transition-all`}
                          placeholder={p('firstName', 'First name')}
                        />
                        {fieldError.firstName && <span className="text-red-400 text-sm">{fieldError.firstName}</span>}
                      </div>
                      <div className="space-y-2">
                        <input
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleChange}
                          className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${
                            fieldError.lastName ? 'border-red-500 focus:ring-red-500' : 'border-white/20'
                          } transition-all`}
                          placeholder={p('lastName', 'Last name')}
                        />
                        {fieldError.lastName && <span className="text-red-400 text-sm">{fieldError.lastName}</span>}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <input
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          fieldError.username ? 'border-red-500' : 'border-white/20'
                        } transition-all`}
                        placeholder={p('username', 'User name')}
                      />
                      {fieldError.username && <span className="text-red-400 text-sm">{fieldError.username}</span>}
                    </div>
                    <div className="space-y-2">
                      <input
                        type="text"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          fieldError.email ? 'border-red-500' : 'border-white/20'
                        } transition-all`}
                        placeholder={p('email', 'Email')}
                      />
                      {fieldError.email && <span className="text-red-400 text-sm">{fieldError.email}</span>}
                    </div>
                    <PhoneInputField
                      label="Phone"
                      value={formData.phoneNumber}
                      onChange={(value) => setPhoneValue('phoneNumber', value)}
                      countryCode={'US'}
                      className="w-full"
                      placeholder={true}
                    />

                    {/* <div className="space-y-2">
                      <input
                        type="text"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          fieldError.phone ? 'border-red-500' : 'border-white/20'
                        } transition-all`}
                        placeholder="Phone"
                      />
                      {fieldError.phone && <span className="text-red-400 text-sm">{fieldError.phone}</span>}
                    </div> */}
                    <div className="space-y-2 relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          fieldError.password ? 'border-red-500' : 'border-white/20'
                        } transition-all`}
                        placeholder={t('PasswordPlaceholder', 'Password')}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="bg-transparent absolute right-3 top-2.5 my-auto flex items-center justify-center text-white/50 hover:text-white transition-colors"
                      >
                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                      </button>
                      {fieldError.password && <span className="text-red-400 text-sm">{fieldError.password}</span>}
                    </div>
                    <div className="space-y-2 relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        className={`w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          fieldError.confirmPassword ? 'border-red-500' : 'border-white/20'
                        } transition-all`}
                        placeholder={t('ConfirmPasswordPlaceholder', 'Confirm Password')}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="bg-transparent absolute right-3 top-2.5 my-auto flex items-center justify-center text-white/50 hover:text-white transition-colors"
                      >
                        {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                      </button>
                      {fieldError.confirmPassword && (
                        <span className="text-red-400 text-sm">{fieldError.confirmPassword}</span>
                      )}
                    </div>

                    <div className="space-y-2 relative">
                      <div className="relative">
                        <input
                          type="text"
                          name="referral"
                          readOnly={foundInUrl}
                          disabled={foundInUrl}
                          value={formData.referral}
                          onChange={handleChange}
                          className="w-full bg-white/10 backdrop-blur-sm border rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 border-white/20 transition-all"
                          placeholder={t('ReferralCodeOptional', 'Referral Code (Optional)')}
                        />
                        <div
                          onClick={() => setShowReferralInfo(!showReferralInfo)}
                          className="absolute right-3 top-0 bottom-0 flex items-center justify-center text-white/50 hover:text-white transition-colors cursor-pointer"
                        >
                          <FaInfoCircle />
                        </div>
                      </div>
                      {showReferralInfo && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.95 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.95 }}
                          className="absolute z-10 mt-2 p-3 bg-gray-800/90 backdrop-blur-sm rounded-lg text-white/90 text-sm font-light shadow-lg border border-gray-700/50 w-full"
                        >
                          {t('EnterReferralCode', 'Enter a referral code if you were invited by an existing user.')}
                        </motion.div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center">
                        <div className="relative flex items-center h-5">
                          <input
                            type="checkbox"
                            name="acceptTerms"
                            id="acceptTerms"
                            checked={formData.acceptTerms}
                            onChange={handleChange}
                            className="sr-only"
                          />
                          <div
                            onClick={() => setFormData((prev) => ({ ...prev, acceptTerms: !prev.acceptTerms }))}
                            className={`w-5 h-5 flex items-center justify-center rounded border transition-all cursor-pointer ${
                              formData.acceptTerms
                                ? 'bg-green-500 border-green-500'
                                : 'bg-white/10 backdrop-blur-sm border-white/20'
                            }`}
                          >
                            {formData.acceptTerms && (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-3.5 w-3.5 text-black"
                                viewBox="0 0 20 20"
                                fill="white"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            )}
                          </div>
                        </div>
                        <label htmlFor="acceptTerms" className="ml-2 text-white/70 text-sm cursor-pointer">
                          {t('AcceptTermsPrefix', 'I agree to the')}{' '}
                          <a
                            href="https://biela.dev/terms/"
                            target="_blank"
                            className="text-green-400 hover:text-green-300 transition-colors"
                          >
                            {t('TermsOfService', 'Terms of Service')}
                          </a>{' '}
                          {t('AndSeparator', '&')}{' '}
                          <a
                            href="https://biela.dev/privacy/"
                            target="_blank"
                            className="text-green-400 hover:text-green-300 transition-colors"
                          >
                            {t('PrivacyPolicy', 'Privacy Policy')}
                          </a>
                        </label>
                      </div>
                      {fieldError.acceptTerms && (
                        <span className="text-red-400 text-sm mt-1">{fieldError.acceptTerms}</span>
                      )}
                    </div>
                    <div id="turnstile-widget" className="hidden" />
                    <button
                      type="submit"
                      className={`w-full ${
                        captchaLoaded && !loading && !isLoggedIn()
                          ? 'bg-green-500 hover:bg-green-600'
                          : 'bg-gray-500 opacity-70 cursor-not-allowed'
                      } text-white py-3 rounded-lg transition-colors relative flex items-center justify-center gap-2`}
                      disabled={loading || isLoggedIn() || !captchaLoaded}
                    >
                      {loading ? (
                        <FaSpinner className="animate-spin" />
                      ) : !captchaLoaded ? (
                        <div className="flex items-center gap-2">
                          <FaSpinner className="animate-spin" />
                          <span>{t('loading', 'Loading')}...</span>
                        </div>
                      ) : (
                        t('CreateAccount', 'Create Account')
                      )}
                    </button>

                    {error && (
                      <div className="flex items-center justify-center gap-2 rounded-lg p-3 text-red-400 bg-red-400/5">
                        <LuInfo />
                        <p>{error}</p>
                      </div>
                    )}
                    {success && (
                      <div
                        className="flex items-center justify-center gap-2 rounded-lg p-3 text-green-400 bg-green-400/5">
                        <FaCircleCheck />
                        <p>{success}</p>
                      </div>
                    )}
                  </form>

                  {/*<div className="mt-6 text-center">*/}
                  {/*  <div className="flex items-center">*/}
                  {/*    <div className="w-full border-t border-white/20"></div>*/}
                  {/*    <span className="px-2 text-white/60 text-sm">OR</span>*/}
                  {/*    <div className="w-full border-t border-white/20"></div>*/}
                  {/*  </div>*/}
                  {/*  <button*/}
                  {/*    onClick={preHandleGoogleAuth}*/}
                  {/*    disabled={isLoggedIn()}*/}
                  {/*    className="w-full mt-6 bg-white/10 hover:bg-white/20 border border-white/20 text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-0"*/}
                  {/*  >*/}
                  {/*    <span>Register With Google</span>*/}
                  {/*  </button>*/}
                  {/*  <button*/}
                  {/*    onClick={preHandleGitHubAuth}*/}
                  {/*    disabled={isLoggedIn()}*/}
                  {/*    className="w-full mt-6 bg-white/10 hover:bg-white/20 border border-white/20 text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-0"*/}
                  {/*  >*/}
                  {/*    <img alt="github-login" className="mr-[10px]" src={GitHubIcon} />*/}
                  {/*    <span>Register With GitHub</span>*/}
                  {/*  </button>*/}
                  {/*</div>*/}

                  <div className="mt-6 text-center">
                    <div className="flex items-center">
                      <div className="w-full border-t border-white/20"></div>
                      <span className="px-2 text-white/60 text-sm">OR</span>
                      <div className="w-full border-t border-white/20"></div>
                    </div>

                    {/*<button*/}
                    {/*  onClick={preHandlePaypalAuth}*/}
                    {/*  disabled={isLoggedIn()}*/}
                    {/*  className="w-full mt-6 bg-white/10 hover:bg-white/20 border border-white/20 text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-0"*/}
                    {/*>*/}
                    {/*  <img className="mr-[10px]" src={PaypalLogin} alt={'paypal-login'} />*/}
                    {/*  <span>*/}
                    {/*    {t('LoginWithPaypal', 'Login With Paypal')}*/}
                    {/*  </span>*/}
                    {/*</button>*/}

                    <button
                      onClick={preHandleGoogleAuth}
                      disabled={isLoggedIn()}
                      className="w-full mt-6 bg-white/10 hover:bg-white/20 border border-white/20 text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-0"
                    >
                      <img className="mr-[10px]" src={GoogleLogin} alt={'google-login'} />
                      <span>{t('SignUpWithGoogle', 'Sign Up With Google')}</span>
                    </button>

                    <button
                      onClick={preHandleGitHubAuth}
                      disabled={isLoggedIn()}
                      className="w-full mt-6 bg-white/10 hover:bg-white/20 border border-white/20 text-white py-3 rounded-lg transition-colors flex items-center justify-center gap-0"
                    >
                      <img alt="github-login" className="mr-[10px]" src={GitHubIcon} />
                      <span>{t('SignUpWithGitHub', 'Sign Up With GitHub')}</span>
                    </button>
                  </div>

                  <div className="mt-8 text-center">
                    <p className="text-white/60">
                      {t('AlreadyHaveAccountPrompt', 'Already have an account?')}{' '}
                      <Link to="/login" className="text-green-400 hover:text-green-300 transition-colors">
                        {t('Login', 'Login')}
                      </Link>
                    </p>
                  </div>

                </motion.div>
              </div>

              <div className="hidden md:flex md:w-1/2 p-8 md:p-0 items-center justify-center">
                <div className="w-full max-w-md relative h-[500px] overflow-hidden">
                  <AnimatePresence custom={direction} mode="popLayout">
                    <motion.div
                      key={currentTestimonial}
                      custom={direction}
                      variants={slideVariants}
                      initial="enter"
                      animate="center"
                      exit="exit"
                      transition={{
                        x: { type: 'spring', stiffness: 300, damping: 30 },
                        opacity: { duration: 0.2 }
                      }}
                      className={`bg-gradient-to-r ${testimonials[currentTestimonial].color} rounded-xl p-8 w-full absolute inset-0`}
                    >
                      <div className="absolute top-4 right-4 flex space-x-1">
                        <div className="w-2 h-2 rounded-full bg-white/80"></div>
                        <div className="w-2 h-2 rounded-full bg-white/80"></div>
                        <div className="w-2 h-2 rounded-full bg-white/80"></div>
                      </div>

                      <FaQuoteRight className="text-white/20 text-4xl mb-6" />

                      <h3 className="text-white text-2xl font-light leading-relaxed mb-12">
                        "{testimonials[currentTestimonial].quote}"
                      </h3>

                      <div className="absolute bottom-[80px] left-8 right-8">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 rounded-full overflow-hidden bg-black/20">
                            <img
                              src={testimonials[currentTestimonial].logo}
                              alt={testimonials[currentTestimonial].company}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div>
                            <p className="text-white font-medium">{testimonials[currentTestimonial].company}</p>
                          </div>
                        </div>
                      </div>

                      <div className="absolute bottom-7 left-0 right-0 flex justify-center gap-1">
                        {testimonials.map((_, i) => (
                          <div
                            key={i}
                            className={`w-2 h-2 rounded-full ${i === currentTestimonial ? 'bg-green-500' : 'bg-white/40'}`}
                          />
                        ))}
                      </div>
                    </motion.div>
                  </AnimatePresence>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      <CopyRighting />
    </>
  );
}

export default Register;
