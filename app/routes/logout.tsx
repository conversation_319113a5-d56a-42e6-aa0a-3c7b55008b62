import { useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import { useUser } from '~/ai/lib/context/userContext';

export default function Logout() {
  const navigate = useNavigate();
  const { logout } = useUser();

  useEffect(() => {
    const doLogout = async () => {
      await logout();
      navigate('/login');
    };

    doLogout();
  }, [navigate, logout]);

  return null;
}
