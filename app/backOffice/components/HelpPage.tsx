import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import Wrapper from '~/components/common/Wrapper';
import { PopupSupport } from './PopupSupport';
import {
  FaArrowRight,
  FaBook,
  FaBrain,
  FaCode,
  FaComments,
  FaDiscord,
  FaGithub,
  FaQuestionCircle,
  FaRocket,
  FaSearch
} from 'react-icons/fa';

function HelpPage() {
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useTranslation('translation');

  const categories = [
    {
      title: t('help.categories.getting-started.title', 'Getting Started'),
      description: t('help.categories.getting-started.description', ''),
      icon: FaRocket,
      color: 'from-blue-500/20 to-blue-600/20',
      iconColor: 'text-blue-400',
      articles: t('help.categories.getting-started.articles', { returnObjects: true }),
    },
    {
      title: t('help.categories.ai-development.title', 'AI Development'),
      description: t('help.categories.ai-development.description', 'Master AI-powered development'),
      icon: FaBrain,
      color: 'from-purple-500/20 to-purple-600/20',
      iconColor: 'text-purple-400',
      articles: t('help.categories.ai-development.articles', { returnObjects: true }),
    },
    {
      title: t('help.categories.project-management.title', 'Project Management'),
      description: t('help.categories.project-management.description', 'Organize and manage your projects'),
      icon: FaCode,
      color: 'from-green-500/20 to-green-600/20',
      iconColor: 'text-green-400',
      articles: t('help.categories.project-management.articles', { returnObjects: true }),
    },
  ];

  const supportChannels = [
    {
      name: t('help.channels.docs.name', 'Documentation'),
      description: t('help.channels.docs.description', 'Comprehensive guides and API references'),
      icon: FaBook,
      color: 'from-yellow-500/20 to-yellow-600/20',
      iconColor: 'text-yellow-400',
      link: '#',
    },
    {
      name: t('help.channels.community.name', 'Community'),
      description: t('help.channels.community.description', 'Connect with other developers'),
      icon: FaDiscord,
      color: 'from-indigo-500/20 to-indigo-600/20',
      iconColor: 'text-indigo-400',
      link: '#',
    },
    {
      name: t('help.channels.github.name', 'GitHub'),
      description: t('help.channels.github.description', 'Report issues and contribute'),
      icon: FaGithub,
      color: 'from-gray-500/20 to-gray-600/20',
      iconColor: 'text-gray-400',
      link: '#',
    },
  ];

  return (
    <Wrapper handleSendMessage={() => {}}>
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute top-10 left-10 w-64 h-64 bg-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-80 h-80 bg-cyan-600/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-emerald-600/5 rounded-full blur-3xl"></div>
      </div>
      <div
        className="min-h-screen bg-[#0B0E14]"
        style={{
          backgroundImage: 'url(/hero-2.png)',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'top',
          backgroundSize: '100%',
          backgroundColor: '#0A1730',
        }}
      >
        <div className="container mx-auto px-8 py-8 max-w-[1600px] space-y-12">
          {/* Hero Section */}
          <div className="text-center space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-center gap-3"
            >
              <div className="p-3 bg-gradient-to-r from-blue-500/20 to-blue-600/20 rounded-xl">
                <FaQuestionCircle className="text-2xl text-blue-400" />
              </div>
              <h1 className="text-4xl font-light text-white">{t('help.title', 'How can we help you?')}</h1>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="max-w-2xl mx-auto"
            >
              <div className="relative">
                <input
                  type="text"
                  placeholder={t('help.searchPlaceholder', 'Search documentation...')}
                  className="w-full bg-[#161b25] border border-[#374151] rounded-lg pl-12 pr-4 py-4 text-white placeholder-[#6B7280] focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                />
                <FaSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-500" />
              </div>
            </motion.div>
          </div>
          {/* Categories Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => {
              const Icon = category.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`bg-gradient-to-r ${category.color} rounded-xl p-6 relative overflow-hidden group hover:scale-[1.02] transition-transform text-white`}
                >
                  <div className="absolute -bottom-8 -right-8 text-[200px] opacity-5">
                    <Icon />
                  </div>

                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6 text-white">
                      <div className="p-4 bg-black/20 rounded-xl">
                        <Icon className={`text-2xl ${category.iconColor}`} />
                      </div>
                      <div>
                        <h3 className="text-xl font-light">{category.title}</h3>
                        <p className="text-gray-400 text-sm font-light mt-1">{category.description}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      {Array.isArray(category.articles) ? (
                        category.articles.map((article, idx) => (
                          <Link
                            key={idx}
                            to="#"
                            className="flex items-center justify-between bg-black/20 px-4 py-3 rounded-lg hover:bg-black/30 transition-colors"
                          >
                            <span className="font-light text-white">{article}</span>
                            <FaArrowRight className="text-sm opacity-0 group-hover:opacity-100 transition-opacity" />
                          </Link>
                        ))
                      ) : (
                        <p>{t('help.noArticles')}</p>
                      )}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
          {}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {supportChannels.map((channel, index) => {
              const Icon = channel.icon;
              return (
                <motion.a
                  key={index}
                  href={channel.link}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`bg-gradient-to-r ${channel.color} rounded-xl p-6 relative overflow-hidden group hover:scale-[1.02] transition-transform text-white`}
                >
                  <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
                    <Icon />
                  </div>

                  <div className="relative z-10">
                    <div className="flex items-center gap-4">
                      <div className="p-4 bg-black/20 rounded-xl">
                        <Icon className={`text-2xl ${channel.iconColor}`} />
                      </div>
                      <div>
                        <h3 className="text-xl font-light text-white">{channel.name}</h3>
                        <p className="text-gray-400 text-sm font-light mt-1">{channel.description}</p>
                      </div>
                    </div>
                  </div>
                </motion.a>
              );
            })}
          </div>
          ;{/* Contact Support */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-xl p-8 text-center relative overflow-hidden"
          >
            <div className="absolute -bottom-8 -right-8 text-[200px] opacity-5 text-white">
              <FaComments />
            </div>

            <div className="relative z-10 max-w-2xl mx-auto">
              <h2 className="text-2xl font-light mb-4 text-white">{t('help.support.title', 'Still Need Help?')}</h2>
              <p className="text-gray-300 font-light mb-6">{t('help.support.description', 'Our support team is available 24/7 to help you with any questions or issues you may have.')}</p>
              <button
                onClick={() => setIsOpen(true)}
                className="bg-green-500 hover:bg-green-600 px-8 py-3 rounded-lg transition-colors flex items-center gap-2 mx-auto text-white"
              >
                <FaComments />
                <span className="font-light text-white">{t('help.support.button', 'Contact Support')}</span>
              </button>
              <PopupSupport isOpen={isOpen} onClose={() => setIsOpen(false)} />
            </div>
          </motion.div>
        </div>
      </div>
    </Wrapper>
  );
}

export default HelpPage;
