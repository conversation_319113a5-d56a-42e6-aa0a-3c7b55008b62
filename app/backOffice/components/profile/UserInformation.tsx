import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FaBuilding, FaCamera, FaGlobe, FaLink, FaMapMarkerAlt } from 'react-icons/fa';
import { Copy } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { countries, languages } from '~/backOffice/components/profile/languageList';
import {
  InfoSectionProps,
  InputUserFieldProps,
  IUserGeneral,
  SectionProps,
  SelectFieldProps
} from '~/backOffice/types/profile';
import ArrowDown from '~/assets/icons/arrowIcon.svg';
import './toast.css';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { useUser } from '~/ai/lib/context/userContext';
import { UploadImageModal } from '~/backOffice/components/profile/UploadImageModal';
import { detectCountryByIP, validateCountryName } from '~/utils/countryDetection';
import userImg from '/images/user3.svg';
import PhoneInputField from '~/components/common/PhoneInputField';
import { Dialog, DialogButton, DialogTitle } from '~/ai/components/Dialog';
import WithTooltip from '~/ai/components/Tooltip';
import { toast } from 'react-toastify';

type UserInformationProps = IUserGeneral & {
  isEditing: boolean
  setIsEditing: (value: boolean) => void
  handleSave: () => Promise<boolean>
  loading: boolean

  contactInfo: { email: string; phone: string }
  newPhone: string
  newEmail: string
  originalEmail: string
  originalPhone: string
  isPhoneVerified: boolean
  setIsPhoneVerified: (value: boolean) => void
  isEmailVerified: boolean
  setIsEmailVerified: (value: boolean) => void
  verificationField: 'email' | 'phone' | null
  setVerificationField: (value: 'email' | 'phone' | null) => void
  handleChangeContactInfo: (field: keyof IUserGeneral, value: string) => void
  handleVerify: (field: 'email' | 'phone') => void
  isOTPDialogOpen: boolean
  setOTPDialogOpen: (value: boolean) => void

  errors: Partial<Record<keyof IUserData, string>>
  handleBlur: (field: keyof IUserData) => void
}
const UserInformation: React.FC<UserInformationProps> = ({
   userData,
   handleChange,
   isEditing,
   setIsEditing,
   handleSave,
   loading,
   contactInfo,
   newPhone,
   newEmail,
   originalEmail,
   originalPhone,
   isPhoneVerified,
   setIsPhoneVerified,
   isEmailVerified,
   setIsEmailVerified,
   verificationField,
   setVerificationField,
   handleChangeContactInfo,
   handleVerify,
   isOTPDialogOpen,
   setOTPDialogOpen,

   errors,
   handleBlur,
 }) => {
  const { t } = useTranslation('profile');
  const { i18n } = useTranslation();
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const defaultProfileImage = userImg;
  const { user, setUser } = useUser();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [isDetectingCountry, setIsDetectingCountry] = useState(false);
  const emailRef = useRef<HTMLInputElement>(null);
  const phoneRef = useRef<HTMLInputElement>(null);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch {
      toast.error('Copy failed. Please try again.');
    }
  };

  const memoizedAvatarUrl = useMemo(
    () =>
      typeof userData.profilePicture === 'string'
        ? userData.profilePicture
        : defaultProfileImage,
    [userData.profilePicture, defaultProfileImage]
  );

  const handleCountryDetection = async () => {
    if (!isDetectingCountry) {
      setIsDetectingCountry(true);

      try {
        const detectedCountry = await detectCountryByIP();

        if (detectedCountry) {
          const validatedCountry = validateCountryName(detectedCountry, countries);

          if (validatedCountry) {
            handleChange('country', validatedCountry);
          }
        }
      } catch (error) {
        console.error('Error in country detection:', error);
      } finally {
        setIsDetectingCountry(false);
      }
    }
  };

  useEffect(() => {
    if (!loading && !userData.country) {
      handleCountryDetection();
    }
  }, [userData.country, loading]);

  const handleLanguageChange = (val: string) => {
    const selectedLanguage = languages.find((lang) => lang.value === val);

    if (selectedLanguage) {
      i18n.changeLanguage(selectedLanguage.value).then(() => {
        handleChange('preferredLanguage', selectedLanguage.value);
      });
    }
  };

  const submitImageCropped = async (blob: Blob) => {
    const formData = new FormData();
    formData.append('file', blob);

    try {
      setIsUploading(true);

      const response = await backendApiFetch(`/user/profile/picture`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw await response.text();
      }

      const data: { profilePictureUrl: string } = await response.json();
      handleChange('profilePicture', data.profilePictureUrl);

      if (user) {
        const updatedUser = {
          ...user,
          profilePicture: data.profilePictureUrl ?? '',
        };

        setUser(updatedUser);
        localStorage.setItem('user', JSON.stringify(updatedUser));
      }

      toast.success('Image updated successfully');
      setSelectedImage(null);
    } catch (error: any) {
      const message = typeof error === 'string' ? error : 'Unknown error';
      toast.error(message);
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    event.target.value = '';

    if (!file) {
      return;
    }

    if (!file.type.match(/^image\/(jpeg|png|gif|jpg)$/)) {
      alert('Only image files (JPEG, PNG, GIF) are allowed!');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB!');
      return;
    }

    setSelectedImage(file);
  };

  const getCountryCode = () => {
    const country = countries.find((c) => c.name === userData.country);
    return country ? country.code : 'US';
  };

  const [otpCode, setOtpCode] = useState('');

  const closeOTPDialog = () => setOTPDialogOpen(false);

  const handleOTPSubmit = async () => {
    if (!otpCode) {
      toast.error('Please enter the OTP code');
      return;
    }

    try {
      const response = await backendApiFetch(`/user-verification/${otpCode}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('OTP Verified Successfully!');
        closeOTPDialog();

        if (verificationField === 'email' && newEmail === contactInfo.email) {
          setIsEmailVerified(true);
        } else if (verificationField === 'phone' && newPhone === contactInfo.phone) {
          setIsPhoneVerified(true);
        }

        setVerificationField(null);
      } else {
        toast.error(data.message || 'Invalid OTP, please try again.');
      }
    } catch (err) {
      console.error(err);
      toast.error('Something went wrong. Please try again.');
    }
  };

  useEffect(() => {
    const changeLang = async () => {
      if (userData.preferredLanguage) {
        const selectedLanguage = languages.find((lang) => lang.value === userData.preferredLanguage);

        if (selectedLanguage) {
          await i18n.changeLanguage(selectedLanguage.value);
        }
      }
    };

    void changeLang();
  }, [userData.preferredLanguage, i18n]);

  const handleToggleEdit = () => {
    setIsEditing(!isEditing);
  };

  const onHandleSave = async () => {
    if (isEditing) {
      const saved: boolean = await handleSave();
      if(!saved) return;
    }

    setIsEditing(!isEditing);
  };

  const Section: React.FC<SectionProps> = ({ title, icon: Icon, children, className }) => (
    <div
      className={`bg-gradient-to-r from-[#1f293780] to-[#11182780] rounded-xl p-6 relative overflow-hidden hover:border hover:border-green-500 ${className} border-[#191429] border-1 `}
    >
      <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
        <Icon className={'text-[#e5e7eb]'} />
      </div>
      <div className="relative z-10 space-y-6">
        <h3 className="text-xl font-light text-white">{title}</h3>
        {children}
      </div>
    </div>
  );

  const cursorPositionRef = useRef<number>(0);

  const handleEmailChange = useCallback(
    (val: string) => {
      if (emailRef.current) {
        cursorPositionRef.current = emailRef.current.selectionStart || 0;
      }
      handleChangeContactInfo('email', val);
    },
    [handleChangeContactInfo]
  );

  const handlePhoneChange = useCallback(
    (val: string) => {
      if (phoneRef.current) {
        cursorPositionRef.current = phoneRef.current.selectionStart || 0;
      }
      handleChangeContactInfo('phone', val);
    },
    [handleChangeContactInfo]
  );

  useEffect(() => {
    if (isEditing && contactInfo.email !== originalEmail && emailRef.current) {
      emailRef.current.focus();
      emailRef.current.setSelectionRange(cursorPositionRef.current, cursorPositionRef.current);
    }
  }, [isEditing, contactInfo.email, originalEmail]);

  useEffect(() => {
    if (isEditing && contactInfo.phone !== originalPhone && phoneRef.current) {
      const input = phoneRef.current;
      input.focus();
      input.setSelectionRange(cursorPositionRef.current, cursorPositionRef.current);
    }
  }, [isEditing, contactInfo.phone]);

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-[#1f293780] to-gray-[#11182780] backdrop-blur-sm rounded-xl p-6 relative overflow-hidden h-33.5 hover:border hover:border-green-500 border-[#191429] border-1">
        <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
          <FaCamera className={'text-[#e5e7eb]'} />
        </div>
        <div className="relative z-10 flex items-center gap-6">
          <div className="relative group">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImageUpload}
              accept="image/jpeg,image/png,image/gif,image/jpg"
              className="hidden"
            />
            {loading || isUploading ? (
              <div className="w-24 h-24 rounded-full relative">
                <div className="absolute inset-0 rounded-full border-4 border-gray-200 opacity-25" />
                <div className="absolute inset-0 rounded-full border-4 border-green-500 border-t-transparent animate-spin" />
                <div className="absolute inset-2 rounded-full bg-gray-800 flex items-center justify-center">
                  <div className="w-12 h-12 rounded-full bg-green-500/20 animate-pulse" />
                </div>
              </div>
            ) : (
              <img
                crossOrigin="anonymous"
                src={memoizedAvatarUrl}
                alt={`${userData.firstName} ${userData.lastName}`}
                className="w-24 h-24 rounded-full object-cover border-2 border-yellow-400"
              />
            )}
            {isEditing && (
              <button
                onClick={() => fileInputRef.current?.click()}
                className="absolute bottom-0 right-0 bg-green-500 p-2 rounded-full text-white opacity-100 transition-opacity"
                disabled={isUploading}
              >
                {isUploading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <FaCamera className="text-sm" />
                )}
              </button>
            )}
          </div>
          <div>
            <h2 className="text-2xl font-light text-white truncate max-w-[200px]">
              {userData.firstName} {userData.lastName}
            </h2>
          </div>
        </div>
        <div className="absolute right-5 bottom-1 lg:top-1/2 lg:-translate-y-1/2 flex items-center gap-2 z-10">
          <button
            onClick={isEditing ? onHandleSave : handleToggleEdit}
            className="px-2 lg:px-4 py-1 lg:py-2 min-w-[70px] text-base flex justify-center bg-green-500 text-white rounded-lg shadow-lg cursor-pointer transition duration-300"
          >
            {isEditing ? t('Save', 'Save') : t('Edit', 'Edit')}
          </button>
        </div>
      </div>

      <InfoSection
        icon={FaBuilding}
        title={t('userInformation', 'User Information')}
        childrenClassName={'grid-cols-1 md:grid-cols-2'}
      >
        <InputField
           label={t('firstName','First Name')}
           value={userData.firstName}
           onChange={val => handleChange('firstName', val)}
           onBlur={() => handleBlur('firstName')}
           error={errors.firstName}
           disabled={!isEditing}
        />
        <InputField
          label={t('lastName', 'Last Name')}
          value={userData.lastName}
          onChange={(val) => handleChange('lastName', val)}
          onBlur={() => handleBlur('lastName')}
          error={errors.lastName}
          disabled={!isEditing}
        />
        <InputField
          label={t('username', 'User Name')}
          value={userData.username}
          onChange={(val) => handleChange('username', val)}
          onBlur={() => handleBlur('username')}
          error={errors.username}
          disabled={!isEditing}
        />
        <SelectField
          label={t('preferredLanguage', 'Preferred Language')}
          options={languages.map((lang) => ({ label: lang.label, value: lang.value }))}
          value={userData.preferredLanguage}
          onChange={handleLanguageChange}
          disabled={!isEditing}
        />
      </InfoSection>
      <Section title={t('contactInfo', 'Contact Info')} icon={FaGlobe}>

        <div className="flex items-end gap-2">
          <InputField
            label={t('email', 'Email')}
            type="email"
            inputRef={emailRef}
            value={contactInfo.email}
            onChange={handleEmailChange}
            onBlur={() => handleBlur('email')}
            disabled={!isEditing}
            className="w-full"
          />
          {contactInfo.email !== originalEmail && !isEmailVerified && (
            <button
              onClick={() => handleVerify('email')}
              className={`mb-2.5 px-2 py-1 bg-blue-500 text-white rounded-lg shadow-lg transition duration-300 ${
                contactInfo.email === originalEmail || isEmailVerified ? 'opacity-0 pointer-events-none' : ''
              }`}
            >
              Verify
            </button>
          )}
        </div>

        <div className="flex items-end gap-2 w-full">
          <PhoneInputField
            label={t('phone', 'Phone')}
            value={contactInfo?.phone || ''}
            onChange={handlePhoneChange}
            disabled={!isEditing}
            countryCode={getCountryCode()}
            className="w-full"
            onReady={(el) => {
              phoneRef.current = el;
            }}
          />
          {contactInfo.phone !== originalPhone && !isPhoneVerified && (
            <button
              onClick={() => handleVerify('phone')}
              className="mb-2.5 px-2 py-1 bg-blue-500 text-white rounded-lg shadow-lg transition duration-300"
            >
              Verify
            </button>
          )}
        </div>
      </Section>
      {isOTPDialogOpen && (
        <Dialog hasRoot={true} onBackdrop={closeOTPDialog}>
          <DialogTitle>Enter OTP Code</DialogTitle>
          <div className="p-6">
            <p className="text-gray-400">Please enter the OTP code received.</p>
            <input
              type="text"
              value={otpCode}
              onChange={(e) => setOtpCode(e.target.value)}
              className="w-full mt-4 px-4 py-3 border border-gray-700 rounded-lg bg-gray-900 text-white"
            />
            <div className="flex justify-end gap-3 mt-6">
              <DialogButton type="secondary" onClick={closeOTPDialog}>
                Cancel
              </DialogButton>
              <DialogButton type="primary" onClick={handleOTPSubmit}>
                Submit
              </DialogButton>
            </div>
          </div>
        </Dialog>
      )}
        <InfoSection
          icon={FaMapMarkerAlt}
          title={t('billingInformation', 'Billing Information')}
          childrenClassName="grid-cols-2"
        >
        <InputField
          label={t('addressLine1', 'Address Line 1')}
          value={userData.address1}
          onChange={(val) => handleChange('address1', val)}
          onBlur={() => handleBlur('address1')}
          error={errors.address1}
          className="col-span-2"
          disabled={!isEditing}
        />
        <InputField
          label={t('addressLine2', 'Address Line 2')}
          value={userData.address2}
          onChange={(val) => handleChange('address2', val)}
          onBlur={() => handleBlur('address2')}
          error={errors.address2}
          className="col-span-2"
          disabled={!isEditing}
        />
        <InputField
          label={t('city', 'City')}
          value={userData.city}
          onChange={(val) => handleChange('city', val)}
          className="col-span-2 md:col-span-1"
          onBlur={() => handleBlur('city')}
          error={errors.city}
          disabled={!isEditing}
        />
        <div className="col-span-2 md:col-span-1 relative">
          <SelectField
            label={t('Country', 'Country')}
            options={countries.map((item) => ({
              label: `${item.flag} ${item.name}`,
              value: item.name,
            }))}
            value={userData.country}
            onChange={(val) => handleChange('country', val)}
            disabled={!isEditing || isDetectingCountry}
          />
          {isDetectingCountry && (
            <div className="absolute right-10 top-1/2 mt-1">
              <div className="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>
        <InputField
          label={t('zipCode', 'Zip Code')}
          value={userData.zipCode}
          onChange={(val) => handleChange('zipCode', val)}
          pattern="^\d{5}(-\d{4})?$"
          onBlur={() => handleBlur('zipCode')}
          error={errors.zipCode}
          disabled={!isEditing}
        />
      </InfoSection>

      <InfoSection icon={FaLink} title={t('referral', 'Referral')} childrenClassName={'grid-cols-1'}>

        <div className="space-y-2">
          <label className="text-gray-400 text-sm font-light">
            {t('referralCode', 'Referral Code')}
          </label>
          <div className="relative">
            <input
              type="text"
              value={userData.referralCode || ''}
              disabled
              className="w-full bg-black/20  border border-gray-700 rounded-lg px-4 py-3 pr-10 text-white font-light focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"/>
            <WithTooltip tooltip={t('copyReferralCode', 'Copy Referral Code')}>
              <button
                onClick={() => copyToClipboard(userData.referralCode || '')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center justify-center bg-transparent">
                <Copy size={18} color="#fff" />
              </button>
            </WithTooltip>

          </div>
        </div>

        <div className="space-y-2">
          <label className="text-gray-400 text-sm font-light">
            {t('referralLink', 'Referral Link')}
          </label>
          <div className="relative">
            <input
              type="text"
              value={userData.referralLink}
              disabled
              className="w-full bg-black/20 border border-gray-700 rounded-lg px-4 py-3 pr-10  text-white font-light focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            />
            <WithTooltip tooltip={t('copyReferralLink', 'Copy Referral Link')}>
              <button
                onClick={() => copyToClipboard(userData.referralLink)}
                className="absolute bg-transparent right-3 top-1/2  transform -translate-y-1/2 flex items-center justify-center"
              >
                <Copy size={18} color="#fff" />
              </button>
            </WithTooltip>
          </div>
        </div>
      </InfoSection>


      {selectedImage && (
        <UploadImageModal image={selectedImage} onClose={() => setSelectedImage(null)} onSubmit={submitImageCropped} />
      )}
    </div>
  );
};

const InfoSection: React.FC<InfoSectionProps> = ({ icon: Icon, title, children, childrenClassName }) => (
  <div
    className="bg-gradient-to-r from-[#1f293780] to-[#11182780] backdrop-blur-sm rounded-xl p-6 relative overflow-hidden hover:border hover:border-green-500 border-[#191429] border-1">
    <div className="absolute -bottom-8 -right-8 text-[150px] opacity-5">
      <Icon className={'text-[#e5e7eb]'} />
    </div>
    <div className="relative z-10 space-y-6">
      <h3 className="text-xl font-light text-white">{title}</h3>
      <div className={`grid ${childrenClassName} gap-6`}>{children}</div>
    </div>
  </div>
);

interface InputUserFieldProps {
  label: string;
  value: string;
  onChange: (val: string) => void;
  className?: string;
  disabled?: boolean;
  pattern?: string
  inputRef?: React.Ref<HTMLInputElement>
  onBlur?: () => void
  error?: string
}

const InputField: React.FC<InputUserFieldProps> = ({ className = '',label, value, onChange, disabled, pattern, inputRef, onBlur, error, }) => (
  <div className={`space-y-2 ${className}`}>
    <label className="text-gray-400 text-sm font-light">{label}</label>
    <input
      ref={inputRef}
      type="text"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      onBlur={onBlur}
      className="w-full bg-black/20 border border-gray-700 rounded-lg px-4 py-3 text-white font-light focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
      disabled={disabled}
      pattern={pattern}
      title={`Please follow the correct format`}
    />
    {error && <p className="text-red-500 text-sm">{error}</p>}
  </div>
);

const SelectField: React.FC<SelectFieldProps> = ({ label, options, value, onChange, className, disabled }) => {
  const { t } = useTranslation('profile');

  return (
    <div className={`relative space-y-2 ${className}`}>
      <label className="text-gray-400 text-sm font-light">{t(label)}</label>
      <select
        value={value}
        disabled={disabled}
        onChange={(e) => onChange(e.target.value)}
        className="appearance-none w-full bg-black/20 border border-gray-700 rounded-lg px-4 py-3 text-white font-light focus:outline-none focus:ring-2 focus:ring-green-500 transition-all pr-10"
      >
        {options.map((option) =>
          typeof option === 'string' ? (
            <option key={option} value={option}>
              {option}
            </option>
          ) : (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ),
        )}
      </select>
      <img src={ArrowDown} alt="Dropdown Arrow" className="absolute right-3 top-1/2 w-4 h-4 pointer-events-none" />
    </div>
  );
};

export default UserInformation;
