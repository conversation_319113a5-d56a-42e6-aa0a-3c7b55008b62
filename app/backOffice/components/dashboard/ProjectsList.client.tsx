import { useCallback, useEffect, useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { db, deleteById, updateChatDescription, useChatHistory } from '~/ai/lib/persistence';
import IconButton from '~/backOffice/components/dashboard/IconButton';
import {
  Copy,
  Download,
  Edit,
  ExternalLink,
  File,
  FolderSymlink,
  Heart,
  Info,
  Plus,
  RefreshCw,
  Sparkles,
  Trash2,
  Trophy
} from 'lucide-react';
import {
  FaCheck,
  FaChevronLeft,
  FaChevronRight,
  FaCode,
  FaExclamationTriangle,
  FaFolder, FaPaperPlane,
  FaSearch,
  FaSpinner,
  FaTimes,
  FaTrash
} from 'react-icons/fa';
import {
  changeProjectFolder,
  deleteProject,
  deleteProjectFolder,
  fetchContestProjects,
  fetchProjects,
  getFolders,
  postProjectToContest,
  Project,
  updateContestProject,
  updateProjectName
} from '~/api/projectsApi';
import { useNavigate } from '@remix-run/react';
import ProjectMetrics from './ProjectMetrics';
import debounce from 'lodash/debounce';
import SubmissionModal from './SubmissionModal';
import ContestIconButton from './ContestIconButton';
import IconButtonRightIcon from '~/backOffice/components/dashboard/IconButtonRightIcon';
import TransferModal from '~/backOffice/components/project/TransferProjectModal';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { toast } from 'react-toastify';

type FolderType = {
  userId: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  projectIds: string[];
  _id: string;
  __v: number;
};

type ProjectsListProps = {
  projects: Project[];
  selectedType: string;
  totalPagesNumber: number;
  setTotalPagesNumber: (totalPages: number) => void;
  nextPage: (number: number) => void;
  selectedFolder: string;
  loading: boolean;
  refreshProjects: () => void;
  filterLoading: boolean;
  publishedProjects: string[];
  updatePublishedProjects: (projectSlug: string, isPublished: boolean) => void;
};

function ProjectsListClient({
  projects,
  selectedFolder,
  selectedType,
  nextPage,
  totalPagesNumber,
  setTotalPagesNumber,
  loading,
  refreshProjects,
  filterLoading,
  publishedProjects,
  updatePublishedProjects,
  triggerRefreshContestProjects,
}: ProjectsListProps & { triggerRefreshContestProjects: () => void }) {
  const { t } = useTranslation('translation');
  const [currentPage, setCurrentPage] = useState(1);
  const [initialPage, setInitialPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchedProjects, setSearchedProjects] = useState<Project[] | null>(null);
  const [searchLoading, setSearchLoading] = useState(false);
  const [showChangeFolderModal, setShowChangeFolderModal] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [targetFolderId, setTargetFolderId] = useState<string | null>(null);
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [folders, setFolders] = useState<FolderType[]>([]);
  const { downloadProject, exportChat, duplicateCurrentChat } = useChatHistory();
  const [deletingProject, setDeletingProject] = useState<string | null>(null);
  const [loadingProjectId, setLoadingProjectId] = useState<string | null>(null);
  const [changingFolderProcess, setChangingFolderProcess] = useState(false);
  const [editingProjectId, setEditingProjectId] = useState<string | null>(null);
  const [editedProjectName, setEditedProjectName] = useState<string>('');
  const [originalProjectName, setOriginalProjectName] = useState<string>('');
  const [savingProjectName, setSavingProjectName] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<{ id: string; slug: string } | null>(null);
  const [deletingInProgress, setDeletingInProgress] = useState(false);
  const [deleteSuccess, setDeleteSuccess] = useState(false);
  const [exportChatId, setExportChatId] = useState<string | null>(null);
  const [duplicatingProjectId, setDuplicatingProjectId] = useState<string | null>(null);
  const [downloadingProjectId, setDownloadingProjectId] = useState<string | null>(null);
  const navigate = useNavigate();
  const [hiddenCodeIcons, setHiddenCodeIcons] = useState<number[]>([]);
  const iconProps = { size: 18, strokeWidth: 1.5 };
  const [contestProjects, setContestProjects] = useState([]);
  const [modalType, setModalType] = useState<'publish' | 'refresh'>('publish');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalState, setModalState] = useState<'loading' | 'success' | 'error'>('loading');
  const [currentProjectSlug, setCurrentProjectSlug] = useState<string | null>(null);
  const projectsContainerRef = useRef<HTMLDivElement>(null);
  const [showTransferProjectModal, setShowTransferProjectModal] = useState<string|boolean>(false);
  const [loadingTransferProjects, setLoadingTransferProjects] = useState<boolean>(false);
  const [transferError, setTransferError] = useState<string | null>(null);
  useEffect(() => {
    const fetchContestData = async () => {
      try {
        const data = await fetchContestProjects();
        setContestProjects(data.projects || []);
      } catch (error) {
        console.error('Error fetching contest projects:', error);
      }
    };

    fetchContestData();
  }, []);

  const getContestProjectId = (projectId: string): string | undefined => {
    // Verificăm dacă proiectul există în displayedProjects
    const projectExists = displayedProjects.some((project) => project._id === projectId);

    if (!projectExists) {
      // Dacă nu există, nu se poate avea un contestProject asociat
      return undefined;
    }

    // Căutăm în contestProjects un obiect care are projectId-ul egal cu cel primit ca parametru
    const contestProject = contestProjects.find((contestProj) => contestProj.projectId === projectId);

    // Dacă am găsit, returnăm id-ul din contestProject; altfel, returnăm undefined
    return contestProject ? contestProject.id : undefined;
  };

  const handlePublishToContest = async (projectSlug: string) => {
    setCurrentProjectSlug(projectSlug);
    setModalType('publish');
    setModalState('loading');
    setIsModalOpen(true);

    try {
      const response = await submitProjectContest(projectSlug);

      if (response && response.id) {
        setModalState('success');

        if (!publishedProjects.includes(projectSlug)) {
          publishedProjects.push(projectSlug);
        }

        updatePublishedProjects(projectSlug, true);
        triggerRefreshContestProjects();
      } else {
        console.error('Unexpected response format:', response);
        setModalState('error');
      }
    } catch (error: any) {
      console.error('Error publishing project to contest:', error);
      setModalState('error');
    }
  };

  const handleRefreshContestSubmission = async (projectSlug: string) => {
    setCurrentProjectSlug(projectSlug);
    setModalType('refresh');
    setModalState('loading');
    setIsModalOpen(true);

    try {
      await updateContestProject(projectSlug);

      const updatedContestProjects = await fetchContestProjects();
      setContestProjects(updatedContestProjects.projects || []);

      setModalState('success');
    } catch (error: any) {
      console.error('Error refreshing contest submission:', error);
      setModalState('error');
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentProjectSlug(null);
    setModalState('loading');
  };

  const handleViewContest = () => {
    window.open('https://biela.dev/contest', '_blank');
    handleCloseModal();
  };

  const isProjectPublished = (projectSlug: string) => {
    return publishedProjects.includes(projectSlug);
  };

  const getProjectLikes = (projectSlug: string) => {
    const contestProject = contestProjects.find((contestProject) => contestProject.projectSlug === projectSlug);
    return contestProject ? contestProject.likesCount : 0;
  };

  const fetchFolders = async () => {
    try {
      const res = await getFolders();
      setFolders(res);
    } catch (error) {
      console.error('Error fetching folders:', error);
    }
  };

  useEffect(() => {
    fetchFolders();
  }, []);

  const submitProjectContest = async (projectSlug: string) => {
    try {
      const response = await postProjectToContest(projectSlug);

      return response;
    } catch (error) {
      console.error('Error posting project:', error);
      throw error;
    }
  };

  const enhancedRefreshProjects = async () => {
    try {
      await refreshProjects();

      if (displayedProjects.length === 0 && currentPage > 1) {
        handlePageChange(currentPage - 1);
      }
    } catch (error) {
      console.error("Error refreshing projects:", error);
    }
  };

  const handleOpenProject = (projectId: string) => {
    setLoadingProjectId(projectId);
    navigate('/chat/' + projectId);
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);

    if (searchTerm.trim() !== '') {
      handleSearch(searchTerm, newPage);
    } else {
      nextPage(newPage - 1);
    }

    setTimeout(() => {
      if (projectsContainerRef.current) {
        const element = projectsContainerRef.current;
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.scrollY - 180;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    }, 100);
  };

  const handleSearch = useCallback(
    debounce(async (term: string, page: number) => {
      setSearchLoading(true);

      if (term.trim() === '') {
        setSearchedProjects(null);
        setCurrentPage(1);
        nextPage(0);
        setSearchLoading(false);
        await enhancedRefreshProjects();
        return;
      }

      try {
        const folderId = selectedFolder === 'all' ? undefined : selectedFolder;
        const result = await fetchProjects(folderId, page - 1, 10, 'updatedAt', 'DESC', term);

        setSearchedProjects(result.items);
        setTotalPagesNumber(result.totalPages || 1);
        setCurrentPage(page);
      } catch (error) {
        console.error('Error searching projects:', error);
        setSearchedProjects([]);
      } finally {
        setSearchLoading(false);
      }
    }, 500),
    [selectedFolder, nextPage, setTotalPagesNumber]
  );

  const [prevSearchTerm, setPrevSearchTerm] = useState('');
  useEffect(() => {
    if (searchTerm !== prevSearchTerm) {
      if (prevSearchTerm === '' && searchTerm !== '') {
        setInitialPage(currentPage);
      }

      setPrevSearchTerm(searchTerm);
      handleSearch(searchTerm, searchTerm ? 1 : initialPage);
    }
  }, [searchTerm, handleSearch, prevSearchTerm, currentPage]);

  const displayedProjects = searchedProjects !== null ? searchedProjects : projects;

  const paginatedProjects = displayedProjects;

  useEffect(() => {
    if (displayedProjects.length === 0 && currentPage > 1 && !loading && !filterLoading && !searchLoading) {
      handlePageChange(currentPage - 1);
    }
  }, [displayedProjects, currentPage, loading, filterLoading, searchLoading]);

  useEffect(() => {
    getFolders().then((res) => setFolders(res));
  }, [showChangeFolderModal]);

  const formatDate = (dateString: string) => {
    if (!dateString) {
      return 'Invalid date';
    }

    const date = new Date(dateString);

    if (isNaN(date.getTime())) {
      const parsedDate = dateString.split('T')[0];
      return parsedDate || 'Invalid date';
    }

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
    });
  };

  const handleOpenChangeFolderModal = (projectId: number | string) => {
    const projectIdStr = projectId.toString();
    setSelectedProjectId(projectIdStr);

    const currentFolder = folders.find((folder) => folder.projectIds.includes(projectIdStr));
    setCurrentFolderId(currentFolder ? currentFolder._id : null);
    setTargetFolderId(currentFolder ? currentFolder._id : null);
    setShowChangeFolderModal(true);
  };

  const handleConfirmChangeFolder = async () => {
    if (!targetFolderId || !selectedProjectId) {
      return;
    }

    setChangingFolderProcess(true);

    try {
      const currentFolder = folders.find((folder) => folder.projectIds.includes(selectedProjectId));

      if (currentFolder && currentFolder._id !== targetFolderId) {
        await deleteProjectFolder(selectedProjectId, currentFolder._id);
      }

      await changeProjectFolder(targetFolderId, selectedProjectId || '');
      setShowChangeFolderModal(false);
      setSelectedProjectId(null);
      setTargetFolderId(null);
      setCurrentFolderId(null);
      await enhancedRefreshProjects();
    } catch (error) {
      console.error('Error changing project folder:', error);
    } finally {
      setChangingFolderProcess(false);
    }
  };

  const handleExportChat = (projectSlug: string) => {
    executeWithMinLoadingTime(() => exportChat(projectSlug), setExportChatId, projectSlug, 'Error exporting chat:');
  };

  const handleDuplicateProject = (projectSlug: string) => {
    executeWithMinLoadingTime(
      () => duplicateCurrentChat(projectSlug),
      setDuplicatingProjectId,
      projectSlug,
      'Error duplicating project:',
    );
  };

  const handleDownloadProject = (projectSlug: string) => {
    executeWithMinLoadingTime(
      () => downloadProject(projectSlug),
      setDownloadingProjectId,
      projectSlug,
      'Error downloading project:',
    );
  };

  const handleEditProjectName = (projectId: string, currentName: string) => {
    setEditingProjectId(projectId);
    setEditedProjectName(currentName);
    setOriginalProjectName(currentName);
  };

  const handleSaveProjectName = async (projectId: string) => {
    if (editedProjectName.trim() === '' || editedProjectName === originalProjectName) {
      return;
    }

    setSavingProjectName(true);

    try {
      const project = projects.find((p) => p._id.toString() === projectId);

      if (!project || !project.projectSlug) {
        throw new Error('Project not found or missing projectSlug');
      }

      await updateProjectName(project.projectSlug, editedProjectName);

      // Update the project name to local storage also
      if (db) {
        await updateChatDescription(db, project.projectSlug.split('-')[0].toString(), editedProjectName);
      }

      await refreshProjects();
      setEditingProjectId(null);
      setEditedProjectName('');
      setOriginalProjectName('');
    } catch (error) {
      console.error('Error updating project name:', error);
    } finally {
      setSavingProjectName(false);
    }
  };

  const handleCancelEditProjectName = () => {
    setEditingProjectId(null);
    setEditedProjectName('');
    setOriginalProjectName('');
  };

  const handleOpenDeleteConfirm = (projectId: string, projectSlug: string) => {
    setProjectToDelete({ id: projectId, slug: projectSlug });
    setShowDeleteConfirm(true);
    setDeleteSuccess(false);
  };

  const handleConfirmDelete = async () => {
    if (!projectToDelete) {
      return;
    }

    setDeletingInProgress(true);
    setDeletingProject(projectToDelete.id);

    setTimeout(async () => {
      try {
        if (!selectedFolder || selectedFolder === 'all') {
          await deleteProject(projectToDelete.slug);
        } else {
          await deleteProjectFolder(projectToDelete.id, selectedFolder);
        }

        if (db) {
          await deleteById(db, projectToDelete.slug.split('-')[0].toString());
        }

        await enhancedRefreshProjects();

        setDeleteSuccess(true);
        setTimeout(() => {
          setShowDeleteConfirm(false);
          setDeletingProject(null);
          setProjectToDelete(null);
          setDeleteSuccess(false);
        }, 1000);
      } catch (error) {
        console.error('Error deleting project from folder:', error);
        setDeleteSuccess(false);
      } finally {
        setDeletingInProgress(false);
      }
    }, 300);
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setProjectToDelete(null);
    setDeleteSuccess(false);
  };

  const executeWithMinLoadingTime = async (
    operation: () => Promise<any>,
    setLoadingState: (value: string | null) => void,
    projectSlug: string,
    errorMessage: string,
    minLoadingTime = 800,
  ) => {
    const startTime = Date.now();

    setLoadingState(projectSlug);

    try {
      await operation();
    } catch (error) {
      console.error(errorMessage, error);
    } finally {
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      if (remainingTime > 0) {
        await new Promise((resolve) => setTimeout(resolve, remainingTime));
      }

      setLoadingState(null);
    }
  };

  function getPagination(current: number, total: number) {
    const delta = 2;
    const range: (number | string)[] = [];
    const left = Math.max(2, current - delta);
    const right = Math.min(total - 1, current + delta);

    range.push(1);

    if (left > 2) {
      range.push('...');
    }

    for (let i = left; i <= right; i++) {
      range.push(i);
    }

    if (right < total - 1) {
      range.push('...');
    }

    if (total > 1) {
      range.push(total);
    }

    return range;
  }

  const currentFolder = folders.find((folder) => folder._id === selectedFolder)?.name || 'All';
  const handleTransferProject = async (transferTo: string) => {
    setLoadingTransferProjects(true);
    try {
      const res = await backendApiFetch(
        `/user-projects/${showTransferProjectModal}/transfer/${transferTo}`,
        { method: 'POST', headers: { 'Content-Type': 'application/json' } }
      );
      const data = await res.json();
      if (!res.ok) throw new Error(data.message || 'transfer failed');

      toast.success(t("transferSuccess", { user: transferTo }));
      setTransferError(null);
    } catch (err: any) {
      let msg = t("transferError", "Error transferring project");
      if (err.message.includes('permission')) {
        msg = t("dontHavePermisionToTransfer", "You do not have permission to transfer this project");
      } else if (err.message.includes('not found')) {
        msg = t("transferProjectUserNotFound", {
          user: transferTo,
          defaultValue: `User ${transferTo} was not found!`
        });
      } else if (err.message.includes('cannot transfer a project to your own account')) {
        msg = t(
          "transferErrorOwnAccount",
          "You cannot transfer a project to your own account."
        );}
      setTransferError(msg);
    } finally {
      setLoadingTransferProjects(false);
    }
  };

  const handleCloseTransfer = () => {
    setShowTransferProjectModal(false);
    setTransferError(null);
  };

  return (
    <div className="flex-1 relative">
      <div className="flex items-center justify-between mb-4 lg:mb-6">
        <div>
          <h2 className="text-2xl lg:text-2xl font-light">
            {loading || filterLoading || searchLoading
              ? t('pleaseWait', 'Please wait...')
              : selectedType === 'all'
                ? t('projectsInAll', 'Projects in All')
                : t('projectsInCurrentFolder', 'Projects in', { folderName: currentFolder })}
          </h2>
        </div>
        <div className="relative search-input">
          <input
            type="text"
            placeholder={t('searchProjects', 'Search Project...')}
            value={searchTerm}
            onChange={(e) => {
              const newValue = e.target.value;
              setSearchTerm(newValue);
              handleSearch(newValue, 1);
            }}
            className="px-4 md:min-w-[296px] py-2 bg-transparent border border-gray-500 backdrop-blur-sm rounded-xl text-white placeholder-gray-400 search-folder"
          />
          <FaSearch />
        </div>
      </div>
      {loading || filterLoading || searchLoading ? (
        <div className="flex items-center justify-center h-40">
          <FaSpinner className="animate-spin text-3xl text-green-400" />
        </div>
      ) : displayedProjects.length > 0 ? (
        <>
          <div ref={projectsContainerRef} className="space-y-4 lg:space-y-6">
            {paginatedProjects.map((project, index) => (
              <motion.div
                key={project._id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: deletingProject === project._id ? 0 : 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="bg-[#1e2839] backdrop-blur-sm rounded-xl overflow-hidden relative"
              >
                {!hiddenCodeIcons.includes(index) && (
                  <div className="absolute -bottom-8 -right-8 text-[150px] lg:text-[200px] opacity-5">
                    <FaCode />
                  </div>
                )}

                <div className="p-3 lg:p-6 border-b border-gray-700/50 relative z-10">
                  <div className="flex flex-col justify-between gap-3 lg:gap-4">
                    <div className="flex items-center justify-between flex-icons-dash">
                      <div className="flex flex-col md:flex-row md:items-center gap-3">
                        <div className="bg-[#1f3843] px-3 lg:px-4 py-1.5 lg:py-2 rounded-full flex items-center gap-2 w-fit">
                          <FolderSymlink size={16} strokeWidth={1.5} style={{ color: '#10b981' }} />
                          <span className="text-sm lg:text-base font-light" style={{ color: '#10b981' }}>
                            {folders.find((folder) => folder.projectIds.includes(project._id))?.name || t('all', 'All')}
                          </span>
                        </div>
                        {editingProjectId === project._id.toString() ? (
                          <div className="flex items-center gap-2">
                            <input
                              type="text"
                              value={editedProjectName}
                              onChange={(e) => setEditedProjectName(e.target.value)}
                              className="font-manrope w-96 max-sm:w-64 bg-black/20 rounded-lg px-3 py-1 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 font-light text-lg lg:text-xl"
                              autoFocus
                            />
                            <button
                              onClick={() => handleSaveProjectName(project._id.toString())}
                              className={`p-1.5 lg:p-2 bg-green-500/40 hover:bg-green-500/70 rounded-lg transition-colors group ${
                                savingProjectName || editedProjectName === originalProjectName
                                  ? 'opacity-50 cursor-not-allowed'
                                  : ''
                              }`}
                              disabled={savingProjectName || editedProjectName === originalProjectName}
                            >
                              {savingProjectName ? (
                                <FaSpinner className="animate-spin text-gray-400" />
                              ) : (
                                <FaCheck className="text-gray-400 group-hover:text-white" />
                              )}
                            </button>
                            <button
                              onClick={handleCancelEditProjectName}
                              className="p-1.5 lg:p-2 bg-red-500/40 hover:bg-red-500/70 rounded-lg transition-colors group"
                            >
                              <FaTimes className="text-gray-400 group-hover:text-white" />
                            </button>
                          </div>
                        ) : (
                          <h3 className="text-lg lg:text-xl font-light ellipsis-text-name">
                            {project.projectName || project.projectSlug}
                          </h3>
                        )}
                      </div>
                      <div className="flex items-center flex-wrap md:flex-nowrap gap-2">
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.6 }}
                          className="flex justify-center cursor-pointer"
                        >
                          <motion.a
                            rel="noopener noreferrer"
                            className={`w-full sm:w-auto sm:min-w-[170px] sm:min-h-[36px] px-3 md:px-6 py-1.5 bg-gradient-to-r from-green-500 to-green-600 rounded-lg text-white font-light flex items-center justify-center gap-2 group relative overflow-hidden ${
                              loadingProjectId === project.projectSlug.split('-')[0]
                                ? 'opacity-50 cursor-not-allowed'
                                : ''
                            }`}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => handleOpenProject(project.projectSlug.split('-')[0].toString())}
                            disabled={loadingProjectId === project.projectSlug.split('-')[0]}
                          >
                            {loadingProjectId === project.projectSlug.split('-')[0] ? (
                              <FaSpinner className="animate-spin text-white" />
                            ) : (
                              <>
                                <motion.div
                                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                                  animate={{ x: ['-100%', '200%'] }}
                                  transition={{ repeat: Infinity, duration: 1.5 }}
                                />
                                <IconButtonRightIcon
                                  icon={<ExternalLink {...iconProps} />}
                                  data-tooltip-id="project-tooltip"
                                  variant="primary"
                                  data-tooltip-content={t('openProject', 'Open Project')}
                                  text={t('openProject', 'Open Project')}
                                />
                                {/*<FaExternalLinkAlt className="text-sm group-hover:translate-x-1 transition-transform"/>*/}
                              </>
                            )}
                          </motion.a>
                        </motion.div>

                        <div className="action-group">
                          <IconButton
                            icon={
                              downloadingProjectId === project.projectSlug ? (
                                <FaSpinner className="animate-spin text-gray-400 text-sm lg:text-base" />
                              ) : (
                                <Download {...iconProps} />
                              )
                            }
                            variant="download"
                            data-tooltip-id="project-tooltip"
                            data-tooltip-content={t('downloadProject', 'downloadProject')}
                            onClick={() => handleDownloadProject(project.projectSlug)}
                            disabled={downloadingProjectId === project.projectSlug}
                          />
                          <IconButton
                            icon={
                              exportChatId === project.projectSlug ? (
                                <FaSpinner className="animate-spin text-gray-400 text-sm lg:text-base" />
                              ) : (
                                <File {...iconProps} />
                              )
                            }
                            onClick={() => handleExportChat(project.projectSlug)}
                            disabled={exportChatId === project.projectSlug}
                            // variant="download"
                            data-tooltip-id="project-tooltip"
                            data-tooltip-content={t('downloadChat', 'Download Chat')}
                          />
                        </div>
                        <div className="action-group">
                          <IconButton
                            icon={<FaPaperPlane {...iconProps} />}
                            variant="secondary"
                            data-tooltip-id="project-tooltip"
                            //data-tooltip-content={t('editProjectName')}
                            data-tooltip-content={t('transferProject',"Transfer Project")}
                            onClick={() =>
                              setShowTransferProjectModal(project.projectSlug)
                            }
                          />
                          <IconButton
                            icon={<Edit {...iconProps} />}
                            variant="secondary"
                            data-tooltip-id="project-tooltip"
                            data-tooltip-content={t('editProjectName', 'Edit project name')}
                            onClick={() =>
                              handleEditProjectName(project._id.toString(), project.projectName || project.projectSlug)
                            }
                          />
                          <IconButton
                            icon={<FolderSymlink {...iconProps} />}
                            data-tooltip-id="project-tooltip"
                            data-tooltip-content={t('changeFolder', 'Change Folder')}
                            onClick={() => handleOpenChangeFolderModal(project._id)}
                            variant="secondary"
                          />

                          <IconButton
                            icon={
                              duplicatingProjectId === project.projectSlug ? (
                                <FaSpinner className="animate-spin text-gray-400 text-sm lg:text-base" />
                              ) : (
                                <Copy {...iconProps} />
                              )
                            }
                            variant="secondary"
                            data-tooltip-id="project-tooltip"
                            data-tooltip-content={t('duplicateProject', 'Duplicate Project')}
                            onClick={() => handleDuplicateProject(project.projectSlug)}
                            disabled={duplicatingProjectId === project.projectSlug}
                          />
                        </div>

                        <div className="action-group bg-[#EF4444]/5 border-[#EF4444]/20">
                          <IconButton
                            icon={<Trash2 {...iconProps} />}
                            variant="danger"
                            data-tooltip-id="project-tooltip"
                            data-tooltip-content={t('deleteProject', 'Delete Project')}
                            data-tip={
                              selectedFolder === 'all' || !selectedFolder ? t('deleteProject', 'Delete Project') : t('removeFromFolder', 'Remove from Folder')
                            }
                            onClick={() => handleOpenDeleteConfirm(project._id.toString(), project.projectSlug)}
                          />
                        </div>
                        <ReactTooltip id="project-tooltip" place="bottom" effect="solid" delayShow={0} />
                      </div>
                    </div>
                    <div className="flex flex-wrap justify-between items-center gap-3 text-xs lg:text-sm text-gray-400">
                      <div className="flex items-center gap-2 bg-[#222d40] px-2 lg:px-3 py-1 rounded-lg">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth="1.5"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                        <span className="font-light color-[#9ca3af]">{formatDate(project.updatedAt)}</span>
                      </div>
                      <div className="flex items-center space-x-2 max-[415px]:flex-wrap gap-2">
                        {project.isDeployed && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6 }}
                            className="flex justify-center cursor-pointer max-[415px]:w-[100%]"
                          >
                            <motion.a
                              rel="noopener noreferrer"
                              href={
                                project.cloudAppUrl
                                  ? project.cloudAppUrl.startsWith('http')
                                    ? project.cloudAppUrl
                                    : `https://${project.cloudAppUrl}`
                                  : undefined
                              }
                              target="_blank"
                              className="project-action text-sm max-[415px]:flex-1 bg-gradient-to-r from-purple-600 to-indigo-700 text-white rounded-lg py-1.5 px-3 font-light shadow-md hover:shadow-lg hover:from-indigo-700 hover:to-purple-600 hover:scale-105 flex items-center justify-center gap-2 group relative overflow-hidden"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <motion.div
                                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                                animate={{ x: ['-100%', '200%'] }}
                                transition={{ repeat: Infinity, duration: 1.5 }}
                              />
                              <span>Live App</span>
                              <ExternalLink size={18} strokeWidth={1.5} />
                            </motion.a>
                          </motion.div>
                        )}
                        {!isProjectPublished(project.projectSlug) && (
                          <motion.button
                            className="project-action bg-gradient-to-r from-purple-600 to-indigo-700 text-white rounded-lg py-1.5 px-3 font-normal shadow-md hover:shadow-lg hover:from-indigo-700 hover:to-purple-600 hover:scale-105 flex items-center justify-center gap-2 group relative overflow-hidden"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => handlePublishToContest(project.projectSlug)}
                          >
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                              animate={{ x: ['-100%', '200%'] }}
                              transition={{ repeat: Infinity, duration: 1.5 }}
                            />
                            <Trophy size={18} strokeWidth={1.5} />
                            <span>{t('publishToContest', 'Publish to contest')}</span>
                          </motion.button>
                        )}
                        {isProjectPublished(project.projectSlug) && (
                          <button className="project-action bg-gradient-to-r from-indigo-500 to-indigo-600 text-white py-1.5 px-3 font-normal shadow-md hover:shadow-lg hover:from-indigo-600 hover:to-indigo-500 flex items-center space-x-1.5">
                            <Trophy size={18} strokeWidth={1.5} />
                            <span>{t('publishedToContest', 'Published to contest')}</span>
                            <div className="flex items-center bg-white/20 px-1.5 py-0.5 rounded-full gap-1">
                              <Heart size={12} strokeWidth={1.5} fill="white" />
                              <span className="text-xs">{getProjectLikes(project.projectSlug)}</span>
                            </div>
                          </button>
                        )}

                        {/* Refresh Button */}
                        {isProjectPublished(project.projectSlug) && (
                          <ContestIconButton
                            icon={<RefreshCw size={18} strokeWidth={1.5} />}
                            onClick={() => handleRefreshContestSubmission(project.projectSlug)}
                            variant="info"
                            tooltip={t('refreshSubmission', 'Refresh submission')}
                          />
                        )}
                        <ContestIconButton
                          customStyleTooltip={{ left: '-35px' }}
                          icon={<Info size={18} strokeWidth={1.5} />}
                          onClick={() => {
                            if (getContestProjectId(project._id)) {
                              window.open(`https://biela.dev/contest/${getContestProjectId(project._id)}`, '_blank');
                            } else {
                              window.open('https://biela.dev/contest', '_blank');
                            }
                          }}
                          variant="info"
                          tooltip={t('contestInformation', 'Contest Information')}
                          text=""
                        />
                      </div>
                    </div>
                  </div>
                </div>
                {/* Project Metrics */}
                {project.estimations && project.totalTokensCost !== 0 && (
                  <div className="p-3 lg:p-6">
                    <ProjectMetrics
                      index={index}
                      hideIcon={(e) => {
                        if (e === undefined) {
                          // scoate indexul din listÄƒ
                          setHiddenCodeIcons((prev) => prev.filter((i) => i !== index));
                        } else {
                          // adaugÄƒ indexul Ã®n listÄƒ (dacÄƒ nu e deja)
                          setHiddenCodeIcons((prev) => [...new Set([...prev, index])]);
                        }
                      }}
                      metrics={{
                        timeMetrics: {
                          traditional: project.estimations.estimatedTimeTraditional,
                          actual: project.timeSpent,
                        },
                        estimatedCost: {
                          traditional: project.estimations.estimatedCostTraditional,
                          tokens: project.totalTokensCost,
                        },
                        estimations: {
                          confidenceScore: project.estimations.confidenceScore,
                          estimatedCostTraditional: project.estimations.estimatedCostTraditional,
                          estimatedTimeTraditional: project.estimations.estimatedTimeTraditional,
                          estimatedNumberOfDevelopers: project.estimations.estimatedNumberOfDevelopers,
                          recommendedDeveloperLevel: project.estimations.recommendedDeveloperLevel,
                          timeToMarket: project.estimations.timeToMarket,
                          maintenanceCostPercentage: project.estimations.maintenanceCostPercentage,
                          projectType: project.estimations.projectType,
                          projectComplexity: project.estimations.projectComplexity,
                          uniqueComponentCount: project.estimations.uniqueComponentCount,
                          featureCount: project.estimations.featureCount,
                          rangeOfUncertainty: project.estimations.rangeOfUncertainty,
                          keyTechnologies: project.estimations.keyTechnologies,
                          breakdown: project.estimations.breakdown,
                        },
                      }}
                    />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
          {totalPagesNumber > 1 && (
            <div style={{ zIndex: '20', position: 'relative' }} className="flex justify-center items-center mt-6">
              <button
                onClick={() => handlePageChange(currentPage === 1 ? totalPagesNumber : currentPage - 1)}
                className="p-2 bg-[#123456] hover:bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-lg"
              >
                <FaChevronLeft />
              </button>

              <div className="flex gap-2 mx-4">
                {getPagination(currentPage, totalPagesNumber).map((n, idx) =>
                  n === '...' ? (
                    <span key={`ellipsis-${idx}`} className="px-2 text-gray-400 select-none">
                      ...
                    </span>
                  ) : (
                    <button
                      key={n}
                      onClick={() => handlePageChange(Number(n))}
                      className={`p-2 px-3.5 rounded-lg ${
                        currentPage === Number(n)
                          ? 'bg-gradient-to-r from-green-500 to-green-600 text-white'
                          : 'bg-[#123456] hover:bg-gradient-to-r from-green-500/20 to-green-600/20'
                      }`}
                    >
                      {n}
                    </button>
                  ),
                )}
              </div>

              <button
                onClick={() => handlePageChange(currentPage === totalPagesNumber ? 1 : currentPage + 1)}
                className="p-2 bg-[#123456] hover:bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-lg"
              >
                <FaChevronRight />
              </button>
            </div>
          )}
        </>
      ) : searchedProjects !== null ? (
        // **NEW**: you did run a search but got zero hits
        <div className="bg-navy-800 rounded-xl p-10 overflow-hidden relative">
          {/* Decorative elements for futuristic feel */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-purple-500/0"></div>
          <div className="absolute bottom-0 right-0 w-1/3 h-1 bg-gradient-to-r from-purple-500/0 via-purple-500/30 to-blue-500/0"></div>

          <div className="flex flex-col items-center justify-center py-8 relative">
            {/* Subtle grid background */}
            <div className="absolute inset-0 bg-[radial-gradient(#2a3a5a_1px,transparent_1px)] bg-[size:20px_20px] opacity-20"></div>

            <h2 className=" text-2xl font-light font-manrope mb-4 text-white text-center">
              {t('NoProjectFound', 'No project found with that name.')}
            </h2>

            <p className="text-center text-gray-400 max-w-xl mb-8 text-md font-light leading-normal">
              {t('NoProjectFoundDescription', 'Please check for any typos and try again.')}
            </p>
          </div>
        </div>
      ) : (
        <div className="bg-navy-800 rounded-xl p-10 overflow-hidden relative">
          {/* Decorative elements for futuristic feel */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-purple-500/0"></div>
          <div className="absolute bottom-0 right-0 w-1/3 h-1 bg-gradient-to-r from-purple-500/0 via-purple-500/30 to-blue-500/0"></div>

          <div className="flex flex-col items-center justify-center py-8 relative">
            {/* Subtle grid background */}
            <div className="absolute inset-0 bg-[radial-gradient(#2a3a5a_1px,transparent_1px)] bg-[size:20px_20px] opacity-20"></div>

            {/* Icon with glow effect */}
            <div className="relative mb-8">
              <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-xl transform scale-150"></div>
              <div className="bg-[rgba(134,244,156,0.07)] p-6 rounded-full relative z-10 border border-[rgba(134,244,156,0.07)]">
                <Sparkles className="w-10 h-10 text-white" />
              </div>
            </div>

            <h2 className="rexton-light text-lg font-light leading-rexton-small mb-4 text-white text-center">
              {t('createYourFirstProject', 'Create Your First Project')}
            </h2>

            <p className="text-center text-gray-400  mb-8 text-md font-light leading-normal md:whitespace-nowrap">
              {t(
                'startCreateNewProjectDescription',
                'Start building something amazing. Your projects will be displayed here once you create them.',
              )}
            </p>

            {/* Updated button with shimmer effect */}
            <div className="flex justify-center">
              <a
                href="/"
                className="w-full sm:w-auto px-6 py-1.5 bg-gradient-to-r from-green-500 to-green-600 rounded-lg text-white font-light flex items-center justify-center gap-2 group relative overflow-hidden hover:scale-105 transition-transform active:scale-95"
              >
                {/* Shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full animate-shimmer" />

                {/* Button content */}
                <Plus className="w-5 h-5" />
                <span className="text-md font-light leading-small">{t('createProjectBtn', 'New Project')}</span>
              </a>
            </div>
          </div>
        </div>
      )}

      {/* Modalul de schimbare folder */}
      <AnimatePresence>
        {showChangeFolderModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            key="change-folder-modal"
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowChangeFolderModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-[#1e2839] rounded-2xl max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 flex justify-between items-center">
                <h3 className="text-xl font-light">{t('selectFolder', 'Choose a Folder')}</h3>
                <button
                  onClick={() => setShowChangeFolderModal(false)}
                  className="p-2 hover:bg-[#293445] rounded-full transition-colors bg-transparent"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line
                      x1="6"
                      y1="6"
                      x2="18"
                      y2="18"
                      stroke="currentColor"
                      stroke-width="1.5"
                      stroke-linecap="round"
                    />
                    <line
                      x1="18"
                      y1="6"
                      x2="6"
                      y2="18"
                      stroke="currentColor"
                      stroke-width="1.5"
                      stroke-linecap="round"
                    />
                  </svg>
                </button>
              </div>
              <div className="p-6 pt-0 space-y-4">
                <div className={'relative'}>
                  <label className="block text-sm text-gray-400 mb-2" style={{ color: 'rgb(16, 185, 129)' }}>
                    {t('folder', 'Folder')}
                  </label>
                  <select
                    value={targetFolderId || ''}
                    onChange={(e) => setTargetFolderId(e.target.value)}
                    className="appearance-none bg-[#293445] w-full px-4 py-2 pr-10 rounded-lg text-white focus:outline-none arrow-absolute"
                  >
                    <option value="" disabled>
                      {t('selectAFolder', 'Select a folder')}
                    </option>
                    {folders.map((folder) => (
                      <option key={folder._id} value={folder._id}>
                        {folder.name}
                      </option>
                    ))}
                  </select>
                  <span className="absolute right-2 top-12 transform -translate-y-1/2 pointer-events-none">
                    <svg
                      className="w-4 h-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                </div>
              </div>
              <div className="p-6 flex justify-end gap-3">
                <button
                  onClick={() => setShowChangeFolderModal(false)}
                  className="bg-[#0B1931] px-4 py-2 rounded-lg transition-colors text-white cancel-btn-modal flex items-center rounded-lg transition-all duration-300 relative bg-gradient-to-r from-biela-green to-biela-hover text-white font-normal shadow-md hover:shadow-lg hover:from-biela-hover hover:to-biela-green hover:scale-105"
                >
                  {t('cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleConfirmChangeFolder}
                  className={`px-4 py-2 bg-[#0bb57c] gap-2 flex items-center rounded-lg transition-all duration-300 relative bg-gradient-to-r from-biela-green to-biela-hover text-white font-normal shadow-md hover:shadow-lg hover:from-biela-hover hover:to-biela-green hover:scale-105 ${
                    !targetFolderId || targetFolderId === currentFolderId || changingFolderProcess
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }`}
                  disabled={!targetFolderId || targetFolderId === currentFolderId || changingFolderProcess}
                >
                  {changingFolderProcess ? (
                    <FaSpinner className="animate-spin" />
                  ) : (
                    <>
                      <FaCheck />
                      <span>{t('Confirm', 'Confirm')}</span>
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Delete Confirmation Dialog */}
      <AnimatePresence>
        {showDeleteConfirm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            key="delete-confirm-modal"
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowDeleteConfirm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 rounded-2xl max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 flex justify-between items-center">
                <h3 className="text-xl font-light text-red-400">
                  {selectedFolder === 'all' || !selectedFolder
                    ? t('confirmDeleteProject', 'Confirm Delete Project')
                    : t('confirmRemoveFromFolder', 'Confirm Remove from Folder')}
                </h3>
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="p-2 hover:bg-gray-800 rounded-full transition-colors bg-transparent"
                >
                  <FaTimes className="text-gray-400" />
                </button>
              </div>
              <div className="p-6">
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-4">
                  <div className="flex items-start gap-3">
                    <FaExclamationTriangle className="text-red-400 mt-1" />
                    <p className="text-gray-300">
                      {selectedFolder === 'all' || !selectedFolder
                        ? t('deleteProjectWarning', {
                            projectName:
                              projects.find((p) => p._id.toString() === projectToDelete?.id)?.projectName ||
                              t('thisProject', 'this project'),
                          })
                        : t('removeFromFolderWarning', {
                            projectName:
                              projects.find((p) => p._id.toString() === projectToDelete?.id)?.projectName ||
                              t('thisProject', 'this project'),
                            folderName: currentFolder,
                          })}
                    </p>
                  </div>
                </div>
                {deleteSuccess ? (
                  <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <FaCheck className="text-green-400" />
                      <p className="text-gray-300">
                        {selectedFolder === 'all' || !selectedFolder
                          ? t('projectDeletedSuccessfully', 'Project deleted successfully')
                          : t('projectRemovedFromFolder', 'Project removed from folder')}
                      </p>
                    </div>
                  </div>
                ) : projectToDelete ? (
                  <div className="bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-lg bg-gray-800 flex items-center justify-center flex-shrink-0">
                        <FaFolder className="text-gray-400 text-xl" />
                      </div>
                      <div>
                        <div className="font-medium text-white">
                          {projects.find((p) => p._id.toString() === projectToDelete.id)?.projectName ||
                            t('unnamedProject', 'Unnamed Project')}
                        </div>
                        <div className="text-sm text-gray-400">
                          {selectedFolder === 'all' || !selectedFolder
                            ? t('permanentDeletion', 'Permanent deletion')
                            : t('removeFromFolder', 'Remove from folder', { folderName: currentFolder })}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : null}
              </div>
              <div className="p-6 flex justify-end gap-3">
                <button
                  onClick={handleCancelDelete}
                  className={`px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors text-white ${deletingInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={deletingInProgress}
                >
                  {t('cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleConfirmDelete}
                  className={`px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-white flex items-center gap-2 ${deletingInProgress || deleteSuccess ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={deletingInProgress || deleteSuccess}
                >
                  {deletingInProgress ? (
                    <FaSpinner className="animate-spin" />
                  ) : (
                    <>
                      <FaTrash />
                      <span>{t('confirm', 'Confirm')}</span>
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
        <AnimatePresence>
          {showTransferProjectModal && (
            <TransferModal
              key="transfer-modal"
              isOpen={showTransferProjectModal}
              isLoading={loadingTransferProjects}
              onTransfer={handleTransferProject}
              onClose={handleCloseTransfer}
              errorMessage={transferError}
            />
          )}

        </AnimatePresence>



        <SubmissionModal
          key="submission-modal"
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onViewContest={handleViewContest}
          onRetry={() => {
            if (modalType === 'publish') {
              handlePublishToContest(currentProjectSlug!);
            } else if (modalType === 'refresh') {
              handleRefreshContestSubmission(currentProjectSlug!);
            }
          }}
          type={modalType}
          state={modalState}
          projectName={currentProjectSlug || 'Project'}
        />
      </AnimatePresence>
    </div>
  );
}

export default ProjectsListClient;
