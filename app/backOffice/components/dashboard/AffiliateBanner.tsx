import { motion } from 'framer-motion';
import { FaArrowRight, FaChartLine, FaGem, FaHandshake, FaMoneyBillWave, FaRocket, FaUsers } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

type Particle = { top: number; left: number };

function AffiliateBanner() {
  const { t } = useTranslation('translation');
  const [particles, setParticles] = useState<Particle[] | null>(null);
  useEffect(() => {
    const arr: Particle[] = Array.from({ length: 8 }).map(() => ({
      top: Math.random() * 100,
      left: Math.random() * 100,
    }));
    setParticles(arr);
  }, []);

  // Dacă nu avem încă pozițiile, afișăm un placeholder (sau nimic)
  if (!particles) return null;
  return (
    <motion.div
      className="relative h-auto bg-gradient-to-r from-gray-800/50 to-gray-900/50 rounded-xl overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/[0.04] to-transparent" />
      <div className="absolute -bottom-16 -right-16 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl" />
      <div className="absolute -top-16 -left-16 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl" />

       {/*Semi-transparent background icons*/}
      <div className="absolute -bottom-8 right-0 text-[120px] opacity-5 hidden lg:block">
        <FaHandshake />
      </div>
      <div className="absolute -bottom-8 right-60 text-[120px] opacity-5 hidden lg:block">
        <FaMoneyBillWave />
      </div>
      <div className="absolute -bottom-8 right-120 text-[120px] opacity-5 hidden lg:block">
        <FaUsers />
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex flex-col xl:flex-row xl:items-center justify-between p-6 lg:px-8 gap-4 full-banner">
        <div className="flex flex-col lg:flex-row lg:items-center gap-6 lg:gap-12 mb-6 lg:mb-0 join-affiliate">
          {/* Icon and Title */}
          <div className="flex items-center gap-4">
            <div className="p-3 bg-black/20 rounded-xl">
              <FaHandshake className="text-2xl text-green-400" />
            </div>
            <div style={{maxWidth: '300px'}}>
              <h3 className="text-xl font-light text-left">{t('joinAffiliateProgram','Join Our Affiliate Program')}</h3>
              <p className="text-gray-400 text-sm font-light text-left">{t('earnCommission','Earn 50% commission on your first sale')}</p>
            </div>
          </div>

          {/* Benefits - Show in grid on mobile, row on desktop */}
          <div className="grid grid-cols-1 sm:grid-cols-3 lg:flex items-stretch gap-4 lg:gap-6 liftime-cols">
            <div className="flex items-center gap-2 bg-black/20 px-8 py-4 rounded-lg justify-center">
              <FaMoneyBillWave className="text-yellow-400 text-[22px] flex-shrink-0" />
              <span className="text-[1.1rem] font-light text-center">{t('highCommissions','High Commissions')}</span>
            </div>
            <div className="flex items-center gap-2 bg-black/20 px-8 py-4 rounded-lg justify-center">
              <FaGem className="text-purple-400 text-[22px] flex-shrink-0" />
              <span className="text-[1.1rem] font-light text-center">{t('lifetimeEarnings','Lifetime Earnings')}</span>
            </div>
            <div className="flex items-center gap-2 bg-black/20 px-8 py-4 rounded-lg justify-center">
              <FaChartLine className="text-blue-400 text-[22px] flex-shrink-0" />
              <span className="text-[1.1rem] font-light text-center">{t('exclusiveGrowth','Exclusive Growth Perks')}</span>
            </div>
          </div>
        </div>

        <a href="https://affiliate.biela.dev" className="w-full lg:w-auto relative">
          <motion.button
            className="w-full relative bg-gradient-to-r from-green-600 to-emerald-500 rounded-lg px-6 py-3 text-white font-light flex items-center justify-center gap-2 overflow-hidden group"
            whileHover={{
              scale: 1.02,
              boxShadow: "0 0 15px rgba(16, 185, 129, 0.5)",
            }}
            whileTap={{ scale: 0.98 }}
          >
            {/* Animated background particles */}
            {particles.map((pos, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-white rounded-full opacity-0"
                style={{
                  top: `${pos.top}%`,
                  left: `${pos.left}%`,
                }}
                animate={{
                  opacity: [0, 0.7, 0],
                  scale: [0, 1.5, 0],
                  x: [0, Math.random() * 40 - 20],
                  y: [0, Math.random() * 40 - 20],
                }}
                transition={{
                  duration: 2 + Math.random() * 2,
                  repeat: Infinity,
                  repeatType: 'loop',
                  delay: Math.random() * 2,
                }}
              />
            ))}

            {/* Animated gradient overlay */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-green-400/0 via-green-300/30 to-green-400/0"
              animate={{
                x: ["-100%", "100%"],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut",
                repeatDelay: 1,
              }}
            />

            {/* Animated border */}
            <motion.div
              className="absolute inset-0 rounded-lg border-2 border-green-300/0"
              animate={{
                borderColor: [
                  "rgba(134, 239, 172, 0)",
                  "rgba(134, 239, 172, 0.5)",
                  "rgba(134, 239, 172, 0)",
                ],
                boxShadow: [
                  "0 0 0px rgba(16, 185, 129, 0)",
                  "0 0 10px rgba(16, 185, 129, 0.5)",
                  "0 0 0px rgba(16, 185, 129, 0)",
                ],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut",
              }}
            />

            {/* Icon without rotation */}
            <div className="relative z-10">
              <FaRocket className="text-white" />
            </div>

            {/* Text with glow effect */}
            <motion.span
              className="relative z-10 text-shadow-sm"
              animate={{
                textShadow: [
                  "0 0 0px rgba(255, 255, 255, 0)",
                  "0 0 5px rgba(255, 255, 255, 0.5)",
                  "0 0 0px rgba(255, 255, 255, 0)",
                ],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut",
              }}
            >
              <span>{t('becomeAffiliate','Affiliate Dashboard')}</span>
            </motion.span>
            <FaArrowRight className="text-sm group-hover:translate-x-1 transition-transform" />

            {/* Notification dot */}
            <motion.div
              className="absolute top-1 right-1 w-2 h-2 bg-yellow-400 rounded-full"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.7, 1, 0.7],
                boxShadow: [
                  "0 0 0px rgba(250, 204, 21, 0)",
                  "0 0 5px rgba(250, 204, 21, 0.7)",
                  "0 0 0px rgba(250, 204, 21, 0)",
                ],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          </motion.button>

        </a>
      </div>

      <div
        className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500/50 via-purple-500/50 to-blue-500/50" />
    </motion.div>
  );
}

export default AffiliateBanner;
