import React from 'react';
import { motion } from 'framer-motion';
import { Check, CreditCard, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import CardVerificationImg from 'public/card_verification_img.svg';

function CardVerificationModal({ onClose }) {
  const { t } = useTranslation('translation');

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center p-4 z-999"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="w-[90%] lg:w-full max-w-6xl pt-10 md:pt-0 flex flex-col lg:flex-row rounded-xl border border-[#4ADE80]/20 bg-[#1E2833] relative overflow-hidden"
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 w-8 h-8 rounded-full flex items-center justify-center border border-white/10 bg-transparent hover:bg-[#34D399]/20 hover:border-[#34D399] transition-colors duration-200 z-20"
        >
          <X className="w-4 h-4 text-white/70 hover:text-[#34D399] transition-colors duration-200" />
        </button>

        <div className="absolute top-0 right-0 md:relative items-end flex flex-col justify-center w-full h-[75%] md:w-auto md:h-auto pt-16 pl-18 overflow-hidden basis-[100%] lg:basis-[35%] xl:basis-[37.5%] lg:py-21 lg:pl-24 lg:pr-0">
          <div className="absolute rounded-full w-[100%] h-[100%] bg-[#16A249] blur-[175px] -top-[400px] -right-[200px]"></div>
          <img
            src={CardVerificationImg}
            alt="Card Verification"
            className="hidden md:block relative z-1 max-w-[45%] lg:max-w-full"
          />
        </div>

        <div className="relative z-10 p-6 md:p-16 overflow-auto max-h-[calc(100vh-70px)] basis-[100%] lg:basis-[65%] xl:basis-[62.5%]">
          <div>
            {/* Header */}
            <div className="flex items-center gap-4 mb-8">
              <div>
                <h1 className="rexton-light text-[16px] md:text-[24px] leading-[32px] font-light text-white mb-6">
                  {t('cardVerificationRequired', 'Folders')}
                </h1>
                <p className="font-manrope text-[14px] md:text-[18px]">
                  {t('pleaseVerifyCard', 'Please verify your card to continue')}
                </p>
              </div>
            </div>

            <div className="mb-8">
              <p className="text-white font-manrope text-[14px] md:text-[16px]">
                {t(
                  'unlockFeaturesMessage',
                  'To unlock full access to all platform features, we require a quick card verification. This process is completely secure and ensures a smooth experience on Biela.dev. No charges will be made to your card during verification.',
                )}
              </p>
            </div>

            <div className="mb-4">
              <p className="text-white rexton-manrope text-[16px] md:text-[20px]">
                {t('freeVerificationNotice', 'Verification Benefits')}
              </p>
            </div>

            <ul className="space-y-4 mb-8">
              <li className="flex items-start">
                <span className="text-white font-inter text-[14px] md:text-[16px]">
                  <Check className="w-3 h-3 md:w-5 md:h-5 text-[#34D399] inline-block mr-2" />{' '}
                  {t('accessToAllFeatures', 'Full access to Biela.dev features.')}
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-white font-inter text-[14px] md:text-[16px]">
                  <Check className="w-3 h-3 md:w-5 md:h-5 text-[#34D399] inline-block mr-2" />{' '}
                  {t('enhancedFunctionality', 'Enhanced platform performance.')}
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-white font-inter text-[14px] md:text-[16px]">
                  <Check className="w-3 h-3 md:w-5 md:h-5 text-[#34D399] inline-block mr-2" />{' '}
                  {t('quickSecureVerification', 'Secure and encrypted verification process.')}
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-white font-inter text-[14px] md:text-[16px]">
                  <span className="text-[#34D399] font-inter font-medium uppercase text-smaller">
                    <Check className="w-3 h-3 md:w-5 md:h-5 text-[#34D399] inline-block mr-2" />{' '}
                    {t('noCharges', '*No charges or hidden fees')}
                  </span>
                  {t('verificationOnly', '— verification only.')}
                </span>
              </li>
            </ul>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex w-full md:w-[270px]"
            >
              <motion.a
                href="/settings?tab=billing"
                className="inline-flex w-full px-6 py-[14.5px] bg-gradient-to-r from-green-500 to-green-600 rounded-lg text-white items-center justify-center gap-3 group relative overflow-hidden"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                  animate={{ x: ['-100%', '200%'] }}
                  transition={{ repeat: Infinity, duration: 1.5 }}
                />
                <CreditCard className="w-5 h-5 relative z-10" />
                <span className="relative z-10 text-[16px]">{t('verifyNow', 'Verify Now')}</span>
              </motion.a>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}

export default CardVerificationModal;
