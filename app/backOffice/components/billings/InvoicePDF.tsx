import { Document, Font, Image, Page, Text, View } from '@react-pdf/renderer';
import { <PERSON>iela<PERSON>ogo } from './InvoiceAssets/BielaLogo';
import LeftDecoration from './InvoiceAssets/leftDecoration.png';
import BottomDecoration from './InvoiceAssets/bottomDecoration.png';
import { FromIcon } from './InvoiceAssets/FromIcon';
import { ToIcon } from './InvoiceAssets/ToIcon';
import { GlobeIcon } from './InvoiceAssets/GlobeIcon';
import { LocationPinIcon } from './InvoiceAssets/LocationPinIcon';
import { BankIcon } from './InvoiceAssets/BankIcon';
import { styles } from './InvoiceAssets/InvoicePDFStyles';

Font.register({
    family: 'Inter',
    fonts: [
        { src: 'https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hjp-Ek-_EeA.woff', fontWeight: 300 },
        { src: 'https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hjp-Ek-_EeA.woff', fontWeight: 400 },
        { src: 'https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fAZ9hjp-Ek-_EeA.woff', fontWeight: 500 },
        { src: 'https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hjp-Ek-_EeA.woff', fontWeight: 600 },
        { src: 'https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYAZ9hjp-Ek-_EeA.woff', fontWeight: 700 }
    ]
});

const InvoicePDF = ({ invoiceData }) => (
    <Document>
        <Page size="A4" style={styles.page}>
            {/* Background decorations */}
            <Image
                src={LeftDecoration}
                style={styles.backgroundDecoration1}
            />
            <Image
                src={BottomDecoration}
                style={styles.backgroundDecoration2}
            />

            {/* Header */}
            <View style={styles.header}>
                <View>
                    <BielaLogo />
                </View>
                <View style={styles.headerRight}>
                    <View style={styles.headerItem}>
                        <Text style={styles.headerLabel}>Invoice number</Text>
                        <Text style={styles.headerValue}>{invoiceData.invoiceNumber}</Text>
                    </View>
                    <View style={styles.headerItem}>
                        <Text style={styles.headerLabel}>Issued</Text>
                        <Text style={styles.headerValue}>{invoiceData.issuedDate}</Text>
                    </View>
                    <View style={styles.headerItem}>
                        <Text style={styles.headerLabel}>Due Date</Text>
                        <Text style={styles.headerValue}>{invoiceData.dueDate}</Text>
                    </View>
                </View>
            </View>

            {/* From and To sections */}
            <View style={styles.fromToContainer}>
                <View style={styles.fromTo}>
                    <View style={styles.sectionTitle}>
                        <View style={styles.sectionIconContainer}>
                            <FromIcon />
                        </View>
                        <Text style={styles.sectionTitleLabel}>From</Text>
                    </View>
                    <View style={{ gap: 4 }}>
                        <View style={{ flexDirection: 'row', gap: 6 }}>
                            <GlobeIcon />
                            <Text style={styles.addressLine}>{invoiceData.from.companyName}</Text>
                        </View>
                        <View style={{ flexDirection: 'row', gap: 6 }}>
                            <LocationPinIcon />
                            <Text style={styles.addressLine}>{invoiceData.from.address}</Text>
                        </View>
                    </View>
                    <View style={styles.bankDetails}>
                        <View style={{ flexDirection: 'row', gap: 6 }}>
                            <BankIcon />
                            <Text style={{ fontSize: 12, marginBottom: 5, color: '#D1D5DB', fontWeight: 500 }}>Bank Details:</Text>
                        </View>
                        <Text style={styles.bankDetailsLine}>Account No: {invoiceData.from.bankDetails.accountNo}</Text>
                        <Text style={styles.bankDetailsLine}>Code: {invoiceData.from.bankDetails.code}</Text>
                    </View>
                </View>

                <View style={styles.fromTo}>
                    <View style={styles.sectionTitle}>
                        <View style={styles.sectionIconContainer}>
                            <ToIcon />
                        </View>
                        <Text style={styles.sectionTitleLabel}>To</Text>
                    </View>
                    <View style={{ gap: 4 }}>
                        <View style={{ flexDirection: 'row', gap: 6 }}>
                            <GlobeIcon />
                            <Text style={styles.addressLine}>{invoiceData.to.companyName}</Text>
                        </View>
                        <View style={{ flexDirection: 'row', gap: 6 }}>
                            <LocationPinIcon />
                            <Text style={styles.addressLine}>{invoiceData.to.address}</Text>
                        </View>
                    </View>
                </View>
            </View>

            {/* Invoice Table */}
            <View style={styles.tableContainer}>
                {/* Table Header */}
                <View style={styles.tableHeader}>
                    <View style={styles.descriptionCol}>
                        <Text style={styles.tableHeaderText}>Description</Text>
                    </View>
                    <View style={styles.qtyCol}>
                        <Text style={styles.tableHeaderText}>QTY</Text>
                    </View>
                    <View style={styles.priceCol}>
                        <Text style={styles.tableHeaderText}>Price</Text>
                    </View>
                    <View style={styles.amountCol}>
                        <Text style={styles.tableHeaderText}>Amount</Text>
                    </View>
                </View>

                {/* Table Rows */}
                {invoiceData.items.map((item, idx) => {
                    const amount = item.qty * item.price;
                    return (
                        <View style={styles.tableRow} key={idx}>
                            <View style={styles.descriptionCol}>
                                <Text style={styles.tableCell}>{item.description}</Text>
                            </View>
                            <View style={styles.qtyCol}>
                                <Text style={styles.tableCell}>{item.qty}</Text>
                            </View>
                            <View style={styles.priceCol}>
                                <Text style={styles.tableCell}>${item.price}</Text>
                            </View>
                            <View style={styles.amountCol}>
                                <Text style={styles.tableCell}>${amount}</Text>
                            </View>
                        </View>
                    );
                })}
            </View>

            <View style={styles.summaryContainer}>
                <View style={styles.summary}>
                    <View style={styles.summaryRow}>
                        <Text style={styles.summaryLabel}>Sub Total</Text>
                        <Text style={styles.summaryValue}>${invoiceData.subTotal}</Text>
                    </View>
                    <View style={styles.summaryRow}>
                        <Text style={styles.summaryLabel}>Tax</Text>
                        <Text style={styles.summaryValue}>${invoiceData.tax}</Text>
                    </View>
                    <View style={styles.totalRow}>
                        <Text style={styles.totalLabel}>Total</Text>
                        <Text style={styles.totalValue}>${invoiceData.total}</Text>
                    </View>
                </View>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
                <View style={styles.contact}>
                    <View style={styles.contactTitle}>
                        <Text style={{ color: '#10B981', fontWeight: 600 }}>Contact</Text>
                    </View>
                    <View style={styles.contactLine}>
                        <Text style={styles.addressLine}>{invoiceData.contact.email}</Text>
                    </View>
                    <View style={styles.contactLine}>
                        <Text style={styles.addressLine}>{invoiceData.contact.phone}</Text>
                    </View>
                </View>
            </View>
        </Page>
    </Document>);

export {
    InvoicePDF
}
