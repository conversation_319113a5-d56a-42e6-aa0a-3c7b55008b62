import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from '@remix-run/react';
import { backendApiFetch } from '~/ai/lib/backend-api';
import ErrorSupabaseDialog from '~/ai/components/ErrorSupabaseDialog';
import { LoadingBielaSupabase } from './LoadingBielaSupabase';
import CustomErrorDialog from '~/ai/components/SupabaseErrorMessageDialog';
import { motion } from 'framer-motion';
import { FaCheck } from 'react-icons/fa';
import { BackgroundLinesComponent } from '~/components/styles/backgroundLinesComponent';


export const OAuthCallbackClient = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<{show: boolean; message: string; title?: string}>({
    show: false,
    message: '',
    title: 'Error'
  });
  const [isErrorSupabaseDialogOpen, setErrorSupabaseDialogOpen] = useState(false);
  const authInProgress = useRef(false);

  const handleAuthCode = async () => {
    if (authInProgress.current) {
      return { success: false, message: 'Auth request already in progress' };
    }

    authInProgress.current = true;
    setIsLoading(true);

    try {
      const params = new URLSearchParams(window.location.search);
      const code = params.get('code');
      const codeVerifier = localStorage.getItem('code_verifier');
      const chatId = localStorage.getItem('chatID');
      const projectSlug =localStorage.getItem('projectSlug');

      if (!code) {
        setError({
          show: true,
          message: 'No authentication code found. Please try again.',
          title: 'Authentication Error'
        });
        return { success: false, message: 'No authentication code found' };
      }

      if (!codeVerifier) {
        setError({
          show: true,
          message: 'Missing authentication data. Please try again from the beginning.',
          title: 'Authentication Error'
        });
        return { success: false, message: 'Missing code verifier' };
      }

      const handleAuthSuccess = (data: unknown) => {
        const authData = {
          type: 'SUPABASE_AUTH_COMPLETE',
          projectUrl: data.projectUrl,
          annonKey: data.annonKey
        };

        localStorage.setItem('supabaseAuthData', JSON.stringify(authData));

        localStorage.removeItem('code_verifier');

        if (window.opener) {
          window.opener.postMessage(authData, window.location.origin);
        }

          setTimeout(() => window.close(), 3400);
        };

      const response = await backendApiFetch(`/cloud/supabase/supabase-auth`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          code,
          codeVerifier,
          projectSlug,
        })
      });

      const data = await response.json();

      if (data.success) {
        setIsSuccess(true);
        handleAuthSuccess(data);
        return data;
      }

      if(data.message && data.message.includes('PROJECT_LIMIT_REACHED')) {
        setIsLoading(false);
        setErrorSupabaseDialogOpen(true);
        return data;
      }

      const errorMsg = data.message || 'An error occurred during authentication';
      setError({
        show: true,
        message: errorMsg,
        title: 'Connection Failed'
      });
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError({
        show: true,
        message: errorMessage,
        title: 'Connection Error'
      });
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetry = async () => {
    setError({ show: false, message: '', title: 'Error' });
    setErrorSupabaseDialogOpen(false);

    authInProgress.current = false;

    const response = await handleAuthCode();
    if (response && response.success) {
      const chatId = localStorage.getItem('chatID');
      setIsSuccess(true);
      setTimeout(() => {
        window.location.href = `chat/${chatId}`;
      }, 3400);
    }
  };

  const handleCloseError = () => {
    setError({ show: false, message: '', title: 'Error' });
    const chatId = localStorage.getItem('chatID');
    if (chatId) {
      window.location.href = `/chat/${chatId}`;
    } else {
      window.close();
    }
  };

  useEffect(() => {
    handleAuthCode();

    return () => {
      authInProgress.current = false;
    };
  }, []);

  const isLoadingComplete = () => isSuccess;

  return (
    <>
      {isLoading ? (
        <div className="flex items-center justify-center min-h-screen" style={{   backgroundImage: 'url(/hero-bg-shade-empty.png)',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center'}}>
          <LoadingBielaSupabase isLoadingComplete={isLoadingComplete} />
        </div>
      ) : isSuccess ? (
        <div className="flex items-center justify-center min-h-screen">
          <div className={'background-biela'}>
            <BackgroundLinesComponent />
          </div>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-8 relative"
          >
            <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaCheck className="text-green-400 text-2xl" />
            </div>
            <h2 className="text-3xl font-light text-white mb-4">Successfully connected!</h2>
            <p className="text-gray-400 font-light mb-4">You will be redirected back to biela.dev</p>
            <div className="w-full bg-gray-700 h-1 rounded-full overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: '100%' }}
                transition={{ duration: 3 }}
                className="h-full bg-green-500"
              />
            </div>
          </motion.div>
        </div>
      ) : (
        <>
          <ErrorSupabaseDialog
            isOpen={isErrorSupabaseDialogOpen}
            onEdit={handleRetry}
            onClose={() => setErrorSupabaseDialogOpen(false)}
            isLoading={isLoading}
          />
          <CustomErrorDialog
            isOpen={error.show}
            onRetry={handleRetry}
            onClose={handleCloseError}
            isLoading={isLoading}
            errorMessage={error.message}
            errorTitle={error.title}
          />
        </>
      )}
    </>
  );
};
