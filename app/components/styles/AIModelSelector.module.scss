.aiSelector_full {
    display: flex;
    align-items: center;
    margin-left: 1.5rem;
}

.aiSelector_label {
    font-size: 15px;
    color: rgba(255, 255, 255, 0.7);
    margin-right: 0.5rem;
}

.aiSelector_buttonContainer {
    position: relative;
}

.aiSelector_button {
    display: flex;
    align-items: center;
}

.aiSelector_buttonCompact {
    padding: 0.375rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
    position: relative;
    background-color: transparent;
}

.aiSelector_buttonFull {
    height: 38px;
    padding: 0 1rem;
    background-color: #1A243B;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
    transition: colors 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.aiSelector_buttonFull:hover {
    background-color: #1E2A45;
}

.aiSelector_iconWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    flex-shrink: 0;
}

.aiSelector_iconCompact {
    width: 1rem;
    height: 1rem;
}

.aiSelector_iconFull {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

.aiSelector_icon {
    position: absolute;
    top: 48%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.1rem;
    line-height: 1;
}

.aiSelector_iconCompactDefault {
    color: rgba(255, 255, 255, 0.5);
    transition: colors 0.2s;
}

.aiSelector_iconCompactHover:hover {
    color: rgba(255, 255, 255, 0.7);
}

.aiSelector_name {
    font-size: 0.875rem;
    color: white;
    white-space: nowrap;
}

.aiSelector_arrow {
    color: rgba(255, 255, 255, 0.5);
    margin-left: 0.375rem;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.aiSelector_dropdown {
    position: absolute;
    z-index: 50;
    width: 420px;
    background-image: linear-gradient(to bottom, #131B2E, #0A1020);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.aiSelector_dropdownBelow {
    left: 0;
    margin-top: 0.5rem;
}

.aiSelector_dropdownAbove {
    left: 0;
    bottom: calc(100% + 0.5rem);
}

.aiSelector_scrollArea {
    padding: 1rem;
    max-height: 250px;
    overflow-y: auto;
}

.aiSelector_optionCard {
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 0.375rem;
    position: relative;
    border: 1px solid rgba(31, 45, 80, 0.4);
    background-color: #131B2E;
}

.aiSelector_optionCard:last-child {
    margin-bottom: 0;
}

.aiSelector_optionCard:hover {
    background-color: rgba(26, 36, 59, 0.7);
}

.aiSelector_optionSelected {
    background-color: #1A243B;
    border-color: #1F2D50;
    border-left-width: 3px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.aiSelector_optionDefault {
    border-left-width: 1px;
}

.aiSelector_optionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.aiSelector_modelIcon {
    flex-shrink: 0;
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 9999px;
    margin-right: 0.75rem;
    position: relative;
}

.aiSelector_modelIconInner {
    position: absolute;
    top: 48%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.4rem;
    line-height: 1;
}

.aiSelector_modelInfo {
    flex-grow: 1;
    min-width: 0;
    /* Ensure text truncation works */
}

.aiSelector_modelHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.aiSelector_modelName {
    color: white;
    font-size: 16px;
    font-weight: 500;
}

.aiSelector_activeTag {
    font-size: 0.75rem;
    padding: 0 0.5rem;
    border-radius: 9999px;
}

.aiSelector_modelDesc {
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
    margin-top: 0.125rem;
}

.aiSelector_featuresGrid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
}

.aiSelector_sectionTitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.aiSelector_featureList {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.aiSelector_featureItem {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
}

.aiSelector_featureDot {
    margin-right: 0.5rem;
    width: 0.375rem;
    height: 0.375rem;
    border-radius: 9999px;
    flex-shrink: 0;
}

.aiSelector_statsContainer {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.aiSelector_statItem {
    display: flex;
    align-items: center;
    font-size: 13px;
}

.aiSelector_statLabel {
    color: rgba(255, 255, 255, 0.5);
    margin-right: 0.5rem;
}

.aiSelector_flexRow {
    display: flex;
    align-items: center;
}