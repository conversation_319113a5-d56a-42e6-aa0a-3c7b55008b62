import React, { useState, useEffect, useRef } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { AI_MODELS, AI_MODEL_STORAGE_KEY } from '~/utils/constants';
import styles from '~/components/styles/AIModelSelector.module.scss';

const safeGetItem = (key: string, defaultValue: string): string => {
  try {
    const value = localStorage.getItem(key);
    return value !== null ? value : defaultValue;
  } catch (error) {
    console.warn(`Error reading from localStorage: ${error}`);
    return defaultValue;
  }
};

const safeSetItem = (key: string, value: string): void => {
  try {
    localStorage.setItem(key, value);
  } catch (error) {
    console.warn(`Error writing to localStorage: ${error}`);
  }
};

interface AIModelSelectorProps {
  isCompact?: boolean;
}

const AIModelSelector: React.FC<AIModelSelectorProps> = ({ isCompact = false }) => {
  const { t } = useTranslation('translation');
  const { t: c } = useTranslation('constants');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedModelId, setSelectedModelId] = useState(AI_MODELS[0].value);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const selectedOptionRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [buttonWidth, setButtonWidth] = useState<number | undefined>(undefined);

  useEffect(() => {
    const handleModelChangeEvent = (event: CustomEvent<{ modelValue: string; source?: string }>) => {
      if (event.detail && event.detail.modelValue && event.detail.source !== 'AIModelSelector') {
        selectModel(event.detail.modelValue);
      }
    };

    window.addEventListener('biela:changeAIModel', handleModelChangeEvent as EventListener);

    return () => {
      window.removeEventListener('biela:changeAIModel', handleModelChangeEvent as EventListener);
    };
  }, []);

  useEffect(() => {
    const savedModelId = safeGetItem(AI_MODEL_STORAGE_KEY, AI_MODELS[0].value);
    setSelectedModelId(savedModelId);
  }, []);

  useEffect(() => {
    safeSetItem(AI_MODEL_STORAGE_KEY, selectedModelId);
  }, [selectedModelId]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && selectedOptionRef.current && scrollContainerRef.current) {
      requestAnimationFrame(() => {
        if (selectedOptionRef.current && scrollContainerRef.current) {
          scrollContainerRef.current.scrollTop =
            selectedOptionRef.current.offsetTop - scrollContainerRef.current.offsetTop - 16;
        }
      });
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isCompact && buttonRef.current) {
      setButtonWidth(buttonRef.current.offsetWidth);
    }
  }, [selectedModelId, isCompact]);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const selectModel = (modelValue: string) => {
    setSelectedModelId(modelValue);
    setIsOpen(false);

    const event = new CustomEvent('biela:changeAIModel', {
      detail: { modelValue, source: 'AIModelSelector' },
    });
    window.dispatchEvent(event);
  };

  const currentModel = AI_MODELS.find((model) => model.value === selectedModelId) || AI_MODELS[0];
  const animationProps = isCompact
    ? {
        initial: { opacity: 0, y: -5 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -5 },
      }
    : {
        initial: { opacity: 0, y: 5 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: 5 },
      };

  return (
    <div className={isCompact ? '' : styles.aiSelector_full} ref={dropdownRef}>
      {!isCompact && <div className={styles.aiSelector_label}>{t('AIModel', 'AI Model')}:</div>}

      <div className={styles.aiSelector_buttonContainer}>
        <button
          ref={buttonRef}
          onClick={toggleDropdown}
          className={
            isCompact
              ? 'action-button p-1.5 rounded-md transition-all duration-200 relative group bg-transparent'
              : styles.aiSelector_buttonFull
          }
          style={!isCompact && buttonWidth ? { width: `${buttonWidth}px` } : undefined}
        >
          {isCompact ? (
            <>
              <div className="relative w-4 h-4 flex items-center justify-center">
                <span
                  style={{
                    color: currentModel.color,
                    fontSize: '1.1rem',
                  }}
                  className="text-white/50 group-hover:text-white/70 transition-colors"
                >
                  {currentModel.icon}
                </span>
              </div>
              <span className="tooltip">{t('AIModel', 'AI Model')}</span>
            </>
          ) : (
            <>
              <div className={styles.aiSelector_flexRow} style={{ flexGrow: 1 }}>
                <div className={styles.aiSelector_iconWrapper + ' ' + styles.aiSelector_iconFull}>
                  <span className={styles.aiSelector_icon} style={{ color: currentModel.color }}>
                    {currentModel.icon}
                  </span>
                </div>

                <span className={styles.aiSelector_name}>{currentModel.name}</span>
              </div>

              <motion.span
                animate={{ rotate: isOpen ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className={styles.aiSelector_arrow}
              >
                ▼
              </motion.span>
            </>
          )}
        </button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              {...animationProps}
              transition={{ duration: 0.15 }}
              className={`${styles.aiSelector_dropdown} ${isCompact ? styles.aiSelector_dropdownAbove : styles.aiSelector_dropdownBelow}`}
            >
              <div ref={scrollContainerRef} className={styles.aiSelector_scrollArea}>
                {AI_MODELS.map((model) => (
                  <div
                    key={model.id}
                    ref={selectedModelId === model.value ? selectedOptionRef : null}
                    onClick={() => selectModel(model.value)}
                    className={`${styles.aiSelector_optionCard} ${
                      selectedModelId === model.value
                        ? styles.aiSelector_optionSelected
                        : styles.aiSelector_optionDefault
                    }`}
                    style={{
                      borderLeftColor: model.color,
                    }}
                  >
                    <div className={styles.aiSelector_optionHeader}>
                      <div className={styles.aiSelector_modelIcon} style={{ backgroundColor: `${model.color}20` }}>
                        <span className={styles.aiSelector_modelIconInner} style={{ color: model.color }}>
                          {model.icon}
                        </span>
                      </div>

                      <div className={styles.aiSelector_modelInfo}>
                        <div className={styles.aiSelector_modelHeader}>
                          <h4 className={styles.aiSelector_modelName}>{model.name}</h4>
                          {selectedModelId === model.value && (
                            <span
                              className={styles.aiSelector_activeTag}
                              style={{
                                backgroundColor: `${model.color}30`,
                                color: model.color,
                              }}
                            >
                              {t('Active', 'Active')}
                            </span>
                          )}
                        </div>
                        <p className={styles.aiSelector_modelDesc}>
                          {c(model.description.key, model.description.defaultValue)}
                        </p>
                      </div>
                    </div>

                    <div className={styles.aiSelector_featuresGrid}>
                      <div>
                        <h5 className={styles.aiSelector_sectionTitle}>{t('projectInfo.features', 'Features')}</h5>
                        <ul className={styles.aiSelector_featureList}>
                          {model.features.map((feature, idx) => (
                            <li key={idx} className={styles.aiSelector_featureItem}>
                              <span
                                className={styles.aiSelector_featureDot}
                                style={{ backgroundColor: model.color }}
                              ></span>
                              {c(feature.key, feature.defaultValue)}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className={styles.aiSelector_sectionTitle}>{t('Stats', 'Stats')}</h5>
                        <div className={styles.aiSelector_statsContainer}>
                          <div className={styles.aiSelector_statItem}>
                            <span className={styles.aiSelector_statLabel}>{t('Performance', 'Performance')}:</span>
                            <span className={styles.aiSelector_statValue} style={{ color: model.color }}>
                              {c(model.performance.key, model.performance.defaultValue)}
                            </span>
                          </div>
                          <div className={styles.aiSelector_statItem}>
                            <span className={styles.aiSelector_statLabel}>{t('Cost', 'Cost')}:</span>
                            <span className={styles.aiSelector_statValue} style={{ color: model.color }}>
                              {c(model.cost.key, model.cost.defaultValue)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AIModelSelector;
