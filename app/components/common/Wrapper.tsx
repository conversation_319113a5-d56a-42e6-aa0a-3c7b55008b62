import React, { ReactNode, UIEvent, useEffect, useState } from 'react';
import { Header } from '~/components/common/Header/Header';
import CopyRight from '~/components/common/Footer/copyRight';
import { ClientOnly } from 'remix-utils/client-only';
import '~/components/styles/index.scss?url';

interface WrapperProps {
  children: ReactNode;
  handleSendMessage?: (event: UIEvent, messageInput?: string) => void;
  isStreaming?: boolean;
  isDashboardPage?: boolean;
  isSettingsPage?: boolean;
  isProfilePage?: boolean;
  handleStop?: () => void;
  footerOff?: boolean;
}
const Wrapper: React.FC<WrapperProps> = ({
  children,
  handleSendMessage,
  isStreaming,
  isDashboardPage,
  isSettingsPage,
  isProfilePage,
  handleStop,
  footerOff,
}) => {

  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {

    const handleScroll = () => {
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      setIsScrolled(scrollTop > 0);
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);
  return (
    <div className={`flex flex-col min-h-screen ${footerOff ? 'overflow-hidden max-h-[100vh]' : ''}`}>
      <ClientOnly>
        {() => (
          <div
            className={`fixed xl:h-20 top-0 left-0 right-0 z-50 transition-all duration-300 bg-[#0A1730] shadow-md
      ${isScrolled ? 'blur-bg' : ''}`}>
            <Header
              handleSendMessage={handleSendMessage}
              isStreaming={isStreaming}
              isDashboardPage={isDashboardPage}
              isProfilePage={isProfilePage}
              isSettingsPage={isSettingsPage}
              handleStop={handleStop}
            />
          </div>
        )}
      </ClientOnly>

      <main className="flex-1">{children}</main>
      {!footerOff && <CopyRight />}
    </div>
  );
};

export default Wrapper;
