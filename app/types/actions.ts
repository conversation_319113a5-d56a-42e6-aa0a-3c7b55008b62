export type ActionType = 'file' | 'shell' | 'unit_testing_shell' | 'migration';

export interface BaseAction {
  content: string;
}

export interface FileAction extends BaseAction {
  type: 'file';
  filePath: string;
}

export interface ShellAction extends BaseAction {
  type: 'shell';
}

export interface UnitTestingShellAction extends BaseAction {
  type: 'unit_testing_shell';
}

export interface StartAction extends BaseAction {
  type: 'start';
}

export interface MigrationAction extends BaseAction {
  type: 'migration';
  path: string;
  migrationTitle?: string;
}

export type bielaAction = FileAction | ShellAction | UnitTestingShellAction | StartAction | MigrationAction;



export type bielaActionData = bielaAction | BaseAction;

export interface ActionAlert {
  type: string;
  title: string;
  description: string;
  content: string;
  source?: 'terminal' | 'preview' | 'migration'; // Add source to differentiate between terminal and preview errors
}
