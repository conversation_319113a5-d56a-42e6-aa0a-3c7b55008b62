{"name": "biela.dev", "description": "", "private": true, "license": "", "sideEffects": false, "type": "module", "version": "0.0.5", "scripts": {"deploy": "npm run build && wrangler pages deploy", "build": "remix vite:build", "dev": "node pre-start.cjs && remix vite:dev --host", "test": "vitest --run", "test:watch": "vitest", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint app", "lint:fix": "npm run lint -- --fix && prettier app --write", "start:windows": "wrangler pages dev ./build/client", "start:unix": "bindings=$(./bindings.sh) && wrangler pages dev ./build/client $bindings", "start": "node -e \"const { spawn } = require('child_process'); const isWindows = process.platform === 'win32'; const cmd = isWindows ? 'npm run start:windows' : 'npm run start:unix'; const child = spawn(cmd, { shell: true, stdio: 'inherit' }); child.on('exit', code => process.exit(code));\"", "dockerstart": "bindings=$(./bindings.sh) && wrangler pages dev ./build/client $bindings --ip 0.0.0.0 --port 5173 --no-show-interactive-dev-session", "dockerrun": "docker run -it -d --name biela-ai-live -p 5173:5173 --env-file .env.local biela-ai", "dockerbuild:prod": "docker build -t biela-ai:production -t biela-ai:latest --target biela-ai-production .", "dockerbuild": "docker build -t biela-ai:development -t biela-ai:latest --target biela-ai-development .", "typecheck": "tsc", "typegen": "wrangler types", "preview": "pnpm run build && pnpm run start", "prepare": "husky"}, "engines": {"node": ">=18.18.0"}, "dependencies": {"@codemirror/autocomplete": "^6.18.4", "@codemirror/commands": "^6.8.0", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-python": "^6.1.7", "@codemirror/lang-sass": "^6.0.2", "@codemirror/lang-vue": "^0.1.3", "@codemirror/lang-wast": "^6.0.2", "@codemirror/language": "^6.10.8", "@codemirror/search": "^6.5.8", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@iconify-json/ph": "^1.2.2", "@iconify-json/svg-spinners": "^1.2.2", "@lezer/highlight": "^1.2.1", "@mui/material": "^6.4.3", "@nanostores/react": "^0.7.3", "@octokit/rest": "^21.1.0", "@octokit/types": "^13.8.0", "@radix-ui/react-context-menu": "^2.2.5", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.7", "@react-pdf/renderer": "^4.3.0", "@remix-run/cloudflare": "^2.15.3", "@remix-run/cloudflare-pages": "^2.15.3", "@remix-run/react": "^2.15.3", "@tanstack/react-query": "^5.66.0", "@types/lodash": "^4.17.15", "@uiw/codemirror-theme-vscode": "^4.23.8", "@unocss/reset": "^0.61.9", "@webcontainer/api": "1.5.3-internal.2", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "ai": "4.1.6", "autoprefixer": "^10.4.21", "chalk": "^5.4.1", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "dat.gui": "^0.7.9", "date-fns": "^3.6.0", "diff": "^5.2.0", "dotenv": "^16.4.7", "file-saver": "^2.0.5", "framer-motion": "^11.18.2", "googleapis": "^144.0.0", "i18n": "^0.15.1", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "ignore": "^6.0.2", "isbot": "^4.4.0", "isomorphic-git": "^1.29.0", "istextorbinary": "^9.5.0", "jose": "^5.9.6", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "nanostores": "^0.10.3", "postcss": "^8.5.3", "react": "^18.3.1", "react-audio-visualize": "^1.2.0", "react-audio-voice-recorder": "^2.2.0", "react-circular-progressbar": "^2.2.0", "react-cropper": "^2.3.3", "react-dom": "^18.3.1", "react-hotkeys-hook": "^4.6.1", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-markdown": "^9.0.3", "react-phone-number-input": "^3.4.12", "react-qr-code": "^2.0.15", "react-resizable-panels": "^2.1.7", "react-router-dom": "6", "react-scroll-to-bottom": "^4.2.0", "react-select": "^5.10.0", "react-toastify": "^10.0.6", "react-tooltip": "^5.28.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "remix-island": "^0.2.0", "remix-utils": "^7.7.0", "shiki": "^1.29.2", "unist-util-visit": "^5.0.0", "viem": "^2.22.21", "zustand": "^5.0.3"}, "devDependencies": {"@blitz/eslint-plugin": "0.1.0", "@cloudflare/workers-types": "^4.20250204.0", "@remix-run/dev": "^2.15.3", "@types/dat.gui": "^0.7.13", "@types/diff": "^5.2.3", "@types/dom-speech-recognition": "^0.0.4", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-scroll-to-bottom": "^4.2.5", "fast-glob": "^3.3.3", "husky": "9.1.7", "is-ci": "^3.0.1", "node-fetch": "^3.3.2", "pnpm": "^9.15.5", "prettier": "^3.4.2", "sass-embedded": "^1.83.4", "tailwindcss": "^4.1.3", "typescript": "^5.7.3", "unified": "^11.0.5", "unocss": "^0.61.9", "vite": "^5.4.14", "vite-plugin-node-polyfills": "^0.22.0", "vite-plugin-optimize-css-modules": "^1.2.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.9", "wrangler": "^3.107.3", "zod": "^3.24.1"}, "resolutions": {"@typescript-eslint/utils": "^8.0.0-alpha.30"}, "packageManager": "pnpm@9.4.0"}